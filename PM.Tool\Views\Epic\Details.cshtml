@model EpicDetailsViewModel
@using PM.Tool.Core.Entities.Agile
@using PM.Tool.Core.Extensions
@{
    ViewData["Title"] = $"Epic: {Model.Epic.Title}";
    ViewData["Breadcrumbs"] = new List<object>
    {
        new { Text = "Home", Href = "/", Icon = "fas fa-home" },
        new { Text = "Agile", Href = "/Agile", Icon = "fas fa-tasks" },
        new { Text = "Epics", Href = $"/Epic?projectId={Model.Epic.ProjectId}", Icon = "fas fa-mountain" },
        new { Text = Model.Epic.EpicKey, Href = (string?)null, Icon = "fas fa-mountain" }
    };
}

<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-start mb-4">
        <div class="flex-grow-1">
            <div class="d-flex align-items-center mb-2">
                <h1 class="h3 mb-0 text-gray-800 me-3">
                    <i class="fas fa-mountain text-primary me-2"></i>
                    @Model.Epic.EpicKey
                </h1>
                <span class="badge bg-@GetStatusColor(Model.Epic.Status) fs-6">@Model.Epic.Status</span>
                <span class="badge bg-@GetPriorityColor(Model.Epic.Priority) fs-6 ms-2">@Model.Epic.Priority</span>
                @if (Model.Epic.IsOverdue)
                {
                    <span class="badge bg-danger fs-6 ms-2">Overdue</span>
                }
            </div>
            <h2 class="h4 text-gray-700 mb-2">@Model.Epic.Title</h2>
            <p class="text-muted mb-0">@Model.Epic.Description</p>
        </div>
        
        <div class="d-flex gap-2">
            <a href="@Url.Action("Edit", new { id = Model.Epic.Id })" class="btn btn-outline-primary">
                <i class="fas fa-edit me-2"></i>
                Edit Epic
            </a>
            <a href="@Url.Action("Index", new { projectId = Model.Epic.ProjectId })" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Epics
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Progress Overview -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-success me-2"></i>
                        Progress Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Overall Progress -->
                        <div class="col-md-6 mb-4">
                            <div class="progress-section">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">Overall Progress</h6>
                                    <span class="text-muted">@Model.Epic.ProgressPercentage.ToString("F0")%</span>
                                </div>
                                <div class="progress mb-2" style="height: 10px;">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: @Model.Epic.ProgressPercentage%"
                                         aria-valuenow="@Model.Epic.ProgressPercentage" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100"></div>
                                </div>
                                <small class="text-muted">
                                    @Model.ProgressMetrics.ActualStoryPoints of @Model.ProgressMetrics.EstimatedStoryPoints story points completed
                                </small>
                            </div>
                        </div>
                        
                        <!-- Feature Progress -->
                        <div class="col-md-6 mb-4">
                            <div class="progress-section">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">Features</h6>
                                    <span class="text-muted">@Model.ProgressMetrics.CompletedFeatures / @Model.ProgressMetrics.TotalFeatures</span>
                                </div>
                                <div class="progress mb-2" style="height: 10px;">
                                    <div class="progress-bar bg-info" role="progressbar" 
                                         style="width: @Model.ProgressMetrics.FeatureProgress%"
                                         aria-valuenow="@Model.ProgressMetrics.FeatureProgress" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100"></div>
                                </div>
                                <small class="text-muted">
                                    @Model.ProgressMetrics.FeatureProgress.ToString("F0")% of features completed
                                </small>
                            </div>
                        </div>
                        
                        <!-- User Story Progress -->
                        <div class="col-md-6 mb-4">
                            <div class="progress-section">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">User Stories</h6>
                                    <span class="text-muted">@Model.ProgressMetrics.CompletedUserStories / @Model.ProgressMetrics.TotalUserStories</span>
                                </div>
                                <div class="progress mb-2" style="height: 10px;">
                                    <div class="progress-bar bg-warning" role="progressbar" 
                                         style="width: @Model.ProgressMetrics.UserStoryProgress%"
                                         aria-valuenow="@Model.ProgressMetrics.UserStoryProgress" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100"></div>
                                </div>
                                <small class="text-muted">
                                    @Model.ProgressMetrics.UserStoryProgress.ToString("F0")% of user stories completed
                                </small>
                            </div>
                        </div>
                        
                        <!-- Task Progress -->
                        <div class="col-md-6 mb-4">
                            <div class="progress-section">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">Tasks</h6>
                                    <span class="text-muted">@Model.ProgressMetrics.CompletedTasks / @Model.ProgressMetrics.TotalTasks</span>
                                </div>
                                <div class="progress mb-2" style="height: 10px;">
                                    <div class="progress-bar bg-primary" role="progressbar" 
                                         style="width: @Model.ProgressMetrics.TaskProgress%"
                                         aria-valuenow="@Model.ProgressMetrics.TaskProgress" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100"></div>
                                </div>
                                <small class="text-muted">
                                    @Model.ProgressMetrics.TaskProgress.ToString("F0")% of tasks completed
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-puzzle-piece text-info me-2"></i>
                            Features (@Model.Features.Count)
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" id="addFeature">
                            <i class="fas fa-plus me-2"></i>
                            Add Feature
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if (!Model.Features.Any())
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-puzzle-piece text-muted mb-3" style="font-size: 2rem;"></i>
                            <h6 class="text-muted mb-3">No Features Yet</h6>
                            <p class="text-muted mb-3">Break down this epic into features to better organize your work.</p>
                            <button class="btn btn-primary btn-sm" id="addFirstFeature">
                                <i class="fas fa-plus me-2"></i>
                                Add First Feature
                            </button>
                        </div>
                    }
                    else
                    {
                        <div class="feature-list">
                            @foreach (var feature in Model.Features)
                            {
                                <div class="feature-item" data-feature-id="@feature.Id">
                                    <div class="d-flex align-items-start">
                                        <div class="feature-status-indicator">
                                            <i class="fas fa-circle text-@GetFeatureStatusColor(feature.Status)"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <a href="#" class="text-decoration-none">@feature.FeatureKey - @feature.Title</a>
                                                    </h6>
                                                    <p class="text-muted mb-2 small">@feature.Description.Truncate(150)</p>
                                                    <div class="feature-meta">
                                                        <span class="badge bg-@GetFeatureStatusColor(feature.Status) me-2">@feature.Status</span>
                                                        <span class="badge bg-@GetFeaturePriorityColor(feature.Priority)">@feature.Priority</span>
                                                        @if (feature.EstimatedStoryPoints > 0)
                                                        {
                                                            <span class="text-muted ms-2 small">
                                                                <i class="fas fa-chart-bar me-1"></i>
                                                                @feature.EstimatedStoryPoints pts
                                                            </span>
                                                        }
                                                    </div>
                                                </div>
                                                <div class="feature-actions">
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" onclick="editFeature(@feature.Id)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteFeature(@feature.Id)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            @if (feature.UserStories.Any())
                                            {
                                                <div class="feature-progress mt-2">
                                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                                        <small class="text-muted">Progress</small>
                                                        <small class="text-muted">@feature.ProgressPercentage.ToString("F0")%</small>
                                                    </div>
                                                    <div class="progress" style="height: 4px;">
                                                        <div class="progress-bar" role="progressbar" 
                                                             style="width: @feature.ProgressPercentage%"
                                                             aria-valuenow="@feature.ProgressPercentage" 
                                                             aria-valuemin="0" 
                                                             aria-valuemax="100"></div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>

            <!-- User Stories -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-book text-warning me-2"></i>
                            User Stories (@Model.UserStories.Count)
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" id="addUserStory">
                            <i class="fas fa-plus me-2"></i>
                            Add User Story
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if (!Model.UserStories.Any())
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-book text-muted mb-3" style="font-size: 2rem;"></i>
                            <h6 class="text-muted mb-3">No User Stories Yet</h6>
                            <p class="text-muted mb-3">Add user stories to define the requirements for this epic.</p>
                            <button class="btn btn-primary btn-sm" id="addFirstUserStory">
                                <i class="fas fa-plus me-2"></i>
                                Add First User Story
                            </button>
                        </div>
                    }
                    else
                    {
                        <div class="user-story-list">
                            @foreach (var story in Model.UserStories.Take(10))
                            {
                                <div class="user-story-item" data-story-id="@story.Id">
                                    <div class="d-flex align-items-start">
                                        <div class="story-status-indicator">
                                            <i class="fas fa-circle text-@GetUserStoryStatusColor(story.Status)"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <a href="#" class="text-decoration-none">@story.StoryKey - @story.Title</a>
                                                    </h6>
                                                    <p class="text-muted mb-2 small">@story.UserStoryFormat</p>
                                                    <div class="story-meta">
                                                        <span class="badge bg-@GetUserStoryStatusColor(story.Status) me-2">@story.Status</span>
                                                        <span class="badge bg-@GetUserStoryPriorityColor(story.Priority)">@story.Priority</span>
                                                        @if (story.StoryPoints > 0)
                                                        {
                                                            <span class="text-muted ms-2 small">
                                                                <i class="fas fa-chart-bar me-1"></i>
                                                                @story.StoryPoints pts
                                                            </span>
                                                        }
                                                        @if (story.TaskCount > 0)
                                                        {
                                                            <span class="text-muted ms-2 small">
                                                                <i class="fas fa-tasks me-1"></i>
                                                                @story.CompletedTaskCount/@story.TaskCount tasks
                                                            </span>
                                                        }
                                                    </div>
                                                </div>
                                                <div class="story-actions">
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" onclick="editUserStory(@story.Id)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteUserStory(@story.Id)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                            
                            @if (Model.UserStories.Count > 10)
                            {
                                <div class="text-center mt-3">
                                    <button class="btn btn-outline-secondary btn-sm" id="loadMoreStories">
                                        <i class="fas fa-chevron-down me-2"></i>
                                        Load More (@(Model.UserStories.Count - 10) remaining)
                                    </button>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Epic Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        Epic Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="epic-info">
                        <div class="info-item">
                            <label class="info-label">Epic Key</label>
                            <div class="info-value">@Model.Epic.EpicKey</div>
                        </div>
                        
                        <div class="info-item">
                            <label class="info-label">Project</label>
                            <div class="info-value">@Model.Epic.Project.Name</div>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(Model.Epic.OwnerId))
                        {
                            <div class="info-item">
                                <label class="info-label">Owner</label>
                                <div class="info-value">@Model.Epic.Owner?.UserName</div>
                            </div>
                        }
                        
                        @if (Model.Epic.TargetDate.HasValue)
                        {
                            <div class="info-item">
                                <label class="info-label">Target Date</label>
                                <div class="info-value">@Model.Epic.TargetDate.Value.ToString("MMMM dd, yyyy")</div>
                            </div>
                        }
                        
                        <div class="info-item">
                            <label class="info-label">Created</label>
                            <div class="info-value">@Model.Epic.CreatedAt.ToString("MMMM dd, yyyy")</div>
                        </div>
                        
                        @if (Model.Epic.CompletedDate.HasValue)
                        {
                            <div class="info-item">
                                <label class="info-label">Completed</label>
                                <div class="info-value">@Model.Epic.CompletedDate.Value.ToString("MMMM dd, yyyy")</div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.Epic.BusinessValue))
                        {
                            <div class="info-item">
                                <label class="info-label">Business Value</label>
                                <div class="info-value">@Model.Epic.BusinessValue</div>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.Epic.AcceptanceCriteria))
                        {
                            <div class="info-item">
                                <label class="info-label">Acceptance Criteria</label>
                                <div class="info-value">@Model.Epic.AcceptanceCriteria</div>
                            </div>
                        }
                    </div>
                </div>
            </div>
            
            <!-- Timeline -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history text-info me-2"></i>
                        Timeline
                    </h6>
                </div>
                <div class="card-body">
                    @if (Model.Timeline.Any())
                    {
                        <div class="timeline">
                            @foreach (var timelineEvent in Model.Timeline.Take(10))
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker">
                                        <i class="@timelineEvent.Icon <EMAIL>"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-title">@timelineEvent.Type</div>
                                        <div class="timeline-description">@timelineEvent.Description</div>
                                        <div class="timeline-date">@timelineEvent.Date.ToString("MMM dd, yyyy")</div>
                                    </div>
                                </div>
                            }
                        </div>
                        
                        @if (Model.Timeline.Count > 10)
                        {
                            <div class="text-center mt-3">
                                <button class="btn btn-outline-secondary btn-sm" id="loadMoreTimeline">
                                    <i class="fas fa-chevron-down me-2"></i>
                                    Load More
                                </button>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-3">
                            <i class="fas fa-history text-muted mb-2" style="font-size: 2rem;"></i>
                            <p class="text-muted mb-0">No timeline events yet</p>
                        </div>
                    }
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" id="createFeature">
                            <i class="fas fa-puzzle-piece me-2"></i>
                            Create Feature
                        </button>
                        <button class="btn btn-outline-success btn-sm" id="createUserStory">
                            <i class="fas fa-book me-2"></i>
                            Create User Story
                        </button>
                        <button class="btn btn-outline-info btn-sm" id="viewKanban">
                            <i class="fas fa-columns me-2"></i>
                            View in Kanban
                        </button>
                        <button class="btn btn-outline-warning btn-sm" id="generateReport">
                            <i class="fas fa-chart-bar me-2"></i>
                            Generate Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Quick action handlers
            $('#addFeature, #addFirstFeature, #createFeature').on('click', function() {
                // Navigate to feature creation
                alert('Feature creation coming soon!');
            });
            
            $('#addUserStory, #addFirstUserStory, #createUserStory').on('click', function() {
                // Navigate to user story creation
                alert('User story creation coming soon!');
            });
            
            $('#viewKanban').on('click', function() {
                window.location.href = '@Url.Action("Index", "Agile", new { projectId = Model.Epic.ProjectId })';
            });
            
            $('#generateReport').on('click', function() {
                alert('Epic report generation coming soon!');
            });
            
            // Load more functionality
            $('#loadMoreStories').on('click', function() {
                // Load more user stories
                alert('Load more functionality coming soon!');
            });
            
            $('#loadMoreTimeline').on('click', function() {
                // Load more timeline events
                alert('Load more timeline functionality coming soon!');
            });
        });
        
        function editFeature(featureId) {
            alert('Feature editing coming soon!');
        }
        
        function deleteFeature(featureId) {
            if (confirm('Are you sure you want to delete this feature?')) {
                alert('Feature deletion coming soon!');
            }
        }
        
        function editUserStory(storyId) {
            alert('User story editing coming soon!');
        }
        
        function deleteUserStory(storyId) {
            if (confirm('Are you sure you want to delete this user story?')) {
                alert('User story deletion coming soon!');
            }
        }
    </script>
}

@functions {
    private string GetStatusColor(EpicStatus status)
    {
        return status switch
        {
            EpicStatus.Draft => "secondary",
            EpicStatus.Ready => "info",
            EpicStatus.InProgress => "warning",
            EpicStatus.Done => "success",
            EpicStatus.Cancelled => "danger",
            _ => "secondary"
        };
    }
    
    private string GetPriorityColor(EpicPriority priority)
    {
        return priority switch
        {
            EpicPriority.Critical => "danger",
            EpicPriority.High => "warning",
            EpicPriority.Medium => "info",
            EpicPriority.Low => "secondary",
            _ => "secondary"
        };
    }
    
    private string GetFeatureStatusColor(FeatureStatus status)
    {
        return status switch
        {
            FeatureStatus.Draft => "secondary",
            FeatureStatus.Ready => "info",
            FeatureStatus.InProgress => "warning",
            FeatureStatus.Done => "success",
            FeatureStatus.Cancelled => "danger",
            _ => "secondary"
        };
    }
    
    private string GetFeaturePriorityColor(FeaturePriority priority)
    {
        return priority switch
        {
            FeaturePriority.Critical => "danger",
            FeaturePriority.High => "warning",
            FeaturePriority.Medium => "info",
            FeaturePriority.Low => "secondary",
            _ => "secondary"
        };
    }
    
    private string GetUserStoryStatusColor(UserStoryStatus status)
    {
        return status switch
        {
            UserStoryStatus.New => "secondary",
            UserStoryStatus.Backlog => "info",
            UserStoryStatus.Ready => "primary",
            UserStoryStatus.InProgress => "warning",
            UserStoryStatus.Review => "info",
            UserStoryStatus.Done => "success",
            UserStoryStatus.Cancelled => "danger",
            _ => "secondary"
        };
    }
    
    private string GetUserStoryPriorityColor(UserStoryPriority priority)
    {
        return priority switch
        {
            UserStoryPriority.Critical => "danger",
            UserStoryPriority.High => "warning",
            UserStoryPriority.Medium => "info",
            UserStoryPriority.Low => "secondary",
            _ => "secondary"
        };
    }
}
