@{
    var title = ViewData["Title"]?.ToString() ?? "";
    var icon = ViewData["Icon"]?.ToString() ?? "";
    var actions = ViewData["Actions"] as IEnumerable<object> ?? new List<object>();
}

<div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 rounded-lg p-6">
    @if (!string.IsNullOrEmpty(title))
    {
        <div class="flex items-center mb-4">
            @if (!string.IsNullOrEmpty(icon))
            {
                <i class="@icon text-neutral-600 dark:text-neutral-400 mr-2"></i>
            }
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100">@title</h3>
        </div>
    }

    <div class="space-y-3">
        @foreach (dynamic action in actions)
        {
            @if (!string.IsNullOrEmpty(action.FormAction))
            {
                <form asp-action="@action.FormAction" method="post" class="delete-form">
                    @Html.AntiForgeryToken()
                    @{
                        ViewData["Text"] = action.Text;
                        ViewData["Variant"] = action.Variant;
                        ViewData["Icon"] = action.Icon;
                        ViewData["Type"] = action.Type ?? "button";
                        ViewData["Classes"] = "w-full justify-center";
                        ViewData["OnClick"] = !string.IsNullOrEmpty(action.ConfirmMessage) ? $"return confirm('{action.ConfirmMessage}')" : "";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </form>
            }
            else
            {
                ViewData["Text"] = action.Text;
                ViewData["Variant"] = action.Variant;
                ViewData["Icon"] = action.Icon;
                ViewData["Href"] = action.Href;
                ViewData["Classes"] = "w-full justify-center";
                <partial name="Components/_Button" view-data="ViewData" />
            }
        }
    </div>
</div>
