@using PM.Tool.Core.Enums
@{
    ViewData["Title"] = "Project Analytics";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
}

@if (project == null)
{
    <div class="alert-danger-custom">
        <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400"></i>
        <div>
            <p class="font-medium">Project Not Found</p>
            <p class="text-sm">The requested project could not be found or you don't have access to it.</p>
        </div>
    </div>
    return;
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-chart-line mr-3 text-primary-600 dark:text-primary-400"></i>
                @project.Name Analytics
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Detailed insights and performance metrics for this project
            </p>
        </div>
        <div class="flex space-x-3 mt-4 sm:mt-0">
            @{
                ViewData["Text"] = "View Project";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-project-diagram";
                ViewData["Href"] = Url.Action("Details", "Projects", new { id = project.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />
            @{
                ViewData["Text"] = "Back to Analytics";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Project Overview Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <!-- Total Tasks -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-tasks text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@project.TotalTasks</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Total Tasks</p>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                <i class="fas fa-list mr-1"></i>
                All project tasks
            </div>
        </div>
    </div>

    <!-- Completed Tasks -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-success-500 to-success-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-check-circle text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@project.CompletedTasks</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Completed</p>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                <i class="fas fa-check mr-1"></i>
                Successfully finished
            </div>
        </div>
    </div>

    <!-- Remaining Tasks -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-warning-500 to-warning-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-clock text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@(project.TotalTasks - project.CompletedTasks)</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Remaining</p>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                <i class="fas fa-hourglass-half mr-1"></i>
                Still in progress
            </div>
        </div>
    </div>

    <!-- Progress Percentage -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-info-500 to-info-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-percentage text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@project.ProgressPercentage.ToString("F0")%</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Progress</p>
            </div>
        </div>
        <div class="mt-4">
            @{
                var progressClass = project.ProgressPercentage >= 80 ? "bg-success-500" :
                                  project.ProgressPercentage >= 60 ? "bg-info-500" :
                                  project.ProgressPercentage >= 30 ? "bg-warning-500" : "bg-danger-500";
            }
            <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                <div class="@progressClass h-2 rounded-full transition-all duration-300" style="width: @project.ProgressPercentage%"></div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
    <!-- Project Details -->
    <div class="xl:col-span-2 space-y-6">
        <!-- Project Information Card -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Project Information</h3>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Status</label>
                            <div>
                                @{
                                    var statusClass = project.Status == ProjectStatus.Active ? "success" :
                                                    project.Status == ProjectStatus.Completed ? "primary" :
                                                    project.Status == ProjectStatus.OnHold ? "warning" : "secondary";
                                }
                                <span class="badge-@statusClass">@project.Status</span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Start Date</label>
                            <div class="text-sm text-neutral-900 dark:text-dark-100">@project.StartDate.ToString("MMM dd, yyyy")</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">End Date</label>
                            <div class="text-sm text-neutral-900 dark:text-dark-100">@(project.EndDate?.ToString("MMM dd, yyyy") ?? "Not set")</div>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Budget</label>
                            <div class="text-sm text-neutral-900 dark:text-dark-100">@(project.Budget > 0 ? project.Budget.ToString("C") : "Not set")</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Client</label>
                            <div class="text-sm text-neutral-900 dark:text-dark-100">@(project.ClientName ?? "Not specified")</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Team Size</label>
                            <div class="text-sm text-neutral-900 dark:text-dark-100">@(project.Members?.Count ?? 0) members</div>
                        </div>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(project.Description))
                {
                    <div class="mt-6 pt-6 border-t border-neutral-200 dark:border-dark-600">
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Description</label>
                        <div class="text-sm text-neutral-600 dark:text-dark-400">@project.Description</div>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Progress Overview Card -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-pie text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Progress Overview</h3>
                </div>
            </div>
            <div class="card-body-custom text-center">
                <div class="relative inline-block mb-6">
                    <canvas id="progressChart" width="150" height="150"></canvas>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div>
                            <div class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@project.ProgressPercentage.ToString("F0")%</div>
                            <div class="text-xs text-neutral-500 dark:text-dark-400">Complete</div>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4 text-center">
                    <div class="border-r border-neutral-200 dark:border-dark-600">
                        <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@project.CompletedTasks</div>
                        <div class="text-xs text-success-600 dark:text-success-400">Completed</div>
                    </div>
                    <div>
                        <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@(project.TotalTasks - project.CompletedTasks)</div>
                        <div class="text-xs text-warning-600 dark:text-warning-400">Remaining</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="mt-8">
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-tools text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Quick Actions</h3>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                @{
                    ViewData["Text"] = "View Project Details";
                    ViewData["Variant"] = "outline-primary";
                    ViewData["Icon"] = "fas fa-eye";
                    ViewData["Href"] = Url.Action("Details", "Projects", new { id = project.Id });
                    ViewData["Class"] = "w-full";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "View Tasks";
                    ViewData["Variant"] = "outline-primary";
                    ViewData["Icon"] = "fas fa-tasks";
                    ViewData["Href"] = Url.Action("Index", "Tasks", new { projectId = project.Id });
                    ViewData["Class"] = "w-full";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Burndown Chart";
                    ViewData["Variant"] = "outline-primary";
                    ViewData["Icon"] = "fas fa-chart-line";
                    ViewData["Href"] = Url.Action("Burndown", new { projectId = project.Id });
                    ViewData["Class"] = "w-full";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                <button class="btn-outline-primary w-full" onclick="exportProjectData(@project.Id)">
                    <i class="fas fa-download mr-2"></i>
                    Export Data
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Create progress chart if Chart.js is available
            if (typeof Chart !== 'undefined') {
                try {
                    const ctx = document.getElementById('progressChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                data: [@project.ProgressPercentage, @(100 - project.ProgressPercentage)],
                                backgroundColor: ['#8b5cf6', '#e5e7eb'],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: false,
                            maintainAspectRatio: false,
                            cutout: '70%',
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                } catch (error) {
                    console.log('Chart initialization error:', error);
                }
            }

            // Add smooth animations to stats cards
            $('.stats-card-custom').each(function(index) {
                $(this).delay(index * 100).queue(function() {
                    $(this).addClass('fade-in').dequeue();
                });
            });
        });

        function exportProjectData(projectId) {
            // Create a form and submit it to trigger the download
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '@Url.Action("ExportData")';

            const projectIdInput = document.createElement('input');
            projectIdInput.type = 'hidden';
            projectIdInput.name = 'projectId';
            projectIdInput.value = projectId;

            const formatInput = document.createElement('input');
            formatInput.type = 'hidden';
            formatInput.name = 'format';
            formatInput.value = 'csv';

            form.appendChild(projectIdInput);
            form.appendChild(formatInput);
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }
    </script>
}
