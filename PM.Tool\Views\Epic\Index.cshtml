@model EpicManagementViewModel
@using PM.Tool.Core.Entities.Agile
@using PM.Tool.Core.Extensions
@{
    ViewData["Title"] = "Epic Management";
    ViewData["Breadcrumbs"] = new List<object>
    {
        new { Text = "Home", Href = "/", Icon = "fas fa-home" },
        new { Text = "Agile", Href = "/Agile", Icon = "fas fa-tasks" },
        new { Text = "Epics", Href = (string?)null, Icon = "fas fa-mountain" }
    };
}

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-mountain text-primary-600 dark:text-primary-400 mr-3"></i>
                Epic Management
            </h1>
            <p class="text-gray-600 dark:text-gray-300 mt-1">Manage and track your project epics</p>
        </div>
        
        <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
            <!-- Project Filter -->
            <div class="min-w-0 flex-1 sm:min-w-[200px]">
                <select id="projectFilter" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="">Select Project</option>
                    @foreach (var project in Model.UserProjects)
                    {
                        <option value="@project.Id" selected="@(project.Id == Model.ProjectId)">
                            @project.Name
                        </option>
                    }
                </select>
            </div>
            
            @if (Model.CanCreateEpic)
            {
                <a href="@Url.Action("Create", new { projectId = Model.ProjectId })" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    Create Epic
                </a>
            }
        </div>
    </div>

    @if (!Model.CanCreateEpic)
    {
        <!-- No Project Selected -->
        <div class="flex justify-center">
            <div class="w-full max-w-4xl">
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="text-center py-12 px-6">
                        <i class="fas fa-mountain text-gray-400 dark:text-gray-500 mb-6 text-6xl"></i>
                        <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">Select a Project</h4>
                        <p class="text-gray-600 dark:text-gray-300 mb-8">Choose a project from the dropdown above to view and manage its epics.</p>
                        
                        @if (Model.UserProjects.Any())
                        {
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach (var project in Model.UserProjects.Take(6))
                                {
                                    <a href="@Url.Action("Index", new { projectId = project.Id })" 
                                       class="block p-6 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm hover:shadow-md hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 group">
                                        <div class="text-center">
                                            <i class="fas fa-folder-open text-primary-600 dark:text-primary-400 mb-3 text-2xl group-hover:scale-110 transition-transform duration-200"></i>
                                            <h6 class="font-medium text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">@project.Name</h6>
                                        </div>
                                    </a>
                                }
                            </div>
                        }
                        else
                        {
                            <p class="text-gray-500 dark:text-gray-400">You don't have access to any projects yet.</p>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Epic Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-primary-600 text-white rounded-lg shadow-sm">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-primary-100 text-sm font-medium">Total Epics</div>
                            <div class="text-2xl font-bold mt-1">@Model.Statistics.TotalEpics</div>
                        </div>
                        <div class="flex-shrink-0">
                            <i class="fas fa-mountain text-3xl text-primary-200"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-600 text-white rounded-lg shadow-sm">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-green-100 text-sm font-medium">Completed</div>
                            <div class="text-2xl font-bold mt-1">@Model.Statistics.CompletedEpics</div>
                        </div>
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-3xl text-green-200"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-yellow-600 text-white rounded-lg shadow-sm">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-yellow-100 text-sm font-medium">In Progress</div>
                            <div class="text-2xl font-bold mt-1">@Model.Statistics.InProgressEpics</div>
                        </div>
                        <div class="flex-shrink-0">
                            <i class="fas fa-spinner text-3xl text-yellow-200"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-blue-600 text-white rounded-lg shadow-sm">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-blue-100 text-sm font-medium">Story Points</div>
                            <div class="text-2xl font-bold mt-1">@Model.Statistics.TotalStoryPoints</div>
                        </div>
                        <div class="flex-shrink-0">
                            <i class="fas fa-chart-line text-3xl text-blue-200"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Epic Management Interface -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Filters Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-filter text-primary-600 dark:text-primary-400 mr-2"></i>
                            Filters
                        </h3>
                    </div>
                    <div class="p-6">
                        <form id="epicFilters" class="space-y-6">
                            <!-- Status Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                                <select class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" name="status">
                                    <option value="">All Statuses</option>
                                    <option value="1">Draft</option>
                                    <option value="2">Ready</option>
                                    <option value="3">In Progress</option>
                                    <option value="4">Done</option>
                                    <option value="5">Cancelled</option>
                                </select>
                            </div>
                            
                            <!-- Priority Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Priority</label>
                                <select class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" name="priority">
                                    <option value="">All Priorities</option>
                                    <option value="1">Critical</option>
                                    <option value="2">High</option>
                                    <option value="3">Medium</option>
                                    <option value="4">Low</option>
                                </select>
                            </div>
                            
                            <!-- Search -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" name="search" placeholder="Search epics...">
                            </div>
                            
                            <!-- Quick Filters -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Quick Filters</label>
                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <input type="checkbox" name="overdue" id="filterOverdue" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                        <label for="filterOverdue" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                            Overdue Epics
                                        </label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" name="myEpics" id="filterMyEpics" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                        <label for="filterMyEpics" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                            My Epics
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="button" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200" id="clearFilters">
                                <i class="fas fa-times mr-2"></i>
                                Clear Filters
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Epic List -->
            <div class="lg:col-span-3">
                <!-- View Toggle -->
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-4">
                        <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                            <button type="button" class="px-3 py-1 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-200 bg-white dark:bg-gray-600 shadow-sm" id="gridView">
                                <i class="fas fa-th mr-2"></i>
                                Grid
                            </button>
                            <button type="button" class="px-3 py-1 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-200" id="listView">
                                <i class="fas fa-list mr-2"></i>
                                List
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            @Model.Epics.Count() epic(s)
                        </span>
                    </div>
                </div>

                <!-- Epic Cards Grid -->
                <div id="epicGrid" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    @foreach (var epic in Model.Epics)
                    {
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
                            <!-- Epic Header -->
                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                            <a href="@Url.Action("Details", new { id = epic.Id })" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                                                @epic.Title
                                            </a>
                                        </h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">@epic.EpicKey</p>
                                    </div>
                                    <div class="ml-4 flex-shrink-0">
                                        <div class="relative">
                                            <button type="button" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-full p-1" onclick="toggleEpicMenu(@epic.Id)">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <div id="<EMAIL>" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg z-10 border border-gray-200 dark:border-gray-600">
                                                <div class="py-1">
                                                    <a href="@Url.Action("Details", new { id = epic.Id })" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                                        <i class="fas fa-eye mr-2"></i>View Details
                                                    </a>
                                                    <a href="@Url.Action("Edit", new { id = epic.Id })" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                                        <i class="fas fa-edit mr-2"></i>Edit
                                                    </a>
                                                    <button type="button" onclick="deleteEpic(@epic.Id)" class="w-full text-left block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-600">
                                                        <i class="fas fa-trash mr-2"></i>Delete
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Epic Content -->
                            <div class="p-6">
                                <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                                    @epic.Description.Truncate(150)
                                </p>
                                
                                <!-- Epic Meta -->
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetStatusColor(epic.Status)">
                                        @epic.Status
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetPriorityColor(epic.Priority)">
                                        @epic.Priority
                                    </span>
                                    @if (epic.IsOverdue)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                            Overdue
                                        </span>
                                    }
                                </div>

                                <!-- Progress Bar -->
                                <div class="mb-4">
                                    <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                        <span>Progress</span>
                                        <span>@epic.ProgressPercentage.ToString("F0")%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: @epic.ProgressPercentage%"></div>
                                    </div>
                                </div>

                                <!-- Epic Stats -->
                                <div class="grid grid-cols-3 gap-4 text-center">
                                    <div>
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white">@epic.FeatureCount</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">Features</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white">@epic.UserStoryCount</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">Stories</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white">@epic.EstimatedStoryPoints</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">Points</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                @if (!Model.Epics.Any())
                {
                    <!-- No Epics -->
                    <div class="text-center py-12">
                        <i class="fas fa-mountain text-gray-400 dark:text-gray-500 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No epics found</h3>
                        <p class="text-gray-500 dark:text-gray-400 mb-6">Get started by creating your first epic for this project.</p>
                        <a href="@Url.Action("Create", new { projectId = Model.ProjectId })" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            Create Epic
                        </a>
                    </div>
                }
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Project filter change
            $('#projectFilter').on('change', function() {
                const projectId = $(this).val();
                if (projectId) {
                    window.location.href = '@Url.Action("Index")?projectId=' + projectId;
                } else {
                    window.location.href = '@Url.Action("Index")';
                }
            });

            // View toggle
            $('#gridView').on('click', function() {
                $(this).addClass('bg-white dark:bg-gray-600 shadow-sm');
                $('#listView').removeClass('bg-white dark:bg-gray-600 shadow-sm');
                $('#epicGrid').removeClass('hidden');
                $('#epicList').addClass('hidden');
            });

            $('#listView').on('click', function() {
                $(this).addClass('bg-white dark:bg-gray-600 shadow-sm');
                $('#gridView').removeClass('bg-white dark:bg-gray-600 shadow-sm');
                $('#epicList').removeClass('hidden');
                $('#epicGrid').addClass('hidden');
            });

            // Filter form
            $('#epicFilters').on('change', 'select, input', function() {
                filterEpics();
            });

            // Clear filters
            $('#clearFilters').on('click', function() {
                $('#epicFilters')[0].reset();
                filterEpics();
            });
        });

        function toggleEpicMenu(epicId) {
            const menu = document.getElementById('epicMenu-' + epicId);
            const allMenus = document.querySelectorAll('[id^="epicMenu-"]');

            // Close all other menus
            allMenus.forEach(m => {
                if (m.id !== 'epicMenu-' + epicId) {
                    m.classList.add('hidden');
                }
            });

            // Toggle current menu
            menu.classList.toggle('hidden');
        }

        function filterEpics() {
            const formData = new FormData(document.getElementById('epicFilters'));
            const params = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }

            if ('@Model.ProjectId' !== '') {
                params.append('projectId', '@Model.ProjectId');
            }

            window.location.href = '@Url.Action("Index")?' + params.toString();
        }

        function deleteEpic(epicId) {
            if (confirm('Are you sure you want to delete this epic? This action cannot be undone.')) {
                $.post('@Url.Action("Delete")', { id: epicId }, function(result) {
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('Error deleting epic: ' + result.message);
                    }
                });
            }
        }

        // Close menus when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('[onclick^="toggleEpicMenu"]') && !event.target.closest('[id^="epicMenu-"]')) {
                document.querySelectorAll('[id^="epicMenu-"]').forEach(menu => {
                    menu.classList.add('hidden');
                });
            }
        });
    </script>
}

@functions {
    private string GetStatusColor(EpicStatus status)
    {
        return status switch
        {
            EpicStatus.Draft => "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
            EpicStatus.Ready => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            EpicStatus.InProgress => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            EpicStatus.Done => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            EpicStatus.Cancelled => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
        };
    }

    private string GetPriorityColor(EpicPriority priority)
    {
        return priority switch
        {
            EpicPriority.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            EpicPriority.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            EpicPriority.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            EpicPriority.Low => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            _ => "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
        };
    }
}
