/**
 * PM.Tool Mentions System
 * Provides @mention functionality for text areas and rich text editors
 */

class MentionSystem {
    constructor(options = {}) {
        this.options = {
            apiBaseUrl: '/api/mention',
            projectId: null,
            debounceDelay: 300,
            maxSuggestions: 10,
            triggerChar: '@',
            ...options
        };

        this.cache = new Map();
        this.activeElement = null;
        this.suggestionBox = null;
        this.currentSuggestions = [];
        this.selectedIndex = -1;
        this.debounceTimer = null;

        this.init();
    }

    init() {
        this.createSuggestionBox();
        this.bindEvents();
    }

    createSuggestionBox() {
        this.suggestionBox = document.createElement('div');
        this.suggestionBox.className = 'mention-suggestions';
        this.suggestionBox.style.cssText = `
            position: absolute;
            z-index: 1000;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
            display: none;
            min-width: 200px;
        `;
        document.body.appendChild(this.suggestionBox);
    }

    bindEvents() {
        document.addEventListener('input', this.handleInput.bind(this));
        document.addEventListener('keydown', this.handleKeydown.bind(this));
        document.addEventListener('click', this.handleClick.bind(this));
        document.addEventListener('scroll', this.hideSuggestions.bind(this));
        window.addEventListener('resize', this.hideSuggestions.bind(this));
    }

    handleInput(event) {
        const element = event.target;
        if (!this.isMentionEnabled(element)) return;

        const text = element.value;
        const cursorPos = element.selectionStart;
        const mentionMatch = this.findMentionAtCursor(text, cursorPos);

        if (mentionMatch) {
            this.activeElement = element;
            this.showSuggestions(mentionMatch.query, element, mentionMatch.start);
        } else {
            this.hideSuggestions();
        }
    }

    handleKeydown(event) {
        if (!this.suggestionBox || this.suggestionBox.style.display === 'none') return;

        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                this.selectNext();
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.selectPrevious();
                break;
            case 'Enter':
            case 'Tab':
                event.preventDefault();
                this.insertSelectedMention();
                break;
            case 'Escape':
                this.hideSuggestions();
                break;
        }
    }

    handleClick(event) {
        if (this.suggestionBox && this.suggestionBox.contains(event.target)) {
            const suggestionItem = event.target.closest('.mention-suggestion-item');
            if (suggestionItem) {
                const index = parseInt(suggestionItem.dataset.index);
                this.selectedIndex = index;
                this.insertSelectedMention();
            }
        } else if (!this.activeElement || !this.activeElement.contains(event.target)) {
            this.hideSuggestions();
        }
    }

    isMentionEnabled(element) {
        return element.tagName === 'TEXTAREA' || 
               element.tagName === 'INPUT' || 
               element.contentEditable === 'true' ||
               element.hasAttribute('data-mentions-enabled');
    }

    findMentionAtCursor(text, cursorPos) {
        const beforeCursor = text.substring(0, cursorPos);
        const match = beforeCursor.match(/@([a-zA-Z0-9._\s]*)$/);
        
        if (match) {
            return {
                query: match[1],
                start: cursorPos - match[1].length - 1,
                end: cursorPos
            };
        }
        return null;
    }

    async showSuggestions(query, element, mentionStart) {
        clearTimeout(this.debounceTimer);
        
        this.debounceTimer = setTimeout(async () => {
            try {
                const users = await this.fetchUsers(query);
                this.currentSuggestions = users;
                this.selectedIndex = -1;
                this.renderSuggestions();
                this.positionSuggestionBox(element, mentionStart);
            } catch (error) {
                console.error('Failed to fetch mention suggestions:', error);
                this.hideSuggestions();
            }
        }, this.options.debounceDelay);
    }

    async fetchUsers(query) {
        const cacheKey = `${this.options.projectId || 'global'}-${query}`;
        
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        const params = new URLSearchParams();
        if (this.options.projectId) {
            params.append('projectId', this.options.projectId);
        }
        if (query) {
            params.append('query', query);
        }

        const response = await fetch(`${this.options.apiBaseUrl}/users?${params}`);
        if (!response.ok) {
            throw new Error('Failed to fetch users');
        }

        const users = await response.json();
        const limitedUsers = users.slice(0, this.options.maxSuggestions);
        
        this.cache.set(cacheKey, limitedUsers);
        return limitedUsers;
    }

    renderSuggestions() {
        if (!this.currentSuggestions.length) {
            this.hideSuggestions();
            return;
        }

        this.suggestionBox.innerHTML = this.currentSuggestions
            .map((user, index) => `
                <div class="mention-suggestion-item ${index === this.selectedIndex ? 'selected' : ''}" 
                     data-index="${index}"
                     style="padding: 8px 12px; cursor: pointer; display: flex; align-items: center; gap: 8px; ${index === this.selectedIndex ? 'background-color: #f3f4f6;' : ''}">
                    <img src="${user.avatar}" alt="${user.displayName}" 
                         style="width: 24px; height: 24px; border-radius: 50%; object-fit: cover;">
                    <div style="flex: 1; min-width: 0;">
                        <div style="font-weight: 500; color: #111827; font-size: 14px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                            ${user.displayName || user.username}
                        </div>
                        <div style="color: #6b7280; font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                            @${user.username}
                        </div>
                    </div>
                </div>
            `).join('');

        this.suggestionBox.style.display = 'block';
    }

    positionSuggestionBox(element, mentionStart) {
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        // Create a temporary element to measure text position
        const temp = document.createElement('div');
        temp.style.cssText = `
            position: absolute;
            visibility: hidden;
            white-space: pre-wrap;
            font-family: ${style.fontFamily};
            font-size: ${style.fontSize};
            line-height: ${style.lineHeight};
            padding: ${style.padding};
            border: ${style.border};
            width: ${element.offsetWidth}px;
        `;
        
        const textBeforeMention = element.value.substring(0, mentionStart);
        temp.textContent = textBeforeMention;
        document.body.appendChild(temp);
        
        const textRect = temp.getBoundingClientRect();
        document.body.removeChild(temp);
        
        // Position suggestion box
        const left = rect.left + (textRect.width % element.offsetWidth);
        const top = rect.top + parseInt(style.lineHeight) + window.scrollY;
        
        this.suggestionBox.style.left = `${left}px`;
        this.suggestionBox.style.top = `${top}px`;
    }

    selectNext() {
        this.selectedIndex = Math.min(this.selectedIndex + 1, this.currentSuggestions.length - 1);
        this.renderSuggestions();
    }

    selectPrevious() {
        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
        this.renderSuggestions();
    }

    insertSelectedMention() {
        if (this.selectedIndex < 0 || !this.currentSuggestions[this.selectedIndex] || !this.activeElement) {
            return;
        }

        const user = this.currentSuggestions[this.selectedIndex];
        const element = this.activeElement;
        const text = element.value;
        const cursorPos = element.selectionStart;
        
        const mentionMatch = this.findMentionAtCursor(text, cursorPos);
        if (!mentionMatch) return;

        const mentionText = `@${user.displayName || user.username}`;
        const newText = text.substring(0, mentionMatch.start) + 
                       mentionText + ' ' + 
                       text.substring(cursorPos);
        
        element.value = newText;
        element.selectionStart = element.selectionEnd = mentionMatch.start + mentionText.length + 1;
        
        // Trigger input event to notify other systems
        element.dispatchEvent(new Event('input', { bubbles: true }));
        
        this.hideSuggestions();
        element.focus();
    }

    hideSuggestions() {
        if (this.suggestionBox) {
            this.suggestionBox.style.display = 'none';
        }
        this.activeElement = null;
        this.currentSuggestions = [];
        this.selectedIndex = -1;
    }

    // Public API methods
    enableForElement(element, projectId = null) {
        element.setAttribute('data-mentions-enabled', 'true');
        if (projectId) {
            element.setAttribute('data-mention-project-id', projectId);
        }
    }

    disableForElement(element) {
        element.removeAttribute('data-mentions-enabled');
        element.removeAttribute('data-mention-project-id');
    }

    setProjectContext(projectId) {
        this.options.projectId = projectId;
        this.cache.clear(); // Clear cache when project context changes
    }

    async processContent(content, context = {}) {
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content,
                    ...context
                })
            });

            if (!response.ok) {
                throw new Error('Failed to process mentions');
            }

            return await response.json();
        } catch (error) {
            console.error('Error processing mentions:', error);
            throw error;
        }
    }

    async formatContent(content) {
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/format`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ content })
            });

            if (!response.ok) {
                throw new Error('Failed to format mentions');
            }

            const result = await response.json();
            return result.formattedContent;
        } catch (error) {
            console.error('Error formatting mentions:', error);
            return content; // Return original content on error
        }
    }
}

// Global instance
window.MentionSystem = MentionSystem;

// Auto-initialize if jQuery is available
if (typeof $ !== 'undefined') {
    $(document).ready(function() {
        window.mentionSystem = new MentionSystem();
    });
}
