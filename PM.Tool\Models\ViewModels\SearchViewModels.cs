using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Models.ViewModels
{
    public class SearchViewModel
    {
        public string Query { get; set; } = string.Empty;
        public List<SearchResultViewModel> Results { get; set; } = new();
        public int TotalResults { get; set; }
        public string SearchType { get; set; } = "all"; // all, projects, tasks, people, documents
    }

    public class SearchResultViewModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // Project, Task, Person, Document
        public string Url { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public Dictionary<string, string> Metadata { get; set; } = new();
    }

    public class SearchResultsViewModel
    {
        public string Query { get; set; } = string.Empty;
        public List<SearchResultItemViewModel> Results { get; set; } = new();
        public int TotalResults { get; set; }
        public int CurrentPage { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalPages { get; set; }
        public Dictionary<string, int> ResultsByType { get; set; } = new();
        
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
        public int StartResult => (CurrentPage - 1) * PageSize + 1;
        public int EndResult => Math.Min(CurrentPage * PageSize, TotalResults);
    }

    public class SearchResultItemViewModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public Dictionary<string, string> Metadata { get; set; } = new();
    }
}
