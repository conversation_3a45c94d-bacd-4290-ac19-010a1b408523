using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Models.ViewModels;
using System.Security.Claims;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class EpicController : SecureBaseController
    {
        private readonly ApplicationDbContext _context;
        private readonly IAgileService _agileService;
        private readonly ILogger<EpicController> _logger;
        private readonly IRealTimeNotificationService _realTimeService;

        public EpicController(
            ApplicationDbContext context,
            IAgileService agileService,
            ILogger<EpicController> logger,
            IRealTimeNotificationService realTimeService,
            IAuditService auditService) : base(auditService)
        {
            _context = context;
            _agileService = agileService;
            _logger = logger;
            _realTimeService = realTimeService;
        }

        // GET: Epic/Index
        public async Task<IActionResult> Index(int? projectId = null)
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var userProjects = await GetUserProjectsAsync(userId);

                var epics = new List<Epic>();
                if (projectId.HasValue)
                {
                    epics = await _context.Epics
                        .Include(e => e.Project)
                        .Include(e => e.Owner)
                        .Include(e => e.Features)
                        .Include(e => e.UserStories)
                        .Where(e => e.ProjectId == projectId.Value)
                        .OrderBy(e => e.SortOrder)
                        .ThenBy(e => e.CreatedAt)
                        .ToListAsync();
                }

                var statistics = new EpicStatisticsViewModel
                {
                    TotalEpics = epics.Count,
                    CompletedEpics = epics.Count(e => e.Status == EpicStatus.Done),
                    InProgressEpics = epics.Count(e => e.Status == EpicStatus.InProgress),
                    DraftEpics = epics.Count(e => e.Status == EpicStatus.Draft),
                    CancelledEpics = epics.Count(e => e.Status == EpicStatus.Cancelled),
                    TotalStoryPoints = epics.Sum(e => e.EstimatedStoryPoints),
                    CompletedStoryPoints = epics.Where(e => e.Status == EpicStatus.Done).Sum(e => e.ActualStoryPoints),
                    OverdueEpics = epics.Count(e => e.IsOverdue)
                };

                statistics.ProgressPercentage = statistics.TotalStoryPoints > 0
                    ? (statistics.CompletedStoryPoints / statistics.TotalStoryPoints) * 100
                    : 0;

                var viewModel = new EpicManagementViewModel
                {
                    ProjectId = projectId,
                    UserProjects = userProjects,
                    Epics = epics,
                    CanCreateEpic = projectId.HasValue,
                    Statistics = statistics
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading epic management");
                return View(new EpicManagementViewModel());
            }
        }

        // GET: Epic/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var epic = await _context.Epics
                    .Include(e => e.Project)
                    .Include(e => e.Owner)
                    .Include(e => e.Features)
                        .ThenInclude(f => f.UserStories)
                    .Include(e => e.UserStories)
                        .ThenInclude(us => us.Tasks)
                    .FirstOrDefaultAsync(e => e.Id == id);

                if (epic == null)
                {
                    return NotFound();
                }

                var viewModel = new EpicDetailsViewModel
                {
                    Epic = epic,
                    Features = epic.Features.OrderBy(f => f.SortOrder).ToList(),
                    UserStories = epic.UserStories.OrderBy(us => us.BacklogOrder).ToList(),
                    ProgressMetrics = CalculateEpicProgress(epic),
                    Timeline = await GetEpicTimelineAsync(epic.Id)
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading epic details for ID {EpicId}", id);
                return NotFound();
            }
        }

        // GET: Epic/Create
        public async Task<IActionResult> Create(int? projectId = null)
        {
            try
            {
                var viewModel = new EpicCreateViewModel
                {
                    ProjectId = projectId ?? 0,
                    Priority = EpicPriority.Medium
                };

                ViewBag.UserProjects = await GetUserProjectsAsync(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "");
                ViewBag.TeamMembers = new List<UserSelectViewModel>();

                if (projectId.HasValue)
                {
                    ViewBag.TeamMembers = await GetTeamMembersAsync(projectId.Value);
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading epic create form");
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Epic/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(EpicCreateViewModel model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var epic = model.ToEntity();
                    epic.SortOrder = await GetNextSortOrderAsync(model.ProjectId);
                    epic.EpicKey = await GenerateEpicKeyAsync(model.ProjectId);

                    _context.Epics.Add(epic);
                    await _context.SaveChangesAsync();

                    // Log audit
                    await LogAuditAsync(Core.Enums.AuditAction.Create, "Epic", epic.Id);

                    // Send real-time notification
                    await _realTimeService.BroadcastToProjectAsync(
                        epic.ProjectId,
                        "EpicCreated",
                        new { EpicId = epic.Id, Title = epic.Title });

                    TempData["SuccessMessage"] = "Epic created successfully!";
                    return RedirectToAction(nameof(Details), new { id = epic.Id });
                }

                ViewBag.UserProjects = await GetUserProjectsAsync(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "");
                ViewBag.TeamMembers = new List<UserSelectViewModel>();
                if (model.ProjectId > 0)
                {
                    ViewBag.TeamMembers = await GetTeamMembersAsync(model.ProjectId);
                }
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating epic");
                ModelState.AddModelError("", "An error occurred while creating the epic.");
                ViewBag.UserProjects = await GetUserProjectsAsync(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "");
                ViewBag.TeamMembers = new List<UserSelectViewModel>();
                return View(model);
            }
        }

        // GET: Epic/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var epic = await _context.Epics
                    .Include(e => e.Project)
                    .FirstOrDefaultAsync(e => e.Id == id);

                if (epic == null)
                {
                    return NotFound();
                }

                var viewModel = new EpicEditViewModel
                {
                    Id = epic.Id,
                    Title = epic.Title,
                    Description = epic.Description,
                    ProjectId = epic.ProjectId,
                    Priority = epic.Priority,
                    Status = epic.Status,
                    AcceptanceCriteria = epic.AcceptanceCriteria,
                    BusinessValue = epic.BusinessValue,
                    EstimatedStoryPoints = epic.EstimatedStoryPoints,
                    ActualStoryPoints = epic.ActualStoryPoints,
                    OwnerId = epic.OwnerId,
                    TargetDate = epic.TargetDate,
                    Tags = epic.Tags,
                    EpicKey = epic.EpicKey
                };

                ViewBag.UserProjects = await GetUserProjectsAsync(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "");
                ViewBag.TeamMembers = await GetTeamMembersAsync(epic.ProjectId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading epic edit form for ID {EpicId}", id);
                return NotFound();
            }
        }

        // POST: Epic/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EpicEditViewModel model)
        {
            try
            {
                if (id != model.Id)
                {
                    return NotFound();
                }

                if (ModelState.IsValid)
                {
                    var epic = await _context.Epics.FindAsync(id);
                    if (epic == null)
                    {
                        return NotFound();
                    }

                    // Store old values for audit
                    var oldValues = new
                    {
                        epic.Title,
                        epic.Description,
                        epic.Priority,
                        epic.Status,
                        epic.EstimatedStoryPoints,
                        epic.OwnerId,
                        epic.TargetDate
                    };

                    // Update epic
                    model.UpdateEntity(epic);
                    epic.Status = model.Status;
                    epic.ActualStoryPoints = model.ActualStoryPoints;

                    // Set completion date if status changed to Done
                    if (model.Status == EpicStatus.Done && epic.CompletedDate == null)
                    {
                        epic.CompletedDate = DateTime.UtcNow;
                    }
                    else if (model.Status != EpicStatus.Done)
                    {
                        epic.CompletedDate = null;
                    }

                    await _context.SaveChangesAsync();

                    // Log audit
                    await LogAuditAsync(Core.Enums.AuditAction.Update, "Epic", epic.Id,
                        oldValues: System.Text.Json.JsonSerializer.Serialize(oldValues),
                        newValues: System.Text.Json.JsonSerializer.Serialize(new
                        {
                            epic.Title,
                            epic.Description,
                            epic.Priority,
                            epic.Status,
                            epic.EstimatedStoryPoints,
                            epic.OwnerId,
                            epic.TargetDate
                        }));

                    // Send real-time notification
                    await _realTimeService.BroadcastToProjectAsync(
                        epic.ProjectId,
                        "EpicUpdated",
                        new { EpicId = epic.Id, Title = epic.Title, Status = epic.Status.ToString() });

                    TempData["SuccessMessage"] = "Epic updated successfully!";
                    return RedirectToAction(nameof(Details), new { id = epic.Id });
                }

                ViewBag.UserProjects = await GetUserProjectsAsync(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "");
                ViewBag.TeamMembers = await GetTeamMembersAsync(model.ProjectId);
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating epic with ID {EpicId}", id);
                ModelState.AddModelError("", "An error occurred while updating the epic.");
                ViewBag.UserProjects = await GetUserProjectsAsync(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "");
                ViewBag.TeamMembers = await GetTeamMembersAsync(model.ProjectId);
                return View(model);
            }
        }

        // POST: Epic/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var epic = await _context.Epics
                    .Include(e => e.Features)
                    .Include(e => e.UserStories)
                    .FirstOrDefaultAsync(e => e.Id == id);

                if (epic == null)
                {
                    return Json(new { success = false, message = "Epic not found" });
                }

                // Check if epic has dependencies
                if (epic.Features.Any() || epic.UserStories.Any())
                {
                    return Json(new { 
                        success = false, 
                        message = "Cannot delete epic with associated features or user stories. Please remove them first." 
                    });
                }

                var projectId = epic.ProjectId;
                var title = epic.Title;

                _context.Epics.Remove(epic);
                await _context.SaveChangesAsync();

                // Log audit
                await LogAuditAsync(Core.Enums.AuditAction.Delete, "Epic", id);

                // Send real-time notification
                await _realTimeService.BroadcastToProjectAsync(
                    projectId,
                    "EpicDeleted",
                    new { EpicId = id, Title = title });

                return Json(new { success = true, message = "Epic deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting epic with ID {EpicId}", id);
                return Json(new { success = false, message = "An error occurred while deleting the epic" });
            }
        }

        #region Helper Methods

        private async Task<List<ProjectSelectViewModel>> GetUserProjectsAsync(string userId)
        {
            if (string.IsNullOrEmpty(userId))
                return new List<ProjectSelectViewModel>();

            return await _context.ProjectMembers
                .Where(pm => pm.UserId == userId && pm.IsActive)
                .Include(pm => pm.Project)
                .Select(pm => new ProjectSelectViewModel
                {
                    Id = pm.Project.Id,
                    Name = pm.Project.Name
                })
                .ToListAsync();
        }

        private async Task<List<UserSelectViewModel>> GetTeamMembersAsync(int projectId)
        {
            return await _context.ProjectMembers
                .Where(pm => pm.ProjectId == projectId && pm.IsActive)
                .Include(pm => pm.User)
                .Select(pm => new UserSelectViewModel
                {
                    Id = pm.UserId,
                    Name = pm.User.UserName ?? pm.User.Email ?? "Unknown",
                    Email = pm.User.Email ?? ""
                })
                .ToListAsync();
        }



        private async Task<int> GetNextSortOrderAsync(int projectId)
        {
            var maxOrder = await _context.Epics
                .Where(e => e.ProjectId == projectId)
                .MaxAsync(e => (int?)e.SortOrder) ?? 0;
            return maxOrder + 1;
        }

        private async Task<string> GenerateEpicKeyAsync(int projectId)
        {
            var project = await _context.Projects.FindAsync(projectId);
            var projectCode = project?.Name?.Substring(0, Math.Min(3, project.Name.Length)).ToUpper() ?? "PRJ";

            var count = await _context.Epics
                .Where(e => e.ProjectId == projectId)
                .CountAsync();

            return $"{projectCode}-EP-{(count + 1):D3}";
        }

        private EpicProgressMetrics CalculateEpicProgress(Epic epic)
        {
            var totalFeatures = epic.Features.Count;
            var completedFeatures = epic.Features.Count(f => f.IsCompleted);
            var totalUserStories = epic.UserStories.Count;
            var completedUserStories = epic.UserStories.Count(us => us.IsCompleted);
            var totalTasks = epic.UserStories.SelectMany(us => us.Tasks).Count();
            var completedTasks = epic.UserStories.SelectMany(us => us.Tasks).Count(t => t.Status == Core.Enums.TaskStatus.Done);

            return new EpicProgressMetrics
            {
                TotalFeatures = totalFeatures,
                CompletedFeatures = completedFeatures,
                FeatureProgress = totalFeatures > 0 ? (double)completedFeatures / totalFeatures * 100 : 0,
                TotalUserStories = totalUserStories,
                CompletedUserStories = completedUserStories,
                UserStoryProgress = totalUserStories > 0 ? (double)completedUserStories / totalUserStories * 100 : 0,
                TotalTasks = totalTasks,
                CompletedTasks = completedTasks,
                TaskProgress = totalTasks > 0 ? (double)completedTasks / totalTasks * 100 : 0,
                EstimatedStoryPoints = epic.EstimatedStoryPoints,
                ActualStoryPoints = epic.ActualStoryPoints,
                StoryPointProgress = epic.EstimatedStoryPoints > 0 ? (double)epic.ActualStoryPoints / (double)epic.EstimatedStoryPoints * 100 : 0
            };
        }

        private async Task<List<EpicTimelineEvent>> GetEpicTimelineAsync(int epicId)
        {
            var timeline = new List<EpicTimelineEvent>();

            // Get epic creation
            var epic = await _context.Epics
                .Include(e => e.Features)
                .Include(e => e.UserStories)
                .FirstOrDefaultAsync(e => e.Id == epicId);

            if (epic != null)
            {
                timeline.Add(new EpicTimelineEvent
                {
                    Date = epic.CreatedAt,
                    Type = "Epic Created",
                    Description = $"Epic '{epic.Title}' was created",
                    Icon = "fas fa-plus-circle",
                    Color = "success"
                });

                // Add feature milestones
                foreach (var feature in epic.Features.OrderBy(f => f.CreatedAt))
                {
                    timeline.Add(new EpicTimelineEvent
                    {
                        Date = feature.CreatedAt,
                        Type = "Feature Added",
                        Description = $"Feature '{feature.Title}' was added",
                        Icon = "fas fa-puzzle-piece",
                        Color = "info"
                    });

                    if (feature.IsCompleted && feature.CompletedDate.HasValue)
                    {
                        timeline.Add(new EpicTimelineEvent
                        {
                            Date = feature.CompletedDate.Value,
                            Type = "Feature Completed",
                            Description = $"Feature '{feature.Title}' was completed",
                            Icon = "fas fa-check-circle",
                            Color = "success"
                        });
                    }
                }

                // Add completion if epic is done
                if (epic.IsCompleted && epic.CompletedDate.HasValue)
                {
                    timeline.Add(new EpicTimelineEvent
                    {
                        Date = epic.CompletedDate.Value,
                        Type = "Epic Completed",
                        Description = $"Epic '{epic.Title}' was completed",
                        Icon = "fas fa-flag-checkered",
                        Color = "success"
                    });
                }
            }

            return timeline.OrderBy(t => t.Date).ToList();
        }

        #endregion
    }
}
