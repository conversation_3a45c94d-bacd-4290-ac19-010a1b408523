@{
    var fieldType = ViewData["Type"]?.ToString() ?? "text";
    var fieldName = ViewData["Name"]?.ToString() ?? "";
    var fieldLabel = ViewData["Label"]?.ToString() ?? "";
    var fieldPlaceholder = ViewData["Placeholder"]?.ToString() ?? "";
    var fieldValue = ViewData["Value"]?.ToString() ?? "";
    var fieldRequired = ViewData["Required"] as bool? ?? false;
    var fieldHelp = ViewData["Help"]?.ToString() ?? "";
    var fieldRows = ViewData["Rows"] as int? ?? 4;
    var fieldOptions = ViewData["Options"] as IEnumerable<object> ?? new List<object>();
    var fieldClasses = ViewData["Classes"]?.ToString() ?? "";
    var fieldAttributes = ViewData["Attributes"] as Dictionary<string, object> ?? new Dictionary<string, object>();
    var colSpan = ViewData["ColSpan"]?.ToString() ?? "";
}

<div class="@colSpan">
    <label for="@fieldName" class="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
        @fieldLabel
        @if (fieldRequired)
        {
            <span class="text-danger-600">*</span>
        }
    </label>

    @if (fieldType == "textarea")
    {
        <textarea name="@fieldName" id="@fieldName" rows="@fieldRows"
                  class="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-800 dark:text-neutral-100 @fieldClasses"
                  placeholder="@fieldPlaceholder"
                  @Html.Raw(string.Join(" ", fieldAttributes.Select(attr => $"{attr.Key}=\"{attr.Value}\"")))>@fieldValue</textarea>
    }
    else if (fieldType == "select")
    {
        <select name="@fieldName" id="@fieldName"
                class="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-800 dark:text-neutral-100 @fieldClasses"
                @Html.Raw(string.Join(" ", fieldAttributes.Select(attr => $"{attr.Key}=\"{attr.Value}\"")))>
            @foreach (dynamic option in fieldOptions)
            {
                <option value="@option.Value" selected="@(option.Selected == true)">@option.Text</option>
            }
        </select>
    }
    else
    {
        <input type="@fieldType" name="@fieldName" id="@fieldName" value="@fieldValue"
               class="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-800 dark:text-neutral-100 @fieldClasses"
               placeholder="@fieldPlaceholder"
               @Html.Raw(string.Join(" ", fieldAttributes.Select(attr => $"{attr.Key}=\"{attr.Value}\""))) />
    }

    @if (!string.IsNullOrEmpty(fieldHelp))
    {
        <p class="mt-2 text-sm text-neutral-500 dark:text-neutral-400">@fieldHelp</p>
    }

    <!-- Validation message placeholder -->
    <span class="text-danger-600 text-sm validation-message" data-field="@fieldName"></span>
</div>
