@model IEnumerable<PM.Tool.Models.ViewModels.ProjectViewModel>

@{
    ViewData["Title"] = "Projects";
    ViewData["Subtitle"] = "Manage and track your project portfolio";
    
    // Page Layout Configuration
    ViewData["PageTitle"] = "Projects";
    ViewData["PageSubtitle"] = "Manage and track your project portfolio";
    ViewData["PageIcon"] = "fas fa-folder-open";
    ViewData["PageActions"] = $"<a href='{Url.Action("Create")}' class='btn-compact btn-compact-primary'><i class='fas fa-plus'></i> New Project</a>";
    
    // Statistics Configuration
    ViewData["ShowStats"] = true;
    var totalProjects = Model.Count();
    var activeProjects = Model.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Active);
    var completedProjects = Model.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Completed);
    var onHoldProjects = Model.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.OnHold);
    
    ViewData["StatsContent"] = $@"
        <div class='card-compact'>
            <div class='card-compact-body'>
                <div class='flex items-center justify-between'>
                    <div>
                        <p class='text-sm text-neutral-500 dark:text-neutral-400'>Total Projects</p>
                        <p class='text-2xl font-bold text-neutral-900 dark:text-white'>{totalProjects}</p>
                    </div>
                    <div class='w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center'>
                        <i class='fas fa-folder text-blue-600 dark:text-blue-400'></i>
                    </div>
                </div>
            </div>
        </div>
        <div class='card-compact'>
            <div class='card-compact-body'>
                <div class='flex items-center justify-between'>
                    <div>
                        <p class='text-sm text-neutral-500 dark:text-neutral-400'>Active</p>
                        <p class='text-2xl font-bold text-green-600 dark:text-green-400'>{activeProjects}</p>
                    </div>
                    <div class='w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center'>
                        <i class='fas fa-play text-green-600 dark:text-green-400'></i>
                    </div>
                </div>
            </div>
        </div>
        <div class='card-compact'>
            <div class='card-compact-body'>
                <div class='flex items-center justify-between'>
                    <div>
                        <p class='text-sm text-neutral-500 dark:text-neutral-400'>Completed</p>
                        <p class='text-2xl font-bold text-blue-600 dark:text-blue-400'>{completedProjects}</p>
                    </div>
                    <div class='w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center'>
                        <i class='fas fa-check text-blue-600 dark:text-blue-400'></i>
                    </div>
                </div>
            </div>
        </div>
        <div class='card-compact'>
            <div class='card-compact-body'>
                <div class='flex items-center justify-between'>
                    <div>
                        <p class='text-sm text-neutral-500 dark:text-neutral-400'>On Hold</p>
                        <p class='text-2xl font-bold text-yellow-600 dark:text-yellow-400'>{onHoldProjects}</p>
                    </div>
                    <div class='w-10 h-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center'>
                        <i class='fas fa-pause text-yellow-600 dark:text-yellow-400'></i>
                    </div>
                </div>
            </div>
        </div>";
    
    // Filters Configuration
    ViewData["ShowFilters"] = true;
    ViewData["FiltersContent"] = @"
        <div class='flex flex-wrap items-center gap-4'>
            <div class='flex items-center gap-2'>
                <label class='text-sm font-medium text-neutral-700 dark:text-neutral-300'>Status:</label>
                <select class='input-compact w-32'>
                    <option>All</option>
                    <option>Active</option>
                    <option>Completed</option>
                    <option>On Hold</option>
                </select>
            </div>
            <div class='flex items-center gap-2'>
                <label class='text-sm font-medium text-neutral-700 dark:text-neutral-300'>Priority:</label>
                <select class='input-compact w-32'>
                    <option>All</option>
                    <option>High</option>
                    <option>Medium</option>
                    <option>Low</option>
                </select>
            </div>
            <div class='flex items-center gap-2'>
                <input type='search' placeholder='Search projects...' class='input-compact w-48'>
                <button class='btn-compact btn-compact-secondary'>
                    <i class='fas fa-search'></i>
                </button>
            </div>
        </div>";
}

<partial name="Components/_PageLayoutCompact" view-data="ViewData">
    @if (Model.Any())
    {
        <!-- Compact Projects Table -->
        <div class="card-compact">
            <div class="card-compact-body p-0">
                <table class="table-compact">
                    <thead>
                        <tr>
                            <th>Project</th>
                            <th>Status</th>
                            <th>Progress</th>
                            <th>Manager</th>
                            <th>Due Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var project in Model)
                        {
                            <tr>
                                <td>
                                    <div class="flex items-center gap-3">
                                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center flex-shrink-0">
                                            <i class="fas fa-folder text-primary-600 dark:text-primary-400 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-neutral-900 dark:text-white">@project.Name</div>
                                            @if (!string.IsNullOrEmpty(project.Description))
                                            {
                                                <div class="text-sm text-neutral-500 dark:text-neutral-400 truncate max-w-xs">@project.Description</div>
                                            }
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-pill <EMAIL>().ToLower()">
                                        @project.Status
                                    </span>
                                </td>
                                <td>
                                    <div class="flex items-center gap-2">
                                        <div class="w-16 bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                                            <div class="bg-primary-600 h-2 rounded-full" style="width: @(project.ProgressPercentage)%"></div>
                                        </div>
                                        <span class="text-sm text-neutral-600 dark:text-neutral-400">@(project.ProgressPercentage)%</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-sm text-neutral-900 dark:text-white">@project.ManagerName</div>
                                </td>
                                <td>
                                    <div class="text-sm text-neutral-600 dark:text-neutral-400">
                                        @project.EndDate?.ToString("MMM dd, yyyy")
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center gap-1">
                                        <a href="@Url.Action("Details", new { id = project.Id })" 
                                           class="btn-compact btn-compact-secondary" 
                                           title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="@Url.Action("Edit", new { id = project.Id })" 
                                           class="btn-compact btn-compact-secondary" 
                                           title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }
    else
    {
        <!-- Compact Empty State -->
        <div class="card-compact">
            <div class="card-compact-body text-center py-12">
                <div class="w-16 h-16 bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-folder-open text-2xl text-neutral-400 dark:text-neutral-500"></i>
                </div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-2">No Projects Found</h3>
                <p class="text-neutral-500 dark:text-neutral-400 mb-6">Get started by creating your first project.</p>
                <a href="@Url.Action("Create")" class="btn-compact btn-compact-primary">
                    <i class="fas fa-plus"></i>
                    Create First Project
                </a>
            </div>
        </div>
    }
</partial>

@section Scripts {
    <script>
        // Add any compact-specific JavaScript here
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize compact table interactions
            console.log('Compact Projects view loaded');
        });
    </script>
}
