@model PM.Tool.Core.Entities.Agile.Epic
@{
    ViewData["Title"] = $"Epic: {Model.Title}";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
    var userStories = ViewBag.UserStories as IEnumerable<PM.Tool.Core.Entities.Agile.UserStory> ?? new List<PM.Tool.Core.Entities.Agile.UserStory>();
    var totalStoryPoints = ViewBag.TotalStoryPoints as decimal? ?? 0;
    var completedStoryPoints = ViewBag.CompletedStoryPoints as decimal? ?? 0;
    var progressPercentage = ViewBag.ProgressPercentage as decimal? ?? 0;

    // Status and priority styling
    var statusColor = Model.Status.ToString().ToLower() switch {
        "draft" => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300",
        "ready" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
        "inprogress" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
        "done" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
        "cancelled" => "bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300",
        _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
    };
    var priorityColor = Model.Priority.ToString().ToLower() switch {
        "critical" => "bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300",
        "high" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
        "medium" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
        "low" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
        _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
    };

    // Standardized Page Header
    ViewData["PageTitle"] = Model.Title;
    ViewData["PageSubtitle"] = $"Epic {Model.EpicKey} • Created {Model.CreatedAt:MMM dd, yyyy}" + (Model.TargetDate.HasValue ? $" • Target: {Model.TargetDate.Value:MMM dd, yyyy}" : "");
    ViewData["PageIcon"] = "fas fa-layer-group";
    ViewData["Breadcrumbs"] = new List<object> {
        new { Text = "Projects", Icon = "fas fa-project-diagram", Href = Url.Action("Index", "Projects") },
        new { Text = project?.Name, Href = Url.Action("Details", "Projects", new { id = project?.Id }) },
        new { Text = "Epics", Href = Url.Action("Epics", new { projectId = project?.Id }) },
        new { Text = Model.EpicKey, IsActive = true }
    };
    ViewData["HeaderActions"] = new List<object> {
        new { Text = "Edit Epic", Variant = "outline", Icon = "fas fa-edit", Href = Url.Action("EditEpic", new { id = Model.Id }) },
        new { Text = "Add User Story", Variant = "primary", Icon = "fas fa-plus", Href = Url.Action("CreateUserStory", new { projectId = Model.ProjectId, epicId = Model.Id }) }
    };
    ViewData["HeaderBadges"] = new List<object> {
        new { Text = Model.Status.ToString(), Classes = statusColor },
        new { Text = $"{Model.Priority} Priority", Classes = priorityColor }
    };
}

<!-- Standardized Page Header -->
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Epic Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <!-- Progress Card -->
    @{
        ViewData["Title"] = "Progress";
        ViewData["Icon"] = "fas fa-chart-line";
        ViewData["IconColor"] = "text-primary-600 dark:text-primary-400";
        ViewData["Value"] = $"{progressPercentage:F1}%";
        ViewData["ShowProgress"] = true;
        ViewData["ProgressValue"] = progressPercentage;
    }
    <partial name="Components/_StatCard" view-data="ViewData" />

    <!-- Story Points Card -->
    @{
        ViewData["Title"] = "Story Points";
        ViewData["Icon"] = "fas fa-tasks";
        ViewData["IconColor"] = "text-success-600 dark:text-success-400";
        ViewData["Value"] = $"{completedStoryPoints}/{totalStoryPoints}";
        ViewData["Description"] = "Completed/Total";
        ViewData["ShowProgress"] = false;
    }
    <partial name="Components/_StatCard" view-data="ViewData" />

    <!-- User Stories Card -->
    @{
        ViewData["Title"] = "User Stories";
        ViewData["Icon"] = "fas fa-list";
        ViewData["IconColor"] = "text-warning-600 dark:text-warning-400";
        ViewData["Value"] = $"{userStories.Count(us => us.IsCompleted)}/{userStories.Count()}";
        ViewData["Description"] = "Completed/Total";
        ViewData["ShowProgress"] = false;
    }
    <partial name="Components/_StatCard" view-data="ViewData" />

    <!-- Business Value Card -->
    @{
        ViewData["Title"] = "Business Value";
        ViewData["Icon"] = "fas fa-dollar-sign";
        ViewData["IconColor"] = "text-info-600 dark:text-info-400";
        ViewData["Value"] = Model.EstimatedStoryPoints > 0 ? Model.EstimatedStoryPoints.ToString("F0") : "TBD";
        ViewData["Description"] = "Estimated Points";
        ViewData["ShowProgress"] = false;
    }
    <partial name="Components/_StatCard" view-data="ViewData" />
</div>

<!-- Epic Details -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
        <!-- Description -->
        @{
            ViewData["Title"] = "Description";
            ViewData["Icon"] = "fas fa-align-left";
            ViewData["BodyContent"] = !string.IsNullOrEmpty(Model.Description)
                ? $"<div class='prose dark:prose-invert max-w-none'><p class='text-neutral-700 dark:text-neutral-300 leading-relaxed'>{Model.Description}</p></div>"
                : "<p class='text-neutral-500 dark:text-neutral-400 italic'>No description provided.</p>";
        }
        <partial name="Components/_Card" view-data="ViewData" />

        <!-- Acceptance Criteria -->
        @if (!string.IsNullOrEmpty(Model.AcceptanceCriteria))
        {
            ViewData["Title"] = "Acceptance Criteria";
            ViewData["Icon"] = "fas fa-check-circle";
            ViewData["BodyContent"] = $"<div class='prose dark:prose-invert max-w-none'><p class='text-neutral-700 dark:text-neutral-300 leading-relaxed'>{Model.AcceptanceCriteria}</p></div>";
            <partial name="Components/_Card" view-data="ViewData" />
        }

        <!-- Business Value -->
        @if (!string.IsNullOrEmpty(Model.BusinessValue))
        {
            ViewData["Title"] = "Business Value";
            ViewData["Icon"] = "fas fa-chart-line";
            ViewData["BodyContent"] = $"<div class='prose dark:prose-invert max-w-none'><p class='text-neutral-700 dark:text-neutral-300 leading-relaxed'>{Model.BusinessValue}</p></div>";
            <partial name="Components/_Card" view-data="ViewData" />
        }

        <!-- User Stories -->
        @{
            ViewData["Title"] = $"User Stories ({userStories.Count()})";
            ViewData["Icon"] = "fas fa-list";
            ViewData["HeaderActions"] = new List<object> {
                new { Text = "Add User Story", Variant = "primary", Icon = "fas fa-plus", Size = "sm", Href = Url.Action("CreateUserStory", new { projectId = Model.ProjectId, epicId = Model.Id }) }
            };
        }
        <partial name="Components/_Card" view-data="ViewData">
            @if (userStories.Any())
            {
                <div class="space-y-3">
                    @foreach (var story in userStories.OrderBy(us => us.Priority))
                    {
                            var storyStatusColor = story.Status.ToString().ToLower() switch {
                                "backlog" => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300",
                                "ready" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
                                "inprogress" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
                                "review" => "bg-info-100 text-info-700 dark:bg-info-900 dark:text-info-300",
                                "done" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
                                _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                            };
                        <div class="border border-neutral-200 dark:border-neutral-600 rounded-lg p-3 hover:shadow-sm transition-all duration-200 hover:border-neutral-300 dark:hover:border-neutral-500">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center gap-2 mb-2">
                                        <h4 class="font-medium text-neutral-900 dark:text-neutral-100 truncate">@story.Title</h4>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium @storyStatusColor flex-shrink-0">
                                            @story.Status
                                        </span>
                                        @if (story.StoryPoints > 0)
                                        {
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-neutral-100 text-neutral-700 dark:bg-neutral-700 dark:text-neutral-300 flex-shrink-0">
                                                @story.StoryPoints SP
                                            </span>
                                        }
                                    </div>
                                    <p class="text-sm text-neutral-600 dark:text-neutral-400 mb-1 line-clamp-2">@story.UserStoryFormat</p>
                                    @if (story.AssignedTo != null)
                                    {
                                        <p class="text-xs text-neutral-500 dark:text-neutral-400">
                                            <i class="fas fa-user mr-1"></i>@story.AssignedTo.UserName
                                        </p>
                                    }
                                </div>
                                <div class="ml-3 flex-shrink-0">
                                    @{
                                        ViewData["Text"] = "";
                                        ViewData["Variant"] = "ghost";
                                        ViewData["Size"] = "sm";
                                        ViewData["Icon"] = "fas fa-eye";
                                        ViewData["Href"] = Url.Action("UserStoryDetails", new { id = story.Id });
                                        ViewData["Title"] = "View Details";
                                    }
                                    <partial name="Components/_Button" view-data="ViewData" />
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                    ViewData["Icon"] = "fas fa-list";
                    ViewData["Title"] = "No User Stories";
                    ViewData["Description"] = "This epic doesn't have any user stories yet.";
                    ViewData["ActionText"] = "Add First User Story";
                    ViewData["ActionIcon"] = "fas fa-plus";
                    ViewData["ActionHref"] = Url.Action("CreateUserStory", new { projectId = Model.ProjectId, epicId = Model.Id });
                <partial name="Components/_EmptyState" view-data="ViewData" />
            }
        </partial>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Epic Information -->
        @{
            ViewData["Title"] = "Epic Information";
            ViewData["Icon"] = "fas fa-info-circle";
            var infoItems = new List<object> {
                new { Label = "Epic Key", Value = Model.EpicKey, Classes = "font-mono" },
                new { Label = "Status", Value = Model.Status.ToString() },
                new { Label = "Priority", Value = Model.Priority.ToString() },
                new { Label = "Created", Value = Model.CreatedAt.ToString("MMM dd, yyyy") }
            };

            if (Model.Owner != null) {
                infoItems.Insert(3, new { Label = "Owner", Value = Model.Owner.UserName });
            }

            if (Model.UpdatedAt.HasValue) {
                infoItems.Add(new { Label = "Last Updated", Value = Model.UpdatedAt.Value.ToString("MMM dd, yyyy") });
            }

            if (Model.TargetDate.HasValue) {
                infoItems.Add(new { Label = "Target Date", Value = Model.TargetDate.Value.ToString("MMM dd, yyyy") });
            }

            ViewData["InfoItems"] = infoItems;
        }
        <partial name="Components/_InfoCard" view-data="ViewData" />

        <!-- Actions -->
        @{
            ViewData["Title"] = "Actions";
            ViewData["Icon"] = "fas fa-cog";
            ViewData["Actions"] = new List<object> {
                new { Text = "Edit Epic", Variant = "outline", Icon = "fas fa-edit", Href = Url.Action("EditEpic", new { id = Model.Id }) },
                new { Text = "Add User Story", Variant = "primary", Icon = "fas fa-plus", Href = Url.Action("CreateUserStory", new { projectId = Model.ProjectId, epicId = Model.Id }) },
                new {
                    Text = "Delete Epic",
                    Variant = "danger",
                    Icon = "fas fa-trash",
                    Type = "submit",
                    FormAction = Url.Action("DeleteEpic", new { id = Model.Id }),
                    ConfirmMessage = "Are you sure you want to delete this epic? This action cannot be undone."
                }
            };
        }
        <partial name="Components/_ActionCard" view-data="ViewData" />
    </div>
</div>
