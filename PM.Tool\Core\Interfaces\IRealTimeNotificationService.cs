namespace PM.Tool.Core.Interfaces
{
    public interface IRealTimeNotificationService
    {
        /// <summary>
        /// Sends real-time notification when a task is updated
        /// </summary>
        Task NotifyTaskUpdatedAsync(int taskId, string updateType, object updateData, string updatedByUserId);

        /// <summary>
        /// Sends real-time notification when a task is assigned to a user
        /// </summary>
        Task NotifyTaskAssignedAsync(int taskId, string assignedToUserId, string assignedByUserId);

        /// <summary>
        /// Sends real-time notification when a comment is added to a task
        /// </summary>
        Task NotifyCommentAddedAsync(int taskId, string commentContent, string authorUserId);

        /// <summary>
        /// Sends real-time notification when a user is mentioned
        /// </summary>
        Task NotifyMentionAsync(string mentionedUserId, string mentionerUserId, string content, int? taskId = null, int? projectId = null);

        /// <summary>
        /// Sends real-time notification when a project is updated
        /// </summary>
        Task NotifyProjectUpdateAsync(int projectId, string updateType, object updateData, string updatedByUserId);

        /// <summary>
        /// Broadcasts user activity to project members
        /// </summary>
        Task NotifyUserActivityAsync(string userId, string activity, int? projectId = null, int? taskId = null);

        /// <summary>
        /// Broadcasts a message to all members of a project
        /// </summary>
        Task BroadcastToProjectAsync(int projectId, string method, object data);

        /// <summary>
        /// Sends a direct notification to a specific user
        /// </summary>
        Task SendDirectNotificationAsync(string userId, string title, string message, string type = "info", object? data = null);
    }
}
