using Microsoft.AspNetCore.DataProtection;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Reflection;

namespace PM.Tool.Application.Services
{
    public interface IDataEncryptionService
    {
        /// <summary>
        /// Encrypt sensitive string data
        /// </summary>
        string EncryptString(string plainText);

        /// <summary>
        /// Decrypt sensitive string data
        /// </summary>
        string DecryptString(string cipherText);

        /// <summary>
        /// Encrypt object data as JSON
        /// </summary>
        string EncryptObject<T>(T obj);

        /// <summary>
        /// Decrypt object data from JSON
        /// </summary>
        T? DecryptObject<T>(string cipherText);

        /// <summary>
        /// Hash sensitive data for searching (one-way)
        /// </summary>
        string HashForSearch(string data);

        /// <summary>
        /// Verify hashed data
        /// </summary>
        bool VerifyHash(string data, string hash);

        /// <summary>
        /// Generate secure random key
        /// </summary>
        string GenerateSecureKey(int length = 32);

        /// <summary>
        /// Encrypt file data
        /// </summary>
        byte[] EncryptBytes(byte[] data);

        /// <summary>
        /// Decrypt file data
        /// </summary>
        byte[] DecryptBytes(byte[] encryptedData);
    }

    public class DataEncryptionService : IDataEncryptionService
    {
        private readonly IDataProtector _dataProtector;
        private readonly ILogger<DataEncryptionService> _logger;
        private readonly string _encryptionKey;

        public DataEncryptionService(
            IDataProtectionProvider dataProtectionProvider,
            IConfiguration configuration,
            ILogger<DataEncryptionService> logger)
        {
            _dataProtector = dataProtectionProvider.CreateProtector("PM.Tool.DataEncryption");
            _logger = logger;
            _encryptionKey = configuration["DataEncryption:Key"] ?? GenerateSecureKey();
        }

        public string EncryptString(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                return plainText;

            try
            {
                return _dataProtector.Protect(plainText);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to encrypt string data");
                throw new InvalidOperationException("Encryption failed", ex);
            }
        }

        public string DecryptString(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
                return cipherText;

            try
            {
                return _dataProtector.Unprotect(cipherText);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to decrypt string data");
                throw new InvalidOperationException("Decryption failed", ex);
            }
        }

        public string EncryptObject<T>(T obj)
        {
            if (obj == null)
                return string.Empty;

            try
            {
                var json = JsonSerializer.Serialize(obj);
                return EncryptString(json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to encrypt object data");
                throw new InvalidOperationException("Object encryption failed", ex);
            }
        }

        public T? DecryptObject<T>(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
                return default(T);

            try
            {
                var json = DecryptString(cipherText);
                return JsonSerializer.Deserialize<T>(json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to decrypt object data");
                throw new InvalidOperationException("Object decryption failed", ex);
            }
        }

        public string HashForSearch(string data)
        {
            if (string.IsNullOrEmpty(data))
                return string.Empty;

            try
            {
                using var sha256 = SHA256.Create();
                var saltedData = data + _encryptionKey; // Add salt for security
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedData));
                return Convert.ToBase64String(hashBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to hash data for search");
                throw new InvalidOperationException("Hashing failed", ex);
            }
        }

        public bool VerifyHash(string data, string hash)
        {
            if (string.IsNullOrEmpty(data) || string.IsNullOrEmpty(hash))
                return false;

            try
            {
                var computedHash = HashForSearch(data);
                return computedHash.Equals(hash, StringComparison.Ordinal);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to verify hash");
                return false;
            }
        }

        public string GenerateSecureKey(int length = 32)
        {
            try
            {
                using var rng = RandomNumberGenerator.Create();
                var keyBytes = new byte[length];
                rng.GetBytes(keyBytes);
                return Convert.ToBase64String(keyBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate secure key");
                throw new InvalidOperationException("Key generation failed", ex);
            }
        }

        public byte[] EncryptBytes(byte[] data)
        {
            if (data == null || data.Length == 0)
                return data;

            try
            {
                using var aes = Aes.Create();
                aes.Key = Convert.FromBase64String(_encryptionKey.PadRight(44, '=')[..44]); // Ensure 32 bytes
                aes.GenerateIV();

                using var encryptor = aes.CreateEncryptor();
                using var msEncrypt = new MemoryStream();
                
                // Prepend IV to encrypted data
                msEncrypt.Write(aes.IV, 0, aes.IV.Length);
                
                using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                {
                    csEncrypt.Write(data, 0, data.Length);
                }

                return msEncrypt.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to encrypt byte data");
                throw new InvalidOperationException("Byte encryption failed", ex);
            }
        }

        public byte[] DecryptBytes(byte[] encryptedData)
        {
            if (encryptedData == null || encryptedData.Length == 0)
                return encryptedData;

            try
            {
                using var aes = Aes.Create();
                aes.Key = Convert.FromBase64String(_encryptionKey.PadRight(44, '=')[..44]); // Ensure 32 bytes

                // Extract IV from the beginning of encrypted data
                var iv = new byte[aes.BlockSize / 8];
                Array.Copy(encryptedData, 0, iv, 0, iv.Length);
                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(encryptedData, iv.Length, encryptedData.Length - iv.Length);
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var msResult = new MemoryStream();
                
                csDecrypt.CopyTo(msResult);
                return msResult.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to decrypt byte data");
                throw new InvalidOperationException("Byte decryption failed", ex);
            }
        }
    }

    /// <summary>
    /// Attribute to mark properties for encryption
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class EncryptedAttribute : Attribute
    {
        public bool SearchableHash { get; set; } = false;
    }

    /// <summary>
    /// Helper class for automatic encryption/decryption of entity properties
    /// </summary>
    public static class EncryptionHelper
    {
        public static void EncryptEntity<T>(T entity, IDataEncryptionService encryptionService) where T : class
        {
            if (entity == null) return;

            var properties = typeof(T).GetProperties()
                .Where(p => p.GetCustomAttributes(typeof(EncryptedAttribute), false).Any());

            foreach (var property in properties)
            {
                var value = property.GetValue(entity) as string;
                if (!string.IsNullOrEmpty(value))
                {
                    var encryptedValue = encryptionService.EncryptString(value);
                    property.SetValue(entity, encryptedValue);

                    // Handle searchable hash if needed
                    var attribute = property.GetCustomAttribute<EncryptedAttribute>();
                    if (attribute?.SearchableHash == true)
                    {
                        var hashProperty = typeof(T).GetProperty($"{property.Name}Hash");
                        if (hashProperty != null)
                        {
                            var hash = encryptionService.HashForSearch(value);
                            hashProperty.SetValue(entity, hash);
                        }
                    }
                }
            }
        }

        public static void DecryptEntity<T>(T entity, IDataEncryptionService encryptionService) where T : class
        {
            if (entity == null) return;

            var properties = typeof(T).GetProperties()
                .Where(p => p.GetCustomAttributes(typeof(EncryptedAttribute), false).Any());

            foreach (var property in properties)
            {
                var value = property.GetValue(entity) as string;
                if (!string.IsNullOrEmpty(value))
                {
                    try
                    {
                        var decryptedValue = encryptionService.DecryptString(value);
                        property.SetValue(entity, decryptedValue);
                    }
                    catch
                    {
                        // If decryption fails, the data might not be encrypted yet
                        // This handles migration scenarios
                    }
                }
            }
        }
    }
}
