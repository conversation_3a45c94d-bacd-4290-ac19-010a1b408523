@model PM.Tool.Core.Entities.Agile.Epic
@{
    ViewData["Title"] = $"Edit Epic: {Model.Title}";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;

    // Standardized Page Header
    ViewData["PageTitle"] = $"Edit Epic: {Model.Title}";
    ViewData["PageSubtitle"] = $"Epic {Model.EpicKey} in {project?.Name}";
    ViewData["PageIcon"] = "fas fa-edit";
    ViewData["Breadcrumbs"] = new List<object> {
        new { Text = "Projects", Icon = "fas fa-project-diagram", Href = Url.Action("Index", "Projects") },
        new { Text = project?.Name, Href = Url.Action("Details", "Projects", new { id = project?.Id }) },
        new { Text = "Epics", Href = Url.Action("Epics", new { projectId = project?.Id }) },
        new { Text = Model.EpicKey, Href = Url.Action("EpicDetails", new { id = Model.Id }) },
        new { Text = "Edit", IsActive = true }
    };
    ViewData["HeaderActions"] = new List<object> {
        new { Text = "View Epic", Variant = "outline", Icon = "fas fa-eye", Href = Url.Action("EpicDetails", new { id = Model.Id }) }
    };
}

<!-- Standardized Page Header -->
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Form -->
<div class="max-w-4xl">
    <form asp-action="EditEpic" asp-route-id="@Model.Id" method="post" class="space-y-8">
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.Id)
        @Html.HiddenFor(m => m.ProjectId)
        @Html.HiddenFor(m => m.EpicKey)
        @Html.HiddenFor(m => m.CreatedAt)
        @Html.HiddenFor(m => m.OwnerId)
        
        <!-- Basic Information -->
        @{
            ViewData["Title"] = "Basic Information";
            ViewData["Icon"] = "fas fa-info-circle";
            ViewData["GridCols"] = "grid-cols-1 md:grid-cols-2";
            ViewData["Fields"] = new List<object> {
                new {
                    Name = "Title",
                    Label = "Epic Title",
                    Value = Model.Title,
                    Placeholder = "Enter a descriptive title for the epic",
                    Required = true,
                    ColSpan = "md:col-span-2"
                },
                new {
                    Name = "Status",
                    Label = "Status",
                    Type = "select",
                    Value = Model.Status.ToString(),
                    Options = ViewBag.EpicStatuses
                },
                new {
                    Name = "Priority",
                    Label = "Priority",
                    Type = "select",
                    Value = Model.Priority.ToString(),
                    Options = ViewBag.EpicPriorities
                },
                new {
                    Name = "TargetDate",
                    Label = "Target Date",
                    Type = "date",
                    Value = Model.TargetDate?.ToString("yyyy-MM-dd") ?? ""
                },
                new {
                    Name = "EstimatedStoryPoints",
                    Label = "Estimated Story Points",
                    Type = "number",
                    Value = Model.EstimatedStoryPoints.ToString(),
                    Placeholder = "0",
                    Attributes = new Dictionary<string, object> { { "step", "0.5" }, { "min", "0" } }
                }
            };
        }
        <partial name="Components/_FormSection" view-data="ViewData" />
        
        <!-- Description -->
        @{
            ViewData["Title"] = "Description";
            ViewData["Icon"] = "fas fa-align-left";
            ViewData["GridCols"] = "grid-cols-1";
            ViewData["Fields"] = new List<object> {
                new {
                    Name = "Description",
                    Label = "Epic Description",
                    Type = "textarea",
                    Rows = 6,
                    Value = Model.Description,
                    Placeholder = "Provide a detailed description of what this epic aims to achieve...",
                    Required = true,
                    Help = "Describe the epic's purpose, scope, and expected outcomes."
                }
            };
        }
        <partial name="Components/_FormSection" view-data="ViewData" />

        <!-- Acceptance Criteria -->
        @{
            ViewData["Title"] = "Acceptance Criteria";
            ViewData["Icon"] = "fas fa-check-circle";
            ViewData["GridCols"] = "grid-cols-1";
            ViewData["Fields"] = new List<object> {
                new {
                    Name = "AcceptanceCriteria",
                    Label = "Acceptance Criteria",
                    Type = "textarea",
                    Rows = 4,
                    Value = Model.AcceptanceCriteria,
                    Placeholder = "Define the criteria that must be met for this epic to be considered complete...",
                    Help = "List the specific conditions that must be satisfied for the epic to be marked as done."
                }
            };
        }
        <partial name="Components/_FormSection" view-data="ViewData" />

        <!-- Business Value -->
        @{
            ViewData["Title"] = "Business Value";
            ViewData["Icon"] = "fas fa-chart-line";
            ViewData["GridCols"] = "grid-cols-1";
            ViewData["Fields"] = new List<object> {
                new {
                    Name = "BusinessValue",
                    Label = "Business Value",
                    Type = "textarea",
                    Rows = 4,
                    Value = Model.BusinessValue,
                    Placeholder = "Explain the business value and benefits this epic will deliver...",
                    Help = "Describe how this epic contributes to business goals and what value it provides to users or stakeholders."
                }
            };
        }
        <partial name="Components/_FormSection" view-data="ViewData" />
        
        <!-- Additional Information -->
        @{
            ViewData["Title"] = "Additional Information";
            ViewData["Icon"] = "fas fa-tags";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Tags -->
                <div>
                    <label asp-for="Tags" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        Tags
                    </label>
                    <input asp-for="Tags" 
                           class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100"
                           placeholder="feature, enhancement, bug-fix" />
                    <span asp-validation-for="Tags" class="text-danger-600 text-sm"></span>
                    <p class="mt-1 text-xs text-neutral-500 dark:text-dark-400">
                        Separate tags with commas
                    </p>
                </div>
                
                <!-- Sort Order -->
                <div>
                    <label asp-for="SortOrder" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        Sort Order
                    </label>
                    <input asp-for="SortOrder" type="number" min="0"
                           class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100"
                           placeholder="0" />
                    <span asp-validation-for="SortOrder" class="text-danger-600 text-sm"></span>
                    <p class="mt-1 text-xs text-neutral-500 dark:text-dark-400">
                        Lower numbers appear first
                    </p>
                </div>
            </div>
        </partial>
        
        <!-- Form Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-neutral-200 dark:border-dark-200">
            @{
                ViewData["Text"] = "Cancel";
                ViewData["Variant"] = "outline";
                ViewData["Href"] = Url.Action("EpicDetails", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />
            
            @{
                ViewData["Text"] = "Save Changes";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-save";
                ViewData["Type"] = "submit";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
