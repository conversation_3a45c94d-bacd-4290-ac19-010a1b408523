@{
    var title = ViewData["Title"]?.ToString() ?? "";
    var icon = ViewData["Icon"]?.ToString() ?? "";
    var infoItems = ViewData["InfoItems"] as IEnumerable<object> ?? new List<object>();
}

<div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 rounded-lg p-6">
    @if (!string.IsNullOrEmpty(title))
    {
        <div class="flex items-center mb-4">
            @if (!string.IsNullOrEmpty(icon))
            {
                <i class="@icon text-neutral-600 dark:text-neutral-400 mr-2"></i>
            }
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100">@title</h3>
        </div>
    }

    <dl class="space-y-3">
        @foreach (dynamic item in infoItems)
        {
            <div class="flex justify-between items-start">
                <dt class="text-sm font-medium text-neutral-500 dark:text-neutral-400 flex-shrink-0 mr-4">@item.Label</dt>
                <dd class="text-sm text-neutral-900 dark:text-neutral-100 text-right @(item.Classes ?? "")">@item.Value</dd>
            </div>
        }
    </dl>
</div>
