@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model IEnumerable<PM.Tool.Core.Entities.Risk>
@{
    ViewData["Title"] = "Risk Analytics";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Risk Management", Href = Url.Action("Index", "Risk"), Icon = "fas fa-exclamation-triangle" },
        new { Text = "Analytics", Href = "", Icon = "fas fa-chart-bar" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Standardized Page Header -->
@{
    ViewData["Title"] = "Risk Analytics";
    ViewData["Description"] = "Comprehensive risk analysis and insights";
    ViewData["Icon"] = "fas fa-chart-bar";

    ViewData["Actions"] = new[] {
        new { Text = "Export Report", Href = (string?)null, Icon = "fas fa-download", Variant = "primary", OnClick = (string?)"exportReport()" },
        new { Text = "Refresh Data", Href = (string?)null, Icon = "fas fa-sync-alt", Variant = "outline", OnClick = (string?)"refreshAnalytics()" },
        new { Text = "Back to Risks", Href = Url.Action("Index"), Icon = "fas fa-arrow-left", Variant = "secondary", OnClick = (string?)null }
    };

    ViewData["Stats"] = new[] {
        new { Label = "Total Risks", Value = Model?.Count().ToString() ?? "0", Icon = "fas fa-exclamation-triangle", Color = "blue" },
        new { Label = "Critical", Value = Model?.Count(r => r.RiskLevel == PM.Tool.Core.Entities.RiskLevel.Critical).ToString() ?? "0", Icon = "fas fa-exclamation-circle", Color = "red" },
        new { Label = "High", Value = Model?.Count(r => r.RiskLevel == PM.Tool.Core.Entities.RiskLevel.High).ToString() ?? "0", Icon = "fas fa-arrow-up", Color = "amber" },
        new { Label = "Mitigated", Value = Model?.Count(r => r.Status == PM.Tool.Core.Entities.RiskStatus.Mitigating).ToString() ?? "0", Icon = "fas fa-shield-alt", Color = "green" }
    };
}
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Analytics Filters -->
@{
    ViewData["Title"] = "Analytics Filters";
    ViewData["Icon"] = "fas fa-filter";
    ViewData["BodyContent"] = @"
        <div class='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div>
                <label for='projectFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Project</label>
                <select id='projectFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Projects</option>
                </select>
            </div>
            <div>
                <label for='dateRange' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Date Range</label>
                <select id='dateRange' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value='30'>Last 30 Days</option>
                    <option value='90' selected>Last 90 Days</option>
                    <option value='180'>Last 6 Months</option>
                    <option value='365'>Last Year</option>
                    <option value='all'>All Time</option>
                </select>
            </div>
            <div>
                <label for='statusFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Status</label>
                <select id='statusFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Statuses</option>
                    <option value='Identified'>Identified</option>
                    <option value='Analyzing'>Analyzing</option>
                    <option value='Mitigating'>Mitigating</option>
                    <option value='Resolved'>Resolved</option>
                </select>
            </div>
            <div class='flex items-end'>
                <button id='refreshAnalytics' class='w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors' onclick='loadAnalyticsData()'>
                    <i class='fas fa-sync-alt mr-2'></i>
                    Refresh
                </button>
            </div>
        </div>
    ";
}
<div class="mb-8">
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Key Metrics -->
<div id="metricsContainer" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <!-- Loading State -->
    <div id="metricsLoading" class="col-span-full">
        @{
            ViewData["Type"] = "skeleton";
            ViewData["Lines"] = 1;
            ViewData["ShowAvatar"] = false;
        }
        <partial name="Components/_Loading" view-data="ViewData" />
    </div>

    <!-- Metrics Cards (will be populated by JavaScript) -->
    <div id="totalRisksCard" class="hidden">
        @{
            ViewData["Title"] = "Total Risks";
            ViewData["Icon"] = "fas fa-exclamation-triangle";
            ViewData["IconColor"] = "bg-gradient-to-br from-primary-500 to-primary-600";
            ViewData["Value"] = "0";
            ViewData["Description"] = "All identified risks";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />
    </div>

    <div id="highCriticalCard" class="hidden">
        @{
            ViewData["Title"] = "High/Critical";
            ViewData["Icon"] = "fas fa-fire";
            ViewData["IconColor"] = "bg-gradient-to-br from-danger-500 to-danger-600";
            ViewData["Value"] = "0";
            ViewData["Description"] = "Require immediate attention";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />
    </div>

    <div id="openRisksCard" class="hidden">
        @{
            ViewData["Title"] = "Open Risks";
            ViewData["Icon"] = "fas fa-folder-open";
            ViewData["IconColor"] = "bg-gradient-to-br from-warning-500 to-warning-600";
            ViewData["Value"] = "0";
            ViewData["Description"] = "Active risks being managed";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />
    </div>

    <div id="avgScoreCard" class="hidden">
        @{
            ViewData["Title"] = "Avg Risk Score";
            ViewData["Icon"] = "fas fa-calculator";
            ViewData["IconColor"] = "bg-gradient-to-br from-info-500 to-info-600";
            ViewData["Value"] = "0";
            ViewData["Description"] = "Average risk impact";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />
    </div>
</div>

<!-- Empty State -->
<div id="emptyState" class="hidden text-center py-12">
    <div class="mx-auto h-24 w-24 text-neutral-400 mb-4">
        <i class="fas fa-chart-bar text-6xl"></i>
    </div>
    <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Risk Data Available</h3>
    <p class="text-neutral-600 dark:text-dark-400 mb-6">There are no risks to analyze for the selected criteria.</p>
    @{
        ViewData["Text"] = "Create First Risk";
        ViewData["Variant"] = "primary";
        ViewData["Icon"] = "fas fa-plus";
        ViewData["Href"] = Url.Action("Create");
    }
    <partial name="Components/_Button" view-data="ViewData" />
</div>

<!-- Charts Row 1 -->
<div id="chartsContainer" class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Risk Distribution by Level -->
    @{
        ViewData["Title"] = "Risk Distribution by Level";
        ViewData["Icon"] = "fas fa-chart-pie";
        ViewData["BodyContent"] = @"
            <div class='h-80 relative'>
                <div id='riskLevelChartLoading' class='absolute inset-0 flex items-center justify-center'>
                    <div class='text-center'>
                        <div class='animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2'></div>
                        <p class='text-sm text-neutral-600 dark:text-dark-400'>Loading chart...</p>
                    </div>
                </div>
                <canvas id='riskLevelChart' class='hidden'></canvas>
                <div id='riskLevelChartEmpty' class='hidden h-full flex items-center justify-center'>
                    <div class='text-center'>
                        <i class='fas fa-chart-pie text-4xl text-neutral-400 mb-2'></i>
                        <p class='text-sm text-neutral-600 dark:text-dark-400'>No data available</p>
                    </div>
                </div>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Risk Status Distribution -->
    @{
        ViewData["Title"] = "Risk Status Distribution";
        ViewData["Icon"] = "fas fa-chart-donut";
        ViewData["BodyContent"] = @"
            <div class='h-80 relative'>
                <div id='riskStatusChartLoading' class='absolute inset-0 flex items-center justify-center'>
                    <div class='text-center'>
                        <div class='animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2'></div>
                        <p class='text-sm text-neutral-600 dark:text-dark-400'>Loading chart...</p>
                    </div>
                </div>
                <canvas id='riskStatusChart' class='hidden'></canvas>
                <div id='riskStatusChartEmpty' class='hidden h-full flex items-center justify-center'>
                    <div class='text-center'>
                        <i class='fas fa-chart-donut text-4xl text-neutral-400 mb-2'></i>
                        <p class='text-sm text-neutral-600 dark:text-dark-400'>No data available</p>
                    </div>
                </div>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Charts Row 2 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Risk Category Analysis -->
    @{
        ViewData["Title"] = "Risk Category Analysis";
        ViewData["Icon"] = "fas fa-chart-bar";
        ViewData["BodyContent"] = @"
            <div class='h-80'>
                <canvas id='riskCategoryChart'></canvas>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Risk Trend Over Time -->
    @{
        ViewData["Title"] = "Risk Trend Over Time";
        ViewData["Icon"] = "fas fa-chart-line";
        ViewData["BodyContent"] = @"
            <div class='h-80'>
                <canvas id='riskTrendChart'></canvas>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Risk Heat Map -->
@{
    ViewData["Title"] = "Risk Heat Map (Probability vs Impact)";
    ViewData["Icon"] = "fas fa-fire";
    ViewData["BodyContent"] = @"
        <div class='h-96'>
            <canvas id='riskHeatMapChart'></canvas>
        </div>
    ";
}
<div class="mb-8">
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Top Risks Table -->
@{
    ViewData["Title"] = "Top 10 Highest Risk Items";
    ViewData["Icon"] = "fas fa-list-ol";
    ViewData["BodyContent"] = @"
        <div class='overflow-x-auto'>
            <table class='min-w-full divide-y divide-neutral-200 dark:divide-dark-600'>
                <thead class='bg-neutral-50 dark:bg-dark-700'>
                    <tr>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Risk</th>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Score</th>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Level</th>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Status</th>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Category</th>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Actions</th>
                    </tr>
                </thead>
                <tbody id='topRisksTable' class='bg-white dark:bg-dark-800 divide-y divide-neutral-200 dark:divide-dark-600'>
                    <!-- Table content will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
    ";
}
<partial name="Components/_Card" view-data="ViewData" />

@section Scripts {
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let analyticsData = null;
        let charts = {};
        let currentProjectId = @(ViewBag.ProjectId ?? "null");

        $(document).ready(function() {
            loadProjects();
            setupFilters();
            initializeCharts();
            loadAnalyticsData();
        });

        function loadAnalyticsData() {
            showLoadingStates();

            const projectId = $('#projectFilter').val() || currentProjectId;
            const url = '@Url.Action("GetRiskAnalytics", "Risk")' + (projectId ? `?projectId=${projectId}` : '');

            $.get(url)
                .done(function(data) {
                    if (data.error) {
                        showErrorState(data.error);
                        return;
                    }

                    analyticsData = data;

                    if (!data.AllRisks || data.AllRisks.length === 0) {
                        showEmptyState();
                    } else {
                        hideEmptyState();
                        updateMetrics(data.Summary);
                        updateCharts(data);
                        populateTopRisksTable(data.TopRisks);
                        loadTrendData();
                    }

                    hideLoadingStates();
                })
                .fail(function(xhr, status, error) {
                    console.error('Failed to load analytics data:', error);
                    showErrorState('Failed to load analytics data. Please try again.');
                    hideLoadingStates();
                });
        }

        function loadTrendData() {
            const projectId = $('#projectFilter').val() || currentProjectId;
            const days = $('#dateRange').val() || 30;
            const url = '@Url.Action("GetRiskTrendData", "Risk")' + `?projectId=${projectId}&days=${days}`;

            $.get(url)
                .done(function(data) {
                    if (data.error) {
                        console.error('Failed to load trend data:', data.error);
                        return;
                    }
                    updateTrendChart(data);
                })
                .fail(function(xhr, status, error) {
                    console.error('Failed to load trend data:', error);
                });
        }

        function loadProjects() {
            $.get('@Url.Action("GetProjects", "Projects")')
                .done(function(data) {
                    const select = $('#projectFilter');
                    if (data && Array.isArray(data)) {
                        data.forEach(function(project) {
                            if (project.id != currentProjectId) {
                                select.append(`<option value="${project.id}">${project.name}</option>`);
                            }
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function setupFilters() {
            $('#projectFilter, #dateRange, #statusFilter').on('change', loadAnalyticsData);
        }

        function getFilteredRisks() {
            const projectFilter = $('#projectFilter').val();
            const dateRange = $('#dateRange').val();
            const statusFilter = $('#statusFilter').val();

            let filtered = allRisks.filter(risk => {
                if (projectFilter && risk.ProjectId != projectFilter) return false;
                if (statusFilter && risk.Status !== statusFilter) return false;

                if (dateRange !== 'all') {
                    const days = parseInt(dateRange);
                    const cutoffDate = new Date();
                    cutoffDate.setDate(cutoffDate.getDate() - days);
                    const riskDate = new Date(risk.CreatedAt);
                    if (riskDate < cutoffDate) return false;
                }

                return true;
            });

            return filtered;
        }

        function showLoadingStates() {
            $('#metricsLoading').removeClass('hidden');
            $('#totalRisksCard, #highCriticalCard, #openRisksCard, #avgScoreCard').addClass('hidden');
            $('#riskLevelChartLoading, #riskStatusChartLoading').removeClass('hidden');
            $('#riskLevelChart, #riskStatusChart').addClass('hidden');
            $('#emptyState').addClass('hidden');
        }

        function hideLoadingStates() {
            $('#metricsLoading').addClass('hidden');
            $('#totalRisksCard, #highCriticalCard, #openRisksCard, #avgScoreCard').removeClass('hidden');
            $('#riskLevelChartLoading, #riskStatusChartLoading').addClass('hidden');
            $('#riskLevelChart, #riskStatusChart').removeClass('hidden');
        }

        function showEmptyState() {
            $('#emptyState').removeClass('hidden');
            $('#metricsContainer, #chartsContainer').addClass('hidden');
        }

        function hideEmptyState() {
            $('#emptyState').addClass('hidden');
            $('#metricsContainer, #chartsContainer').removeClass('hidden');
        }

        function showErrorState(message) {
            if (typeof showErrorAlert === 'function') {
                showErrorAlert(message, 'Error', 0);
            } else {
                alert('Error: ' + message);
            }
        }

        function updateMetrics(summary) {
            $('#totalRisksCard .text-2xl').text(summary.TotalRisks || 0);
            $('#highCriticalCard .text-2xl').text(summary.HighCriticalRisks || 0);
            $('#openRisksCard .text-2xl').text(summary.OpenRisks || 0);
            $('#avgScoreCard .text-2xl').text(summary.AverageRiskScore || 0);
        }

        function initializeCharts() {
            // Risk Level Chart
            const riskLevelCtx = document.getElementById('riskLevelChart').getContext('2d');
            charts.riskLevel = new Chart(riskLevelCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Low', 'Medium', 'High', 'Critical'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: ['#10b981', '#f59e0b', '#f97316', '#ef4444'],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Risk Status Chart
            const riskStatusCtx = document.getElementById('riskStatusChart').getContext('2d');
            charts.riskStatus = new Chart(riskStatusCtx, {
                type: 'pie',
                data: {
                    labels: ['Identified', 'Analyzing', 'Mitigating', 'Resolved'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: ['#6b7280', '#3b82f6', '#f59e0b', '#10b981'],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Risk Category Chart
            const riskCategoryCtx = document.getElementById('riskCategoryChart').getContext('2d');
            charts.riskCategory = new Chart(riskCategoryCtx, {
                type: 'bar',
                data: {
                    labels: ['Technical', 'Business', 'External', 'Organizational', 'Project Management'],
                    datasets: [{
                        label: 'Number of Risks',
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: '#3b82f6',
                        borderColor: '#1d4ed8',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

            // Risk Trend Chart
            const riskTrendCtx = document.getElementById('riskTrendChart').getContext('2d');
            charts.riskTrend = new Chart(riskTrendCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'New Risks',
                        data: [],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Resolved Risks',
                        data: [],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

            // Risk Heat Map Chart
            const riskHeatMapCtx = document.getElementById('riskHeatMapChart').getContext('2d');
            charts.riskHeatMap = new Chart(riskHeatMapCtx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Risks',
                        data: [],
                        backgroundColor: function(context) {
                            if (context.parsed) {
                                const score = context.parsed.x * context.parsed.y;
                                if (score >= 20) return '#ef4444';
                                if (score >= 15) return '#f97316';
                                if (score >= 10) return '#f59e0b';
                                return '#10b981';
                            }
                            return '#10b981';
                        },
                        borderColor: '#ffffff',
                        borderWidth: 2,
                        pointRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Probability'
                            },
                            min: 0,
                            max: 6,
                            ticks: {
                                stepSize: 1
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Impact'
                            },
                            min: 0,
                            max: 6,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const risk = context.raw.risk;
                                    return `${risk.Title} (Score: ${risk.RiskScore})`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function updateCharts(data) {
            // Update Risk Level Chart
            const levelDistribution = data.LevelDistribution || {};
            const levelCounts = {
                'Low': levelDistribution.Low || 0,
                'Medium': levelDistribution.Medium || 0,
                'High': levelDistribution.High || 0,
                'Critical': levelDistribution.Critical || 0
            };

            if (Object.values(levelCounts).every(count => count === 0)) {
                $('#riskLevelChart').addClass('hidden');
                $('#riskLevelChartEmpty').removeClass('hidden');
            } else {
                $('#riskLevelChart').removeClass('hidden');
                $('#riskLevelChartEmpty').addClass('hidden');
                charts.riskLevel.data.datasets[0].data = Object.values(levelCounts);
                charts.riskLevel.update();
            }

            // Update Risk Status Chart - calculate from AllRisks
            const allRisks = data.AllRisks || [];
            const statusCounts = {
                'Identified': allRisks.filter(r => r.Status === 'Identified').length,
                'Analyzing': allRisks.filter(r => r.Status === 'Analyzing').length,
                'Mitigating': allRisks.filter(r => r.Status === 'Mitigating').length,
                'Resolved': allRisks.filter(r => r.Status === 'Resolved').length
            };

            if (Object.values(statusCounts).every(count => count === 0)) {
                $('#riskStatusChart').addClass('hidden');
                $('#riskStatusChartEmpty').removeClass('hidden');
            } else {
                $('#riskStatusChart').removeClass('hidden');
                $('#riskStatusChartEmpty').addClass('hidden');
                charts.riskStatus.data.datasets[0].data = Object.values(statusCounts);
                charts.riskStatus.update();
            }

            // Update Risk Category Chart
            const categoryCounts = {
                'Technical': risks.filter(r => r.Category === 'Technical').length,
                'Business': risks.filter(r => r.Category === 'Business').length,
                'External': risks.filter(r => r.Category === 'External').length,
                'Organizational': risks.filter(r => r.Category === 'Organizational').length,
                'ProjectManagement': risks.filter(r => r.Category === 'ProjectManagement').length
            };
            charts.riskCategory.data.datasets[0].data = Object.values(categoryCounts);
            charts.riskCategory.update();

            // Update Heat Map Chart
            const heatMapData = risks.map(risk => ({
                x: risk.Probability,
                y: risk.Impact,
                risk: risk
            }));
            charts.riskHeatMap.data.datasets[0].data = heatMapData;
            charts.riskHeatMap.update();

            // Update Trend Chart (simplified - would need more complex logic for real trend data)
            const last30Days = Array.from({length: 30}, (_, i) => {
                const date = new Date();
                date.setDate(date.getDate() - (29 - i));
                return date.toISOString().split('T')[0];
            });

            charts.riskTrend.data.labels = last30Days.map(date => new Date(date).toLocaleDateString());
            charts.riskTrend.data.datasets[0].data = last30Days.map(date =>
                risks.filter(r => r.CreatedAt === date).length
            );
            charts.riskTrend.data.datasets[1].data = last30Days.map(date =>
                risks.filter(r => r.Status === 'Resolved' && r.CreatedAt === date).length
            );
            charts.riskTrend.update();
        }

        function populateTopRisksTable(topRisks = []) {
            const tbody = $('#topRisksTable');
            tbody.empty();

            if (!topRisks || topRisks.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center text-neutral-500 dark:text-dark-400">
                            <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                            <div>No high-risk items found</div>
                        </td>
                    </tr>
                `);
                return;
            }

            topRisks.forEach(risk => {
                const riskLevelClass = getRiskLevelClass(risk.RiskLevel);
                const statusClass = getStatusClass(risk.Status);

                tbody.append(`
                    <tr class="hover:bg-neutral-50 dark:hover:bg-dark-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-neutral-900 dark:text-dark-100">${risk.Title}</div>
                            <div class="text-sm text-neutral-500 dark:text-dark-400 truncate max-w-xs">${risk.Description}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-lg font-bold text-neutral-900 dark:text-dark-100">${risk.RiskScore}</div>
                            <div class="text-xs text-neutral-500 dark:text-dark-400">${risk.Probability}×${risk.Impact}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${riskLevelClass}">
                                ${risk.RiskLevel}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">
                                ${risk.Status}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-900 dark:text-dark-100">
                            ${risk.Category}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="@Url.Action("Details", "Risk")/${risk.Id}" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300">
                                View
                            </a>
                        </td>
                    </tr>
                `);
            });
        }

        function getRiskLevelClass(level) {
            switch(level) {
                case 'Critical': return 'bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200';
                case 'High': return 'bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200';
                case 'Medium': return 'bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200';
                case 'Low': return 'bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200';
                default: return 'bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200';
            }
        }

        function getStatusClass(status) {
            switch(status) {
                case 'Identified': return 'bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200';
                case 'Analyzing': return 'bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200';
                case 'Mitigating': return 'bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200';
                case 'Resolved': return 'bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200';
                default: return 'bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200';
            }
        }

        function updateTrendChart(trendData) {
            if (!charts.riskTrend || !trendData || trendData.length === 0) {
                return;
            }

            const labels = trendData.map(item => new Date(item.Date).toLocaleDateString());
            const createdData = trendData.map(item => item.Created);
            const resolvedData = trendData.map(item => item.Resolved);

            charts.riskTrend.data.labels = labels;
            charts.riskTrend.data.datasets[0].data = createdData;
            charts.riskTrend.data.datasets[1].data = resolvedData;
            charts.riskTrend.update();
        }

        function exportReport() {
            try {
                // Implementation for exporting analytics report
                const projectId = $('#projectFilter').val() || currentProjectId;
                const url = '@Url.Action("ExportAnalytics", "Risk")' + (projectId ? `?projectId=${projectId}` : '');
                window.open(url, '_blank');
            } catch (error) {
                console.error('Error in exportReport:', error);
            }
        }

        function refreshAnalytics() {
            try {
                // Implementation for refreshing analytics data
                console.log('Refresh analytics requested');
                location.reload();
            } catch (error) {
                console.error('Error in refreshAnalytics:', error);
            }
        }
    </script>
}
