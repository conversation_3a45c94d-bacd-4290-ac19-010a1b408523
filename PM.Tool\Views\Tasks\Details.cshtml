@using PM.Tool.Core.Helpers;

@model TaskDetailsViewModel

@{
    ViewData["Title"] = $"Task: {Model.Title}";
}

@section Styles {
    <partial name="Partials/_TaskDetailsStyles" />
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-tasks mr-3 text-primary-600 dark:text-primary-400"></i>
                @Model.Title
            </h1>
            <div class="mt-1 space-y-1">
                <p class="text-sm text-neutral-500 dark:text-dark-400">
                    <i class="fas fa-project-diagram mr-1"></i>
                    <a href="@Url.Action("Details", "Projects", new { id = Model.ProjectId })"
                       class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                        @Model.ProjectName
                    </a>
                </p>
                @if (Model.ParentTaskId.HasValue)
                {
                    <p class="text-sm text-neutral-500 dark:text-dark-400">
                        <i class="fas fa-level-up-alt mr-1"></i>
                        <a href="@Url.Action("Details", "Tasks", new { id = Model.ParentTaskId })"
                           class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                            Parent: @Model.ParentTaskTitle
                        </a>
                    </p>
                }
            </div>
        </div>
        <div class="flex space-x-3">
            @if (Model.CanEdit)
            {
                ViewData["Text"] = "Edit Task";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });
                <partial name="Components/_Button" view-data="ViewData" />
            }
            @if (Model.CanDelete)
            {
                ViewData["Text"] = "Delete";
                ViewData["Variant"] = "danger";
                ViewData["Icon"] = "fas fa-trash";
                ViewData["Href"] = "#";
                ViewData["OnClick"] = "showDeleteModal()";
                <partial name="Components/_Button" view-data="ViewData" />
            }
        </div>
    </div>
</div>

<!-- Task Overview Card - Full Width -->
<div class="card-custom mb-8">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Task Overview</h3>
            </div>
        </div>
    </div>
            <div class="card-body-custom">
                <!-- Status and Priority Badges -->
                <div class="flex flex-wrap items-center gap-3 mb-8">
                    @{
                        var statusClass = Model.Status.ToString().ToLower() switch {
                            "done" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                            "inprogress" => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                            "inreview" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                            _ => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200"
                        };

                        var priorityClass = Model.Priority.ToString().ToLower() switch {
                            "critical" => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                            "high" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                            "medium" => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                            "low" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                            _ => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200"
                        };
                    }

                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium @statusClass">
                        <i class="fas fa-circle mr-2 text-xs"></i>
                        @Model.Status
                    </span>

                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium @priorityClass">
                        <i class="fas fa-exclamation-triangle mr-2 text-xs"></i>
                        @Model.Priority Priority
                    </span>

                    @if (Model.IsOverdue)
                    {
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200">
                            <i class="fas fa-clock mr-2 text-xs"></i>
                            Overdue
                        </span>
                    }
                </div>

                <!-- Description -->
                @if (!string.IsNullOrEmpty(Model.Description))
                {
                    <div class="mb-6">
                        <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-3">Description</h4>
                        <div class="prose prose-neutral dark:prose-invert max-w-none">
                            @Html.Raw(MarkdownHelper.FormatMarkdown(Model.Description))
                        </div>
                    </div>
                }

                <!-- Task Metadata - Compact Layout -->
                <div class="space-y-6">
                    <!-- Key Information Grid -->
                    <div class="grid grid-cols-2 md:grid-cols-4 xl:grid-cols-6 gap-4">
                        <!-- Start Date -->
                        <div class="metadata-card flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                            <div class="metadata-icon w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-calendar-alt text-blue-600 dark:text-blue-400 text-sm"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <p class="text-xs font-medium text-neutral-500 dark:text-dark-400">Start Date</p>
                                <p class="text-sm font-semibold text-neutral-900 dark:text-dark-100 truncate">
                                    @(Model.StartDate?.ToString("MMM dd, yyyy") ?? "Not set")
                                </p>
                            </div>
                        </div>

                        <!-- Due Date -->
                        <div class="metadata-card flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                            <div class="metadata-icon w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-calendar-check text-orange-600 dark:text-orange-400 text-sm"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <p class="text-xs font-medium text-neutral-500 dark:text-dark-400">Due Date</p>
                                <p class="text-sm font-semibold text-neutral-900 dark:text-dark-100 truncate">
                                    @(Model.DueDate?.ToString("MMM dd, yyyy") ?? "Not set")
                                </p>
                            </div>
                        </div>

                        <!-- Estimated Hours -->
                        <div class="metadata-card flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                            <div class="metadata-icon w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-clock text-green-600 dark:text-green-400 text-sm"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <p class="text-xs font-medium text-neutral-500 dark:text-dark-400">Estimated</p>
                                <p class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@Model.EstimatedHours h</p>
                            </div>
                        </div>

                        <!-- Actual Hours -->
                        <div class="metadata-card flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                            <div class="metadata-icon w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-stopwatch text-purple-600 dark:text-purple-400 text-sm"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <p class="text-xs font-medium text-neutral-500 dark:text-dark-400">Actual</p>
                                <p class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@Model.ActualHours h</p>
                            </div>
                        </div>

                        <!-- Assigned To -->
                        <div class="metadata-card flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                            <div class="metadata-icon w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user text-indigo-600 dark:text-indigo-400 text-sm"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <p class="text-xs font-medium text-neutral-500 dark:text-dark-400">Assigned To</p>
                                <p class="text-sm font-semibold text-neutral-900 dark:text-dark-100 truncate">
                                    @(Model.AssignedToName ?? "Unassigned")
                                </p>
                            </div>
                        </div>

                        <!-- Project -->
                        <div class="metadata-card flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                            <div class="metadata-icon w-8 h-8 bg-teal-100 dark:bg-teal-900 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-project-diagram text-teal-600 dark:text-teal-400 text-sm"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <p class="text-xs font-medium text-neutral-500 dark:text-dark-400">Project</p>
                                <p class="text-sm font-semibold text-neutral-900 dark:text-dark-100 truncate">
                                    @Model.ProjectName
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Bar (if applicable) -->
                    @if (Model.EstimatedHours > 0 && Model.ActualHours > 0)
                    {
                        <div class="bg-neutral-50 dark:bg-dark-700 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">Time Progress</span>
                                @{
                                    var progressPercentage = Math.Min(100.0, (double)(Model.ActualHours / Model.EstimatedHours) * 100);
                                    var progressClass = progressPercentage > 100 ? "text-warning-600 dark:text-warning-400" : "text-primary-600 dark:text-primary-400";
                                }
                                <span class="text-sm font-semibold @progressClass">@progressPercentage.ToString("F1")%</span>
                            </div>
                            @{
                                var barClass = progressPercentage > 100 ? "bg-warning-500" : "bg-primary-500";
                            }
                            <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                                <div class="@barClass h-2 rounded-full transition-all duration-500 progress-bar" style="width: @Math.Min(100, progressPercentage)%"></div>
                            </div>
                        </div>
                    }

                    <!-- Additional Information -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-neutral-200 dark:border-dark-300">
                        <!-- Created By -->
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-user text-neutral-400 dark:text-dark-500"></i>
                            <div>
                                <p class="text-xs text-neutral-500 dark:text-dark-400">Created by</p>
                                <p class="text-sm font-medium text-neutral-900 dark:text-dark-100">@Model.CreatedByName</p>
                            </div>
                        </div>

                        <!-- Created Date -->
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-calendar-plus text-neutral-400 dark:text-dark-500"></i>
                            <div>
                                <p class="text-xs text-neutral-500 dark:text-dark-400">Created on</p>
                                <p class="text-sm font-medium text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy")</p>
                            </div>
                        </div>

                        <!-- Completed Date (if applicable) -->
                        @if (Model.CompletedDate.HasValue)
                        {
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-check-circle text-success-500"></i>
                                <div>
                                    <p class="text-xs text-neutral-500 dark:text-dark-400">Completed on</p>
                                    <p class="text-sm font-medium text-neutral-900 dark:text-dark-100">@Model.CompletedDate.Value.ToString("MMM dd, yyyy")</p>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
    <!-- Task Details -->
    <div class="xl:col-span-2 space-y-6">
        <!-- Subtasks Card -->
        @if (Model.SubTasks.Any())
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-list text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Subtasks</h3>
                            <p class="text-sm text-neutral-500 dark:text-dark-400">@Model.SubTasks.Count() subtask(s)</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-3">
                        @foreach (var subtask in Model.SubTasks)
                        {
                            <a href="@Url.Action("Details", new { id = subtask.Id })"
                               class="block p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-600 transition-colors">
                                <div class="flex items-start justify-between mb-2">
                                    <h4 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@subtask.Title</h4>
                                    <div class="flex items-center space-x-2">
                                        @{
                                            var subtaskStatusClass = subtask.Status.ToString().ToLower() switch {
                                                "done" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                                "inprogress" => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                                                "inreview" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                                _ => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200"
                                            };

                                            var subtaskPriorityClass = subtask.Priority.ToString().ToLower() switch {
                                                "critical" => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                                "high" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                                "medium" => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                                "low" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                                _ => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200"
                                            };
                                        }
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium @subtaskStatusClass">
                                            @subtask.Status
                                        </span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium @subtaskPriorityClass">
                                            @subtask.Priority
                                        </span>
                                    </div>
                                </div>
                                @if (!string.IsNullOrEmpty(subtask.Description))
                                {
                                    <p class="text-xs text-neutral-500 dark:text-dark-400 mb-2">@subtask.Description</p>
                                }
                                <p class="text-xs text-neutral-400 dark:text-dark-500">
                                    <i class="fas fa-user mr-1"></i>
                                    Assigned to: @(subtask.AssignedToName ?? "Unassigned")
                                </p>
                            </a>
                        }
                    </div>
                </div>
            </div>
        }

        <!-- Comments Card -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-comments text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Comments</h3>
                        <p class="text-sm text-neutral-500 dark:text-dark-400">@Model.Comments.Count() comment(s)</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                @if (Model.CanComment)
                {
                    <div class="mb-6">
                        <form id="addCommentForm" class="space-y-4">
                            @Html.AntiForgeryToken()
                            <input type="hidden" name="taskId" value="@Model.Id" />
                            <div>
                                <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-2">Add Comment</label>
                                <textarea name="content"
                                         class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg bg-white dark:bg-surface-dark text-neutral-900 dark:text-dark-100 placeholder-neutral-500 dark:placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                                         rows="3"
                                         placeholder="Write a comment... (Use @@username to mention someone)"
                                         data-mentions-enabled="true"
                                         data-mention-project-id="@Model.ProjectId"
                                         required></textarea>
                            </div>
                            <div class="flex justify-end">
                                @{
                                    ViewData["Text"] = "Add Comment";
                                    ViewData["Variant"] = "primary";
                                    ViewData["Icon"] = "fas fa-plus";
                                    ViewData["Type"] = "submit";
                                    ViewData["Href"] = null;
                                }
                                <partial name="Components/_Button" view-data="ViewData" />
                            </div>
                        </form>
                    </div>
                }

                <div id="commentsList" class="space-y-4">
                    @foreach (var comment in Model.Comments.OrderByDescending(c => c.CreatedAt))
                    {
                        <div class="comment p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg" data-comment-id="@comment.Id">
                            <div class="flex space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-semibold text-primary-600 dark:text-primary-400">
                                            @comment.UserName.First().ToString().ToUpper()
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <h4 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@comment.UserName</h4>
                                        <span class="text-xs text-neutral-500 dark:text-dark-400">
                                            @comment.CreatedAt.ToString("MMM dd, yyyy HH:mm")
                                            @if (comment.UpdatedAt.HasValue)
                                            {
                                                <span class="italic">(edited)</span>
                                            }
                                        </span>
                                    </div>
                                    <div class="prose prose-sm prose-neutral dark:prose-invert max-w-none">
                                        @Html.Raw(MarkdownHelper.FormatMarkdown(comment.Content))
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    @if (!Model.Comments.Any())
                    {
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-comments text-2xl text-neutral-400 dark:text-dark-500"></i>
                            </div>
                            <p class="text-neutral-500 dark:text-dark-400">No comments yet</p>
                            @if (Model.CanComment)
                            {
                                <p class="text-sm text-neutral-400 dark:text-dark-500 mt-1">Be the first to add a comment!</p>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>

    </div>

    <!-- Sidebar -->
    <div class="xl:col-span-1">
        <!-- Tabbed Interface -->
        <div class="card-custom">
            <div class="card-header-custom border-b border-neutral-200 dark:border-dark-300">
                <div class="flex space-x-1" role="tablist">
                    <button type="button"
                            class="tab-button active flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors"
                            data-tab="assignment"
                            onclick="switchTab('assignment')">
                        <i class="fas fa-user text-sm"></i>
                        <span>Assignment</span>
                    </button>
                    <button type="button"
                            class="tab-button flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors"
                            data-tab="history"
                            onclick="switchTab('history')">
                        <i class="fas fa-history text-sm"></i>
                        <span>History</span>
                        <span class="ml-1 px-2 py-0.5 text-xs bg-neutral-200 dark:bg-dark-600 rounded-full">@Model.History.Count()</span>
                    </button>
                </div>
            </div>
            <div class="card-body-custom">
                <!-- Assignment Tab Content -->
                <div id="assignment-tab" class="tab-content active">
                    @if (!string.IsNullOrEmpty(Model.AssignedToName))
                    {
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3 p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-primary-600 dark:text-primary-400"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@Model.AssignedToName</p>
                                    <p class="text-xs text-neutral-500 dark:text-dark-400">Assigned Team Member</p>
                                </div>
                                @if (Model.CanEdit)
                                {
                                    <button type="button" class="text-neutral-400 hover:text-neutral-600 dark:text-dark-500 dark:hover:text-dark-300">
                                        <i class="fas fa-edit text-sm"></i>
                                    </button>
                                }
                            </div>

                            <!-- Assignment Details -->
                            <div class="space-y-3">
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-neutral-500 dark:text-dark-400">Assigned Date</span>
                                    <span class="text-neutral-900 dark:text-dark-100 font-medium">@Model.CreatedAt.ToString("MMM dd, yyyy")</span>
                                </div>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-neutral-500 dark:text-dark-400">Status</span>
                                    <span class="text-neutral-900 dark:text-dark-100 font-medium">@Model.Status</span>
                                </div>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-neutral-500 dark:text-dark-400">Priority</span>
                                    <span class="text-neutral-900 dark:text-dark-100 font-medium">@Model.Priority</span>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-user-slash text-2xl text-neutral-400 dark:text-dark-500"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">Unassigned</h4>
                            <p class="text-sm text-neutral-500 dark:text-dark-400 mb-4">This task hasn't been assigned to anyone yet.</p>
                            @if (Model.CanEdit)
                            {
                                    ViewData["Text"] = "Assign Task";
                                    ViewData["Variant"] = "primary";
                                    ViewData["Size"] = "sm";
                                    ViewData["Icon"] = "fas fa-user-plus";
                                    ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });
                                <partial name="Components/_Button" view-data="ViewData" />
                            }
                        </div>
                    }
                </div>

                <!-- History Tab Content -->
                <div id="history-tab" class="tab-content hidden">
                    @if (Model.History.Any())
                    {
                        <!-- Timeline Visualization -->
                        <div class="mb-6">
                            <div class="relative">
                                <!-- Timeline Line -->
                                <div class="absolute left-6 top-0 bottom-0 w-0.5 bg-neutral-200 dark:bg-dark-300"></div>

                                <!-- Timeline Items -->
                                <div class="space-y-6">
                                    @foreach (var historyItem in Model.History.Take(8))
                                    {
                                        <div class="timeline-item relative flex items-start space-x-4">
                                            <!-- Timeline Dot -->
                                            <div class="relative z-10 flex-shrink-0">
                                                @{
                                                    var dotClass = historyItem.Action switch
                                                    {
                                                        "Create" => "bg-success-500 border-success-200",
                                                        "ChangeStatus" => "bg-primary-500 border-primary-200",
                                                        "Update" => "bg-warning-500 border-warning-200",
                                                        "Delete" => "bg-danger-500 border-danger-200",
                                                        "Assign" => "bg-info-500 border-info-200",
                                                        "Comment" => "bg-blue-500 border-blue-200",
                                                        "Attachment" => "bg-purple-500 border-purple-200",
                                                        _ => "bg-neutral-400 border-neutral-200"
                                                    };

                                                    var iconClass = historyItem.Action switch
                                                    {
                                                        "Create" => "fas fa-plus",
                                                        "ChangeStatus" => "fas fa-exchange-alt",
                                                        "Update" => "fas fa-edit",
                                                        "Delete" => "fas fa-trash",
                                                        "Assign" => "fas fa-user-tag",
                                                        "Comment" => "fas fa-comment",
                                                        "Attachment" => "fas fa-paperclip",
                                                        _ => "fas fa-circle"
                                                    };
                                                }
                                                <div class="timeline-dot w-12 h-12 rounded-full border-4 @dotClass flex items-center justify-center">
                                                    <i class="@iconClass text-white text-xs"></i>
                                                </div>
                                            </div>

                                            <!-- Timeline Content -->
                                            <div class="flex-1 min-w-0 pb-4">
                                                <div class="timeline-content bg-white dark:bg-dark-700 rounded-lg border border-neutral-200 dark:border-dark-300 p-4 shadow-sm">
                                                    <div class="flex items-center justify-between mb-2">
                                                        <h4 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">
                                                            @historyItem.UserName
                                                        </h4>
                                                        <span class="text-xs text-neutral-500 dark:text-dark-400">
                                                            @historyItem.CreatedAt.ToString("MMM dd, HH:mm")
                                                        </span>
                                                    </div>
                                                    <p class="text-sm text-neutral-600 dark:text-dark-300">
                                                        @historyItem.GetFormattedAction()
                                                    </p>
                                                    @if (!string.IsNullOrEmpty(historyItem.IpAddress))
                                                    {
                                                        <div class="mt-2 flex items-center text-xs text-neutral-400 dark:text-dark-500">
                                                            <i class="fas fa-globe mr-1"></i>
                                                            <span>@historyItem.IpAddress</span>
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>

                        @if (Model.History.Count() > 8)
                        {
                            <div class="text-center pt-4 border-t border-neutral-200 dark:border-dark-300">
                                <button type="button" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium" onclick="showAllHistory()">
                                    <i class="fas fa-chevron-down mr-1"></i>
                                    Show @(Model.History.Count() - 8) more activities
                                </button>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-history text-2xl text-neutral-400 dark:text-dark-500"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No History Available</h4>
                            <p class="text-sm text-neutral-500 dark:text-dark-400">Task activity will appear here as actions are performed.</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Attachments Card -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-paperclip text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Attachments</h3>
                            <p class="text-sm text-neutral-500 dark:text-dark-400">@Model.Attachments.Count() file(s)</p>
                        </div>
                    </div>
                    @if (Model.CanEdit)
                    {
                        <div>
                            @{
                                ViewData["Text"] = "";
                                ViewData["Variant"] = "outline";
                                ViewData["Size"] = "sm";
                                ViewData["Icon"] = "fas fa-upload";
                                ViewData["Href"] = "#";
                                ViewData["OnClick"] = "showUploadModal()";
                                ViewData["Title"] = "Upload Attachment";
                            }
                            <partial name="Components/_Button" view-data="ViewData" />
                        </div>
                    }
                </div>
            </div>
            <div class="card-body-custom">
                @if (Model.Attachments.Any())
                {
                    <div class="space-y-3">
                        @foreach (var attachment in Model.Attachments)
                        {
                            <a href="@Url.Action("Download", "Attachments", new { id = attachment.Id })"
                               class="block p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-600 transition-colors">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-file text-neutral-400 dark:text-dark-500"></i>
                                        <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">@attachment.FileName</span>
                                    </div>
                                    <span class="text-xs text-neutral-500 dark:text-dark-400">@((attachment.FileSize / 1024.0).ToString("F1")) KB</span>
                                </div>
                                <p class="text-xs text-neutral-400 dark:text-dark-500">
                                    <i class="fas fa-user mr-1"></i>
                                    Uploaded by @attachment.UploadedByName on @attachment.UploadedAt.ToString("MMM dd, yyyy")
                                </p>
                            </a>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-paperclip text-2xl text-neutral-400 dark:text-dark-500"></i>
                        </div>
                        <p class="text-neutral-500 dark:text-dark-400 mb-4">No attachments</p>
                        @if (Model.CanEdit)
                        {
                            ViewData["Text"] = "Upload First File";
                            ViewData["Variant"] = "primary";
                            ViewData["Icon"] = "fas fa-upload";
                            ViewData["Href"] = "#";
                            ViewData["OnClick"] = "showUploadModal()";
                            <partial name="Components/_Button" view-data="ViewData" />
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Delete Task Modal -->
@if (Model.CanDelete)
{
        ViewData["ModalId"] = "deleteTaskModal";
        ViewData["ModalTitle"] = "Delete Task";
        ViewData["ModalSize"] = "sm";
        ViewData["BodyContent"] = @"
            <div class=""text-center py-4"">
                <div class=""w-16 h-16 bg-danger-100 dark:bg-danger-900 rounded-full flex items-center justify-center mx-auto mb-4"">
                    <i class=""fas fa-exclamation-triangle text-2xl text-danger-600 dark:text-danger-400""></i>
                </div>
                <h3 class=""text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2"">Delete Task</h3>
                <p class=""text-neutral-600 dark:text-dark-300 mb-6"">Are you sure you want to delete this task? This action cannot be undone.</p>

                <div class=""flex justify-center space-x-3"">
                    <button type=""button"" class=""px-4 py-2 text-sm font-medium text-neutral-700 dark:text-dark-200 bg-white dark:bg-surface-dark border border-neutral-300 dark:border-dark-300 rounded-lg hover:bg-neutral-50 dark:hover:bg-dark-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"" onclick=""closeModal('deleteTaskModal')"">
                        Cancel
                    </button>
                    <form asp-action=""Delete"" asp-route-id=""" + Model.Id + @""" method=""post"" class=""inline"">
                        <input name=""__RequestVerificationToken"" type=""hidden"" value=""" + Html.AntiForgeryToken().ToString() + @""" />
                        <button type=""submit"" class=""px-4 py-2 text-sm font-medium text-white bg-danger-600 border border-transparent rounded-lg hover:bg-danger-700 focus:outline-none focus:ring-2 focus:ring-danger-500 focus:ring-offset-2 transition-colors duration-200"">
                            Delete Task
                        </button>
                    </form>
                </div>
            </div>";
    <partial name="Components/_Modal" view-data="ViewData" />
}

<!-- Upload Attachment Modal -->
@if (Model.CanEdit)
{
        ViewData["ModalId"] = "uploadAttachmentModal";
        ViewData["ModalTitle"] = "Upload Attachment";
        ViewData["ModalSize"] = "md";
        ViewData["BodyContent"] = @"
            <form asp-controller=""Attachments"" asp-action=""Upload"" method=""post"" enctype=""multipart/form-data"" class=""space-y-6"">
                <input name=""__RequestVerificationToken"" type=""hidden"" value=""" + Html.AntiForgeryToken().ToString() + @""" />
                <input type=""hidden"" name=""taskId"" value=""" + Model.Id + @""" />

                <div>
                    <label class=""block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-2"">Select File</label>
                    <input type=""file""
                           name=""file""
                           class=""w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg bg-white dark:bg-surface-dark text-neutral-900 dark:text-dark-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200""
                           required />
                    <p class=""text-xs text-neutral-500 dark:text-dark-400 mt-1"">Maximum file size: 10MB</p>
                </div>

                <div class=""flex justify-end space-x-3 pt-4 border-t border-neutral-200 dark:border-dark-200"">
                    <button type=""button"" class=""px-4 py-2 text-sm font-medium text-neutral-700 dark:text-dark-200 bg-white dark:bg-surface-dark border border-neutral-300 dark:border-dark-300 rounded-lg hover:bg-neutral-50 dark:hover:bg-dark-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"" onclick=""closeModal('uploadAttachmentModal')"">
                        Cancel
                    </button>
                    <button type=""submit"" class=""px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"">
                        <i class=""fas fa-upload mr-2""></i>Upload File
                    </button>
                </div>
            </form>";

    <partial name="Components/_Modal" view-data="ViewData" />
}

@section Scripts {
    <partial name="Partials/_TaskDetailsScripts" />

    <script>
        $(document).ready(function() {
            // Initialize mentions system for this project
            if (window.mentionSystem) {
                window.mentionSystem.setProjectContext(@Model.ProjectId);
            }
        });
    </script>
}
