@model MyTasksViewModel

<!-- Standardized Page Header -->
@{
    ViewData["Title"] = "My Tasks";
    ViewData["Description"] = "Manage and track your assigned tasks";
    ViewData["Icon"] = "fas fa-tasks";
    ViewData["IconColor"] = "text-blue-600 dark:text-blue-400";
    ViewData["Actions"] = new object[] {
        new { Text = "New Task", Variant = "primary", Icon = "fas fa-plus", Href = Url.Action("Create"), OnClick = (string)null },
        new { Text = "Export", Variant = "outline", Icon = "fas fa-download", OnClick = "showExportModal()", Href = (string)null }
    };

    var statsArray = new List<object>
    {
        new { Label = "Total", Value = Model.Stats.TotalTasks.ToString(), Icon = "fas fa-tasks", Color = "blue" },
        new { Label = "Today", Value = Model.Stats.DueTodayTasks.ToString(), Icon = "fas fa-clock", Color = "amber" },
        new { Label = "Done", Value = $"{Model.Stats.CompletionRate:F0}%", Icon = "fas fa-chart-line", Color = "emerald" },
        new { Label = "Active", Value = Model.Stats.InProgressTasks.ToString(), Icon = "fas fa-play", Color = "blue" }
    };

    if (Model.Stats.OverdueTasks > 0)
    {
        statsArray.Insert(2, new { Label = "Overdue", Value = Model.Stats.OverdueTasks.ToString(), Icon = "fas fa-exclamation-triangle", Color = "red" });
    }

    ViewData["Stats"] = statsArray.ToArray();
}
<partial name="Components/_PageHeader" view-data="ViewData" />
