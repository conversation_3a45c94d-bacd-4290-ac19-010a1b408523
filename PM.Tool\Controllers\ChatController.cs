using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Models.ViewModels;
using System.Security.Claims;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class ChatController : SecureBaseController
    {
        private readonly ApplicationDbContext _context;
        private readonly IRealTimeNotificationService _realTimeService;
        private readonly ILogger<ChatController> _logger;

        public ChatController(
            ApplicationDbContext context,
            IRealTimeNotificationService realTimeService,
            ILogger<ChatController> logger,
            IAuditService auditService) : base(auditService)
        {
            _context = context;
            _realTimeService = realTimeService;
            _logger = logger;
        }

        // GET: Chat/Index
        public async Task<IActionResult> Index(int? projectId = null)
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var userProjects = await GetUserProjectsAsync(userId);

                var viewModel = new ChatViewModel
                {
                    ProjectId = projectId,
                    UserProjects = userProjects,
                    Messages = await GetRecentMessagesAsync(projectId, 50)
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading chat");
                return View(new ChatViewModel());
            }
        }

        // POST: Chat/SendMessage
        [HttpPost]
        public async Task<IActionResult> SendMessage([FromBody] SendMessageRequest request)
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var userName = User.Identity?.Name ?? "Unknown";

                // Create message record (we'll need to create a ChatMessage entity)
                var message = new
                {
                    Id = Guid.NewGuid(),
                    Content = request.Content,
                    AuthorId = userId,
                    AuthorName = userName,
                    ProjectId = request.ProjectId,
                    Timestamp = DateTime.UtcNow,
                    Type = request.Type ?? "text"
                };

                // Send real-time message to project members
                if (request.ProjectId.HasValue)
                {
                    await _realTimeService.BroadcastToProjectAsync(
                        request.ProjectId.Value,
                        "MessageReceived",
                        message);
                }

                // Log audit
                await LogAuditAsync(Core.Enums.AuditAction.Create, "ChatMessage", null);

                return Json(new { success = true, message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending chat message");
                return Json(new { success = false, message = "Error sending message" });
            }
        }

        // GET: Chat/GetMessages
        [HttpGet]
        public async Task<IActionResult> GetMessages(int? projectId = null, int page = 1, int pageSize = 50)
        {
            try
            {
                var messages = await GetRecentMessagesAsync(projectId, pageSize, page);

                return Json(new
                {
                    success = true,
                    messages = messages.Select(m => new
                    {
                        id = m.Id,
                        content = m.Content,
                        authorName = m.AuthorName,
                        timestamp = m.Timestamp,
                        relativeTime = GetRelativeTime(m.Timestamp),
                        type = m.Type
                    })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chat messages");
                return Json(new { success = false, message = "Error loading messages" });
            }
        }

        // GET: Chat/GetOnlineUsers
        [HttpGet]
        public async Task<IActionResult> GetOnlineUsers(int projectId)
        {
            try
            {
                // This would integrate with the CollaborationHub to get online users
                // For now, return a placeholder
                var onlineUsers = new List<object>
                {
                    new { id = "1", name = "John Doe", status = "online" },
                    new { id = "2", name = "Jane Smith", status = "away" }
                };

                return Json(new { success = true, users = onlineUsers });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting online users for project {ProjectId}", projectId);
                return Json(new { success = false, message = "Error loading online users" });
            }
        }

        // Private helper methods
        private async Task<List<ProjectSelectViewModel>> GetUserProjectsAsync(string userId)
        {
            return await _context.ProjectMembers
                .Where(pm => pm.UserId == userId && pm.IsActive)
                .Include(pm => pm.Project)
                .Select(pm => new ProjectSelectViewModel
                {
                    Id = pm.Project.Id,
                    Name = pm.Project.Name
                })
                .ToListAsync();
        }

        private async Task<List<ChatMessageViewModel>> GetRecentMessagesAsync(int? projectId, int count, int page = 1)
        {
            // Since we don't have a ChatMessage entity yet, return placeholder data
            // In a real implementation, this would query the database
            await Task.Delay(1); // Placeholder async operation

            var messages = new List<ChatMessageViewModel>();

            // Add some sample messages for demonstration
            if (projectId.HasValue)
            {
                messages.AddRange(new[]
                {
                    new ChatMessageViewModel
                    {
                        Id = Guid.NewGuid().ToString(),
                        Content = "Welcome to the project chat! 👋",
                        AuthorName = "System",
                        Timestamp = DateTime.UtcNow.AddMinutes(-30),
                        Type = "system"
                    },
                    new ChatMessageViewModel
                    {
                        Id = Guid.NewGuid().ToString(),
                        Content = "Great work on the latest sprint! The velocity is looking good.",
                        AuthorName = "Project Manager",
                        Timestamp = DateTime.UtcNow.AddMinutes(-15),
                        Type = "text"
                    },
                    new ChatMessageViewModel
                    {
                        Id = Guid.NewGuid().ToString(),
                        Content = "Thanks! The team has been working really well together.",
                        AuthorName = "Developer",
                        Timestamp = DateTime.UtcNow.AddMinutes(-10),
                        Type = "text"
                    }
                });
            }

            return messages.OrderByDescending(m => m.Timestamp).ToList();
        }

        private string GetRelativeTime(DateTime timestamp)
        {
            var timeSpan = DateTime.UtcNow - timestamp;

            if (timeSpan.TotalMinutes < 1)
                return "Just now";
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} minutes ago";
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} hours ago";
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} days ago";

            return timestamp.ToString("MMM dd, yyyy");
        }
    }

    // Request models
    public class SendMessageRequest
    {
        public string Content { get; set; } = string.Empty;
        public int? ProjectId { get; set; }
        public string? Type { get; set; } = "text";
    }
}
