# Project Requirements Template for Augment

## 📋 **Instructions for Use**

### **For New Projects**
1. Copy this template to `PROJECT_REQUIREMENTS.md` in your project root
2. Fill out all relevant sections (mark N/A for non-applicable items)
3. Start Augment session with: "Create project documentation based on PROJECT_REQUIREMENTS.md"
4. Augment will generate the complete documentation system based on your requirements

### **For Existing Projects**
1. Use this template to document current project state
2. Fill out sections based on existing implementation
3. Use as basis for creating Augment documentation system

---

## 🎯 **Project Overview**

### **Basic Information**
- **Project Name**: [Enter project name]
- **Project Type**: [Web App / Mobile App / Desktop App / API / Library / Other]
- **Primary Purpose**: [Brief description of what the project does]
- **Target Users**: [Who will use this project]
- **Business Goals**: [What business problems does this solve]

### **Project Scope**
- **MVP Features**: [List 3-5 core features for initial release]
- **Future Features**: [List planned features for later releases]
- **Out of Scope**: [What this project will NOT do]
- **Success Metrics**: [How will you measure success]

---

## 🛠️ **Technical Requirements**

### **Technology Stack**
- **Frontend**: [React / Angular / Vue / Blazor / Native / Other: ___]
- **Backend**: [ASP.NET Core / Node.js / Python / Java / Other: ___]
- **Database**: [SQL Server / PostgreSQL / MySQL / MongoDB / Other: ___]
- **Authentication**: [Identity / Auth0 / Firebase / Custom / Other: ___]
- **Hosting/Cloud**: [Azure / AWS / Google Cloud / On-premise / Other: ___]

### **Architecture Preferences**
- **Architecture Pattern**: [Clean Architecture / MVC / Microservices / Monolith / Other: ___]
- **Design Patterns**: [Repository / CQRS / Event Sourcing / Other: ___]
- **API Style**: [REST / GraphQL / gRPC / Other: ___]
- **Real-time Features**: [SignalR / WebSockets / Server-Sent Events / None]

### **Development Tools & Practices**
- **Version Control**: [Git / Other: ___]
- **CI/CD**: [Azure DevOps / GitHub Actions / Jenkins / Other: ___]
- **Testing Framework**: [xUnit / NUnit / Jest / Other: ___]
- **Code Quality**: [SonarQube / ESLint / Other: ___]
- **Documentation**: [Swagger / JSDoc / XML Comments / Other: ___]

---

## 👥 **Team & Development**

### **Team Structure**
- **Team Size**: [Solo / 2-5 people / 6-10 people / 10+ people]
- **Development Approach**: [Solo with Augment / Pair Programming / Team Development]
- **Experience Level**: [Beginner / Intermediate / Advanced]
- **Available Time**: [Full-time / Part-time / Weekends only]

### **Development Methodology**
- **Methodology**: [Agile/Scrum / Kanban / Waterfall / Ad-hoc]
- **Sprint Length**: [1 week / 2 weeks / 3 weeks / 4 weeks / N/A]
- **Planning Approach**: [Detailed upfront / Iterative / Minimal planning]
- **Progress Tracking**: [Daily / Weekly / Monthly / As needed]

---

## 🎨 **User Experience & Design**

### **UI/UX Requirements**
- **Design System**: [Material Design / Bootstrap / Custom / None specified]
- **Responsive Design**: [Mobile-first / Desktop-first / Mobile-only / Desktop-only]
- **Accessibility**: [WCAG 2.1 AA / Basic / Not required]
- **Browser Support**: [Modern browsers / IE11+ / Specific browsers: ___]
- **Themes**: [Light/Dark mode / Single theme / Custom themes]

### **User Interface Preferences**
- **Navigation Style**: [SPA / Multi-page / Hybrid]
- **Layout Preference**: [Sidebar / Top nav / Dashboard / Custom]
- **Data Display**: [Tables / Cards / Lists / Charts / Mixed]
- **Forms**: [Simple / Complex / Wizard-style / Dynamic]

---

## 🔒 **Security & Compliance**

### **Security Requirements**
- **Authentication Method**: [Username/Password / SSO / Multi-factor / Social login]
- **Authorization Model**: [Role-based / Permission-based / Attribute-based]
- **Data Encryption**: [At rest / In transit / Both / Not required]
- **Security Standards**: [OWASP / Custom / Industry-specific: ___]

### **Compliance Requirements**
- **Data Privacy**: [GDPR / CCPA / HIPAA / None / Other: ___]
- **Industry Standards**: [SOX / PCI DSS / ISO 27001 / Other: ___]
- **Audit Requirements**: [Full audit trail / Basic logging / None]
- **Data Retention**: [Specific period: ___ / Indefinite / User-controlled]

---

## 📊 **Performance & Scalability**

### **Performance Requirements**
- **Expected Users**: [Concurrent users: ___ / Total users: ___]
- **Response Time**: [< 200ms / < 500ms / < 1s / < 2s / Not critical]
- **Availability**: [99.9% / 99.5% / 99% / Best effort]
- **Data Volume**: [Small < 1GB / Medium 1-100GB / Large > 100GB]

### **Scalability Needs**
- **Growth Expectations**: [Stable / Moderate growth / Rapid growth / Viral potential]
- **Geographic Distribution**: [Single region / Multi-region / Global]
- **Peak Load Handling**: [2x normal / 5x normal / 10x normal / Unknown]
- **Scaling Strategy**: [Vertical / Horizontal / Auto-scaling / Manual]

---

## 🔌 **Integration Requirements**

### **Third-party Integrations**
- **Payment Processing**: [Stripe / PayPal / Square / None / Other: ___]
- **Email Service**: [SendGrid / Mailgun / SMTP / None / Other: ___]
- **File Storage**: [Azure Blob / AWS S3 / Google Cloud / Local / Other: ___]
- **Analytics**: [Google Analytics / Application Insights / Custom / None]
- **Monitoring**: [Application Insights / New Relic / DataDog / None / Other: ___]

### **API Integrations**
- **External APIs**: [List any external APIs to integrate with]
- **Data Sources**: [Databases / Files / APIs / Real-time feeds]
- **Export Capabilities**: [CSV / Excel / PDF / API / None]
- **Import Capabilities**: [CSV / Excel / API / Bulk upload / None]

---

## 📅 **Timeline & Milestones**

### **Project Timeline**
- **Project Start Date**: [Date]
- **MVP Target Date**: [Date]
- **Production Launch**: [Date]
- **Major Milestones**: [List key milestones with dates]

### **Development Phases**
- **Phase 1 (MVP)**: [Duration: ___ / Features: ___]
- **Phase 2**: [Duration: ___ / Features: ___]
- **Phase 3**: [Duration: ___ / Features: ___]
- **Ongoing Maintenance**: [Support level / Update frequency]

---

## 💰 **Budget & Resources**

### **Development Resources**
- **Budget Range**: [< $10K / $10K-50K / $50K-100K / > $100K / No budget constraints]
- **Infrastructure Budget**: [Monthly: $__ / Annual: $__ / Pay-as-you-go]
- **Third-party Services**: [Budget for external services: $__]
- **Licensing Costs**: [Development tools / Runtime licenses: $__]

### **Resource Constraints**
- **Time Constraints**: [Hard deadline / Flexible / No constraints]
- **Skill Constraints**: [Learning new technologies OK / Stick to known tech]
- **Infrastructure Constraints**: [On-premise only / Cloud preferred / No constraints]

---

## 📝 **Additional Requirements**

### **Special Considerations**
- **Legacy System Integration**: [Yes / No / Details: ___]
- **Data Migration**: [From system: ___ / Volume: ___ / Complexity: ___]
- **Offline Capabilities**: [Required / Nice to have / Not needed]
- **Multi-language Support**: [Languages: ___ / Not required]

### **Risk Factors**
- **Technical Risks**: [List potential technical challenges]
- **Business Risks**: [List potential business challenges]
- **Mitigation Strategies**: [How to address identified risks]

### **Success Criteria**
- **Technical Success**: [Performance metrics / Quality metrics]
- **Business Success**: [User adoption / Revenue / Cost savings]
- **User Success**: [User satisfaction / Task completion rates]

---

## 🎯 **Next Steps**

### **Immediate Actions**
- [ ] Complete this requirements document
- [ ] Review and validate requirements with stakeholders
- [ ] Start Augment session with: "Create project documentation based on PROJECT_REQUIREMENTS.md"
- [ ] Review generated documentation and adjust as needed

### **Development Kickoff**
- [ ] Set up development environment
- [ ] Create initial project structure
- [ ] Establish development workflow
- [ ] Begin Sprint 1 planning

---

## 📋 **Template Completion Checklist**

- [ ] Project Overview completed
- [ ] Technical Requirements specified
- [ ] Team & Development approach defined
- [ ] UI/UX requirements outlined
- [ ] Security & Compliance needs identified
- [ ] Performance & Scalability requirements set
- [ ] Integration requirements listed
- [ ] Timeline & Milestones planned
- [ ] Budget & Resources estimated
- [ ] Additional Requirements documented
- [ ] Next Steps planned

**Once completed, save as `PROJECT_REQUIREMENTS.md` and start your Augment session!**
