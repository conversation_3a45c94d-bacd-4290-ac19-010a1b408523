# PM.Tool - Development Checklist

## Pre-Development Setup ✅

### Before Starting Any Feature
- [ ] Review existing similar features in the codebase
- [ ] Check recent git commits for implementation patterns
- [ ] Understand the business requirements and user stories
- [ ] Identify which entities and services will be affected
- [ ] Plan the database schema changes (if any)

### Information Gathering
- [ ] Use `codebase-retrieval` to understand existing code patterns
- [ ] Use `git-commit-retrieval` to see how similar features were implemented
- [ ] Review the `AUGMENT_DEVELOPMENT_GUIDE.md` for project standards
- [ ] Check `Documentation/` folder for relevant design guidelines

## Feature Implementation Checklist ✅

### 1. Database Layer
- [ ] Create/modify entity in `Core/Entities/`
- [ ] Add `[Encrypted]` attribute for sensitive fields
- [ ] Ensure entity inherits from `BaseEntity`
- [ ] Add entity to `ApplicationDbContext.cs`
- [ ] Create and run database migration
- [ ] Test migration rollback capability

### 2. Repository Layer
- [ ] Create repository interface in `Core/Interfaces/`
- [ ] Implement repository in `Infrastructure/Repositories/`
- [ ] Inherit from `Repository<T>` base class
- [ ] Add custom query methods if needed
- [ ] Register repository in `Program.cs`

### 3. Service Layer
- [ ] Create service interface in `Core/Interfaces/`
- [ ] Implement service in `Application/Services/`
- [ ] Include audit logging for all CRUD operations
- [ ] Add proper error handling and validation
- [ ] Register service in `Program.cs`
- [ ] Add unit tests for service methods

### 4. Controller Layer
- [ ] Create controller inheriting from `SecureBaseController`
- [ ] Add proper authorization attributes
- [ ] Implement standard CRUD actions
- [ ] Add resource-based authorization where needed
- [ ] Include real-time notifications (SignalR) if applicable
- [ ] Add API endpoints with proper versioning
- [ ] Add controller tests

### 5. View Models
- [ ] Create ViewModels for each action (Create, Edit, Details, etc.)
- [ ] Add proper validation attributes
- [ ] Include `ToEntity()` and `FromEntity()` methods
- [ ] Add FluentValidation validators
- [ ] Test validation rules

### 6. Views and UI
- [ ] Use standardized page structure with `_PageHeader`
- [ ] Implement responsive design with Bootstrap 5 + TailwindCSS
- [ ] Use shared components from `Views/Shared/Components/`
- [ ] Add proper breadcrumb navigation
- [ ] Include loading states and error handling
- [ ] Add client-side validation
- [ ] Test on different screen sizes

### 7. Authorization & Security
- [ ] Define authorization policies if needed
- [ ] Implement authorization handlers for resource-based auth
- [ ] Add CSRF protection to forms
- [ ] Validate all user inputs
- [ ] Test authorization scenarios
- [ ] Ensure sensitive data is encrypted

### 8. Real-time Features (if applicable)
- [ ] Add SignalR hub methods
- [ ] Implement client-side JavaScript for real-time updates
- [ ] Test real-time functionality across multiple users
- [ ] Handle connection failures gracefully

### 9. API Documentation
- [ ] Add XML documentation comments
- [ ] Ensure Swagger documentation is complete
- [ ] Test API endpoints with proper authentication
- [ ] Validate API response models

### 10. Testing
- [ ] Write unit tests for services
- [ ] Write integration tests for controllers
- [ ] Add E2E tests for critical user flows
- [ ] Test error scenarios and edge cases
- [ ] Verify authorization works correctly
- [ ] Test real-time features if applicable

## Code Quality Checklist ✅

### Code Standards
- [ ] Follow established naming conventions
- [ ] Use async/await for all database operations
- [ ] Include proper error handling and logging
- [ ] Add XML documentation for public methods
- [ ] Remove unused using statements
- [ ] Follow SOLID principles

### Performance
- [ ] Use pagination for large datasets
- [ ] Implement proper caching where appropriate
- [ ] Avoid N+1 query problems
- [ ] Use `Include()` judiciously for related data
- [ ] Test performance with realistic data volumes

### Security
- [ ] Validate all inputs
- [ ] Use parameterized queries (EF Core handles this)
- [ ] Implement proper authorization
- [ ] Encrypt sensitive data
- [ ] Log security-relevant events

## UI/UX Checklist ✅

### Design Consistency
- [ ] Use standardized components and styling
- [ ] Follow the established design system
- [ ] Ensure consistent spacing and typography
- [ ] Use proper color schemes and themes
- [ ] Test accessibility features

### User Experience
- [ ] Provide clear feedback for user actions
- [ ] Include loading states for async operations
- [ ] Handle errors gracefully with user-friendly messages
- [ ] Ensure forms are intuitive and well-labeled
- [ ] Test user workflows end-to-end

### Responsive Design
- [ ] Test on mobile devices
- [ ] Ensure tables are responsive or scrollable
- [ ] Verify navigation works on all screen sizes
- [ ] Test touch interactions on mobile

## Deployment Checklist ✅

### Configuration
- [ ] Update configuration files for different environments
- [ ] Ensure connection strings are properly configured
- [ ] Verify encryption keys are set correctly
- [ ] Check logging configuration

### Database
- [ ] Run migrations on target environment
- [ ] Verify data integrity after migration
- [ ] Test rollback procedures
- [ ] Backup database before deployment

### Testing in Environment
- [ ] Smoke test critical functionality
- [ ] Verify authentication and authorization
- [ ] Test real-time features
- [ ] Check API endpoints
- [ ] Verify file uploads work correctly

## Documentation Checklist ✅

### Code Documentation
- [ ] Update XML documentation comments
- [ ] Add inline comments for complex logic
- [ ] Update API documentation
- [ ] Document any breaking changes

### User Documentation
- [ ] Update user guides if UI changes significantly
- [ ] Document new features and workflows
- [ ] Update help text and tooltips
- [ ] Create training materials if needed

### Developer Documentation
- [ ] Update architecture documentation if needed
- [ ] Document new patterns or conventions
- [ ] Update deployment guides
- [ ] Add troubleshooting information

## Post-Development Review ✅

### Code Review
- [ ] Self-review code for quality and standards
- [ ] Check for potential security issues
- [ ] Verify error handling is comprehensive
- [ ] Ensure logging is appropriate

### Testing Review
- [ ] Run all tests and ensure they pass
- [ ] Check test coverage is adequate
- [ ] Verify integration tests cover main scenarios
- [ ] Test edge cases and error conditions

### Performance Review
- [ ] Profile database queries for efficiency
- [ ] Check for memory leaks or performance issues
- [ ] Verify caching is working correctly
- [ ] Test with realistic data volumes

## Common Pitfalls to Avoid ❌

- [ ] Don't forget to register services in `Program.cs`
- [ ] Don't skip authorization checks
- [ ] Don't forget to add audit logging
- [ ] Don't hardcode configuration values
- [ ] Don't skip input validation
- [ ] Don't forget to handle async operations properly
- [ ] Don't skip error handling
- [ ] Don't forget to test authorization scenarios
- [ ] Don't skip database migration testing
- [ ] Don't forget to update documentation

## Emergency Fixes Checklist 🚨

### For Critical Issues
- [ ] Identify the root cause quickly
- [ ] Create a minimal fix that addresses the issue
- [ ] Test the fix thoroughly
- [ ] Deploy with proper rollback plan
- [ ] Monitor the system after deployment
- [ ] Document the issue and fix for future reference
- [ ] Plan a proper long-term solution if needed

This checklist should help ensure consistent, high-quality development across all features in the PM.Tool project.
