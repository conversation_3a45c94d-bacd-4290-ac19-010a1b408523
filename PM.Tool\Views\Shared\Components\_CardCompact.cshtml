@{
    var title = ViewData["Title"]?.ToString();
    var subtitle = ViewData["Subtitle"]?.ToString();
    var icon = ViewData["Icon"]?.ToString();
    var iconColor = ViewData["IconColor"]?.ToString() ?? "text-primary-600 dark:text-primary-400";
    var headerActions = ViewData["HeaderActions"]?.ToString();
    var footerActions = ViewData["FooterActions"]?.ToString();
    var additionalClasses = ViewData["AdditionalClasses"]?.ToString() ?? "";
    var headerClasses = ViewData["HeaderClasses"]?.ToString() ?? "";
    var bodyClasses = ViewData["BodyClasses"]?.ToString() ?? "";
    var footerClasses = ViewData["FooterClasses"]?.ToString() ?? "";
    var showHeader = ViewData["ShowHeader"] as bool? ?? true;
    var showFooter = ViewData["ShowFooter"] as bool? ?? false;
    var compact = ViewData["Compact"] as bool? ?? true;
    
    var cardClasses = compact ? "card-compact" : "card-custom";
    var headerClass = compact ? "card-compact-header" : "card-header-custom";
    var bodyClass = compact ? "card-compact-body" : "card-body-custom";
    var footerClass = compact ? "card-compact-footer" : "card-footer-custom";
}

<div class="@cardClasses @additionalClasses">
    @if (showHeader && (!string.IsNullOrEmpty(title) || !string.IsNullOrEmpty(headerActions)))
    {
        <div class="@headerClass @headerClasses">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    @if (!string.IsNullOrEmpty(icon))
                    {
                        <div class="w-8 h-8 bg-primary-50 dark:bg-primary-900/20 rounded-lg flex items-center justify-center flex-shrink-0">
                            <i class="@icon @iconColor text-sm"></i>
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(title))
                    {
                        <div>
                            <h3 class="text-base font-semibold text-neutral-900 dark:text-white leading-tight">@title</h3>
                            @if (!string.IsNullOrEmpty(subtitle))
                            {
                                <p class="text-sm text-neutral-500 dark:text-neutral-400 mt-0.5">@subtitle</p>
                            }
                        </div>
                    }
                </div>
                
                @if (!string.IsNullOrEmpty(headerActions))
                {
                    <div class="flex items-center gap-2">
                        @Html.Raw(headerActions)
                    </div>
                }
            </div>
        </div>
    }
    
    <div class="@bodyClass @bodyClasses">
        @RenderBody()
    </div>
    
    @if (showFooter && !string.IsNullOrEmpty(footerActions))
    {
        <div class="@footerClass @footerClasses">
            @Html.Raw(footerActions)
        </div>
    }
</div>

@* Usage Examples:

<!-- Basic Compact Card -->
@{
    ViewData["Title"] = "Card Title";
    ViewData["Icon"] = "fas fa-chart-bar";
}
<partial name="Components/_CardCompact" view-data="ViewData">
    <p>Card content goes here</p>
</partial>

<!-- Card with Header Actions -->
@{
    ViewData["Title"] = "Projects";
    ViewData["Subtitle"] = "Manage your projects";
    ViewData["Icon"] = "fas fa-folder";
    ViewData["HeaderActions"] = "<button class='btn-compact btn-compact-secondary'>Action</button>";
}
<partial name="Components/_CardCompact" view-data="ViewData">
    <div class="space-y-3">
        <!-- Content -->
    </div>
</partial>

<!-- Card with Footer -->
@{
    ViewData["Title"] = "Statistics";
    ViewData["ShowFooter"] = true;
    ViewData["FooterActions"] = "<a href='#' class='text-sm text-primary-600 hover:text-primary-700'>View Details</a>";
}
<partial name="Components/_CardCompact" view-data="ViewData">
    <div class="grid grid-cols-2 gap-4">
        <!-- Stats content -->
    </div>
</partial>

*@
