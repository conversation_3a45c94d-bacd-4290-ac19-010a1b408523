html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Real-time Collaboration Styles */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    padding: 16px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    border-left: 4px solid #3B82F6;
    animation: slideInRight 0.3s ease-out;
}

.notification-info {
    border-left-color: #3B82F6;
}

.notification-success {
    border-left-color: #10B981;
}

.notification-warning {
    border-left-color: #F59E0B;
}

.notification-error {
    border-left-color: #EF4444;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: #111827;
    margin-bottom: 4px;
}

.notification-message {
    color: #6B7280;
    font-size: 14px;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #9CA3AF;
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
}

.notification-close:hover {
    color: #6B7280;
}

/* Dark mode notifications */
.dark .notification {
    background: #374151;
    color: #F9FAFB;
}

.dark .notification-title {
    color: #F9FAFB;
}

.dark .notification-message {
    color: #D1D5DB;
}

/* Presence indicators */
.presence-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 6px;
    position: relative;
}

.presence-indicator.online {
    background-color: #10B981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

.presence-indicator.offline {
    background-color: #9CA3AF;
}

.presence-indicator.online::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    border: 2px solid #10B981;
    animation: pulse 2s infinite;
}

/* Connection status */
.connection-status {
    position: fixed;
    bottom: 20px;
    left: 20px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
}

.connection-status.success {
    background-color: #D1FAE5;
    color: #065F46;
    border: 1px solid #A7F3D0;
}

.connection-status.warning {
    background-color: #FEF3C7;
    color: #92400E;
    border: 1px solid #FDE68A;
}

.connection-status.error {
    background-color: #FEE2E2;
    color: #991B1B;
    border: 1px solid #FECACA;
}

/* Real-time update animations */
.real-time-updated {
    animation: highlightUpdate 2s ease-out;
}

.real-time-moved {
    animation: highlightMove 1s ease-out;
}

.real-time-comment {
    animation: slideInLeft 0.5s ease-out;
    border-left: 3px solid #3B82F6;
    padding-left: 12px;
}

/* Activity indicators */
.activity-indicator {
    font-size: 11px;
    color: #6B7280;
    font-style: italic;
    margin-top: 2px;
}

.dark .activity-indicator {
    color: #9CA3AF;
}

/* Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes highlightUpdate {
    0% {
        background-color: rgba(59, 130, 246, 0.2);
        transform: scale(1.02);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

@keyframes highlightMove {
    0% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Chat Styles */
.message-item {
    margin-bottom: 16px;
    padding: 12px;
    border-radius: 8px;
    background: #F8F9FA;
    border-left: 3px solid #3B82F6;
}

.message-item.system-message {
    background: #FEF3C7;
    border-left-color: #F59E0B;
    font-style: italic;
}

.dark .message-item {
    background: #374151;
    color: #F9FAFB;
}

.dark .message-item.system-message {
    background: #451A03;
    color: #FEF3C7;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.message-author {
    font-weight: 600;
    color: #3B82F6;
    font-size: 14px;
}

.dark .message-author {
    color: #60A5FA;
}

.message-time {
    font-size: 12px;
    color: #6B7280;
}

.dark .message-time {
    color: #9CA3AF;
}

.message-content {
    color: #374151;
    line-height: 1.5;
}

.dark .message-content {
    color: #E5E7EB;
}

.online-users-list {
    max-height: 300px;
    overflow-y: auto;
}

.online-user-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #E5E7EB;
}

.dark .online-user-item {
    border-bottom-color: #4B5563;
}

.online-user-item:last-child {
    border-bottom: none;
}

.online-user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #3B82F6;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    margin-right: 12px;
}

.online-user-info {
    flex: 1;
}

.online-user-name {
    font-weight: 500;
    color: #111827;
    font-size: 14px;
}

.dark .online-user-name {
    color: #F9FAFB;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #3B82F6;
    line-height: 1;
}

.dark .stat-number {
    color: #60A5FA;
}

.stat-label {
    font-size: 12px;
    color: #6B7280;
    margin-top: 4px;
}

.dark .stat-label {
    color: #9CA3AF;
}

/* Chat input enhancements */
#messagesContainer {
    scroll-behavior: smooth;
}

#messagesContainer::-webkit-scrollbar {
    width: 6px;
}

#messagesContainer::-webkit-scrollbar-track {
    background: #F1F5F9;
}

#messagesContainer::-webkit-scrollbar-thumb {
    background: #CBD5E1;
    border-radius: 3px;
}

#messagesContainer::-webkit-scrollbar-thumb:hover {
    background: #94A3B8;
}

.dark #messagesContainer::-webkit-scrollbar-track {
    background: #1E293B;
}

.dark #messagesContainer::-webkit-scrollbar-thumb {
    background: #475569;
}

.dark #messagesContainer::-webkit-scrollbar-thumb:hover {
    background: #64748B;
}

/* Epic Management Styles */
.epic-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.epic-card {
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
    overflow: hidden;
}

.dark .epic-card {
    border-color: #4B5563;
    background: #374151;
}

.epic-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.dark .epic-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.epic-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #E5E7EB;
    background: #F9FAFB;
}

.dark .epic-card-header {
    border-bottom-color: #4B5563;
    background: #1F2937;
}

.epic-key {
    font-weight: 600;
    color: #3B82F6;
    font-size: 14px;
}

.dark .epic-key {
    color: #60A5FA;
}

.epic-card-body {
    padding: 16px;
}

.epic-title {
    margin-bottom: 8px;
    font-weight: 600;
    color: #111827;
}

.dark .epic-title {
    color: #F9FAFB;
}

.epic-title a {
    color: inherit;
}

.epic-title a:hover {
    color: #3B82F6;
}

.dark .epic-title a:hover {
    color: #60A5FA;
}

.epic-description {
    color: #6B7280;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
}

.dark .epic-description {
    color: #9CA3AF;
}

.epic-meta {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 12px;
}

.epic-progress .progress {
    background-color: #E5E7EB;
}

.dark .epic-progress .progress {
    background-color: #4B5563;
}

.epic-stats {
    text-align: center;
}

.stat-item {
    margin-bottom: 16px;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #3B82F6;
    line-height: 1;
}

.dark .stat-number {
    color: #60A5FA;
}

.stat-label {
    font-size: 12px;
    color: #6B7280;
    margin-top: 4px;
}

.dark .stat-label {
    color: #9CA3AF;
}

.stat-mini {
    text-align: center;
}

.stat-mini-number {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    line-height: 1;
}

.dark .stat-mini-number {
    color: #F9FAFB;
}

.stat-mini-label {
    font-size: 11px;
    color: #6B7280;
    margin-top: 2px;
}

.dark .stat-mini-label {
    color: #9CA3AF;
}

.epic-card-footer {
    padding: 12px 16px;
    border-top: 1px solid #E5E7EB;
    background: #F9FAFB;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dark .epic-card-footer {
    border-top-color: #4B5563;
    background: #1F2937;
}

.project-quick-select {
    transition: all 0.2s ease;
}

.project-quick-select:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .project-quick-select:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Epic Details Styles */
.progress-section {
    padding: 16px;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    background: #F9FAFB;
}

.dark .progress-section {
    border-color: #4B5563;
    background: #1F2937;
}

.feature-item, .user-story-item {
    padding: 16px;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    margin-bottom: 12px;
    background: white;
    transition: all 0.2s ease;
}

.dark .feature-item, .dark .user-story-item {
    border-color: #4B5563;
    background: #374151;
}

.feature-item:hover, .user-story-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.dark .feature-item:hover, .dark .user-story-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.feature-status-indicator, .story-status-indicator {
    margin-right: 12px;
    margin-top: 4px;
}

.feature-meta, .story-meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.epic-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    font-size: 12px;
    font-weight: 600;
    color: #6B7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dark .info-label {
    color: #9CA3AF;
}

.info-value {
    color: #111827;
    font-weight: 500;
}

.dark .info-value {
    color: #F9FAFB;
}

/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #E5E7EB;
}

.dark .timeline::before {
    background: #4B5563;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 16px;
    height: 16px;
    background: white;
    border: 2px solid #E5E7EB;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
}

.dark .timeline-marker {
    background: #374151;
    border-color: #4B5563;
}

.timeline-content {
    padding-left: 8px;
}

.timeline-title {
    font-weight: 600;
    color: #111827;
    font-size: 14px;
}

.dark .timeline-title {
    color: #F9FAFB;
}

.timeline-description {
    color: #6B7280;
    font-size: 13px;
    margin: 2px 0;
}

.dark .timeline-description {
    color: #9CA3AF;
}

.timeline-date {
    color: #9CA3AF;
    font-size: 11px;
}

.dark .timeline-date {
    color: #6B7280;
}