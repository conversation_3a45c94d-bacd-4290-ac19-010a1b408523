using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using PM.Tool.Core.Common;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Entities;
using PM.Tool.Models.ViewModels;
using System.Security.Claims;

namespace PM.Tool.Controllers
{
    public class WbsController : SecureBaseController
    {
        private readonly IWbsService _wbsService;
        private readonly ITaskService _taskService;
        private readonly IProjectService _projectService;
        private readonly UserManager<ApplicationUser> _userManager;

        public WbsController(
            IWbsService wbsService,
            ITaskService taskService,
            IProjectService projectService,
            UserManager<ApplicationUser> userManager,
            IAuditService auditService) : base(auditService)
        {
            _wbsService = wbsService;
            _taskService = taskService;
            _projectService = projectService;
            _userManager = userManager;
        }

        // GET: Wbs
        public async Task<IActionResult> Index(string? searchTerm = null, ProjectStatus? statusFilter = null,
            string? sortBy = "Name", string? sortOrder = "asc", int page = 1, int pageSize = 12)
        {
            try
            {
                // Get current user ID from claims (already authenticated via [Authorize])
                var userId = User.FindFirstValue(System.Security.Claims.ClaimTypes.NameIdentifier);
                var allProjects = await _projectService.GetUserProjectsAsync(userId!);

                // If user has only one project and no filters are applied, redirect directly to its WBS
                if (allProjects.Count() == 1 && string.IsNullOrEmpty(searchTerm) && !statusFilter.HasValue)
                {
                    return RedirectToAction("ProjectWbs", new { projectId = allProjects.First().Id });
                }

                // Apply filters
                var filteredProjects = allProjects.AsQueryable();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    filteredProjects = filteredProjects.Where(p =>
                        p.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        (!string.IsNullOrEmpty(p.Description) && p.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                        (!string.IsNullOrEmpty(p.ClientName) && p.ClientName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)));
                }

                if (statusFilter.HasValue)
                {
                    filteredProjects = filteredProjects.Where(p => p.Status == statusFilter.Value);
                }

                // Apply sorting
                filteredProjects = sortBy?.ToLower() switch
                {
                    "createdat" => sortOrder == "desc" ? filteredProjects.OrderByDescending(p => p.CreatedAt) : filteredProjects.OrderBy(p => p.CreatedAt),
                    "startdate" => sortOrder == "desc" ? filteredProjects.OrderByDescending(p => p.StartDate) : filteredProjects.OrderBy(p => p.StartDate),
                    "status" => sortOrder == "desc" ? filteredProjects.OrderByDescending(p => p.Status) : filteredProjects.OrderBy(p => p.Status),
                    _ => sortOrder == "desc" ? filteredProjects.OrderByDescending(p => p.Name) : filteredProjects.OrderBy(p => p.Name)
                };

                // Apply pagination
                var totalCount = filteredProjects.Count();
                var projects = filteredProjects
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                var paginatedProjects = new PaginatedList<Project>(projects, totalCount, page, pageSize);

                // Create view model
                var viewModel = new ProjectSelectionViewModel
                {
                    Projects = paginatedProjects,
                    SearchTerm = searchTerm,
                    StatusFilter = statusFilter,
                    SortBy = sortBy,
                    SortOrder = sortOrder,
                    PageSize = pageSize,
                    TotalProjects = allProjects.Count(),
                    ActiveProjects = allProjects.Count(p => p.Status == ProjectStatus.Active),
                    CompletedProjects = allProjects.Count(p => p.Status == ProjectStatus.Completed),
                    PlanningProjects = allProjects.Count(p => p.Status == ProjectStatus.Planning)
                };

                ViewBag.Title = "Work Breakdown Structure - Select Project";
                return View("ProjectSelection", viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading projects.";
                return RedirectToAction("Index", "Dashboard");
            }
        }

        // GET: Wbs/ProjectWbs/5
        public async Task<IActionResult> ProjectWbs(int projectId)
        {
            try
            {
                // Debug logging
                Console.WriteLine($"ProjectWbs called with projectId: {projectId}");

                // Validate projectId
                if (projectId <= 0)
                {
                    TempData["Error"] = "Invalid project ID provided.";
                    return RedirectToAction("Index", "Projects");
                }
                // Check if user has access to this project
                if (!await UserCanAccessProject(projectId))
                {
                    return RedirectToAccessDenied();
                }

                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var wbsStructure = await _wbsService.GetWbsStructureAsync(projectId);

                // Get project members for task assignment dropdown
                var projectMembers = await _projectService.GetProjectMembersAsync(projectId);

                ViewBag.ProjectId = projectId;
                ViewBag.ProjectName = project.Name;
                ViewBag.Users = projectMembers;

                return View("Index", wbsStructure);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading WBS structure.";
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        // POST: Wbs/GenerateCodes
        [HttpPost]
        public async Task<IActionResult> GenerateCodes(int projectId)
        {
            try
            {
                // Check if user has access to this project
                if (!await UserCanAccessProject(projectId))
                {
                    return Json(new { success = false, message = "Access denied to this project" });
                }

                var success = await _wbsService.GenerateWbsCodesAsync(projectId);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "WBS", projectId);
                    return Json(new { success = true, message = "WBS codes generated successfully" });
                }
                else
                {
                    return Json(new { success = false, message = "Failed to generate WBS codes" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error generating WBS codes: " + ex.Message });
            }
        }

        // POST: Wbs/DuplicateTask
        [HttpPost]
        public async Task<IActionResult> DuplicateTask(int taskId)
        {
            try
            {
                var originalTask = await _taskService.GetTaskByIdAsync(taskId);
                if (originalTask == null)
                {
                    return Json(new { success = false, message = "Task not found" });
                }

                // Check if user has access to this project
                if (!await UserCanAccessProject(originalTask.ProjectId))
                {
                    return Json(new { success = false, message = "Access denied to this project" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var duplicatedTask = await _taskService.DuplicateTaskAsync(taskId, userId!);

                // Generate WBS code for the duplicated task
                await _wbsService.UpdateWbsCodesAsync(duplicatedTask.ProjectId);

                await LogAuditAsync(AuditAction.Create, "Task", duplicatedTask.Id);
                return Json(new { success = true, message = "Task duplicated successfully" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error duplicating task: " + ex.Message });
            }
        }

        // POST: Wbs/MoveTask
        [HttpPost]
        public async Task<IActionResult> MoveTask(int taskId, string direction)
        {
            try
            {
                var task = await _taskService.GetTaskByIdAsync(taskId);
                if (task == null)
                {
                    return Json(new { success = false, message = "Task not found" });
                }

                // Check if user has access to this project
                if (!await UserCanAccessProject(task.ProjectId))
                {
                    return Json(new { success = false, message = "Access denied to this project" });
                }

                bool success;
                bool moveUp = direction.Equals("up", StringComparison.OrdinalIgnoreCase);

                if (moveUp)
                {
                    success = await _taskService.MoveTaskUpAsync(taskId);
                    if (!success)
                    {
                        return Json(new { success = false, message = "Task is already at the top" });
                    }
                }
                else
                {
                    success = await _taskService.MoveTaskDownAsync(taskId);
                    if (!success)
                    {
                        return Json(new { success = false, message = "Task is already at the bottom" });
                    }
                }

                // Regenerate WBS codes to maintain proper numbering
                await _wbsService.UpdateWbsCodesAsync(task.ProjectId);

                await LogAuditAsync(AuditAction.Update, "Task", taskId);
                return Json(new { success = true, message = $"Task moved {direction} successfully" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Error moving task: {ex.Message}" });
            }
        }

        // POST: Wbs/UpdateTaskStatus
        [HttpPost]
        public async Task<IActionResult> UpdateTaskStatus(int taskId, string status)
        {
            try
            {
                var task = await _taskService.GetTaskByIdAsync(taskId);
                if (task == null)
                {
                    return Json(new { success = false, message = "Task not found" });
                }

                // Check if user has access to this project
                if (!await UserCanAccessProject(task.ProjectId))
                {
                    return Json(new { success = false, message = "Access denied to this project" });
                }

                if (Enum.TryParse<Core.Enums.TaskStatus>(status, out var taskStatus))
                {
                    var oldStatus = task.Status;
                    task.Status = taskStatus;
                    if (taskStatus == Core.Enums.TaskStatus.Done)
                    {
                        task.CompletedDate = DateTime.UtcNow;
                    }

                    var updatedTask = await _taskService.UpdateTaskAsync(task);
                    if (updatedTask != null)
                    {
                        // Log detailed status change
                        var oldValues = $"{{\"Status\":\"{oldStatus}\"}}";
                        var newValues = $"{{\"Status\":\"{taskStatus}\"}}";
                        await LogAuditAsync(AuditAction.ChangeStatus, "Task", taskId, oldValues, newValues);

                        return Json(new { success = true, message = $"Task status updated to {status}" });
                    }
                    else
                    {
                        return Json(new { success = false, message = "Failed to update task status" });
                    }
                }
                else
                {
                    return Json(new { success = false, message = "Invalid status value" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error updating task status: " + ex.Message });
            }
        }

        // POST: Wbs/UpdateTask
        [HttpPost]
        public async Task<IActionResult> UpdateTask(int taskId, string title)
        {
            try
            {
                var task = await _taskService.GetTaskByIdAsync(taskId);
                if (task == null)
                {
                    return Json(new { success = false, message = "Task not found" });
                }

                // Check if user has access to this project
                if (!await UserCanAccessProject(task.ProjectId))
                {
                    return Json(new { success = false, message = "Access denied to this project" });
                }

                task.Title = title;
                task.UpdatedAt = DateTime.UtcNow;

                var updatedTask = await _taskService.UpdateTaskAsync(task);
                if (updatedTask != null)
                {
                    await LogAuditAsync(AuditAction.Update, "Task", taskId);
                    return Json(new { success = true, message = "Task updated successfully" });
                }
                else
                {
                    return Json(new { success = false, message = "Failed to update task" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error updating task: " + ex.Message });
            }
        }

        // GET: Wbs/ExportTask
        [HttpGet]
        public async Task<IActionResult> ExportTask(int taskId, int projectId)
        {
            try
            {
                var task = await _taskService.GetTaskByIdAsync(taskId);
                if (task == null)
                {
                    return Json(new { success = false, message = "Task not found" });
                }

                // Check if user has access to this project
                if (!await UserCanAccessProject(projectId))
                {
                    return Json(new { success = false, message = "Access denied to this project" });
                }

                // Get task with all its subtasks
                var allTasks = await _wbsService.GetWbsStructureAsync(projectId);
                var taskWithSubtasks = GetTaskWithAllSubtasks(task, allTasks.ToList());

                // Export as CSV
                var csv = new System.Text.StringBuilder();
                csv.AppendLine("WBS Code,Task Title,Description,Status,Priority,Assigned To,Start Date,Due Date,Progress,Estimated Hours,Actual Hours");

                AppendTaskToCsv(csv, task, taskWithSubtasks);

                var bytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
                return File(bytes, "text/csv", $"Task_{task.WbsCode}_{DateTime.Now:yyyyMMdd}.csv");
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error exporting task: " + ex.Message });
            }
        }

        private List<TaskEntity> GetTaskWithAllSubtasks(TaskEntity task, List<TaskEntity> allTasks)
        {
            var result = new List<TaskEntity> { task };
            var subtasks = allTasks.Where(t => t.ParentTaskId == task.Id).ToList();

            foreach (var subtask in subtasks)
            {
                result.AddRange(GetTaskWithAllSubtasks(subtask, allTasks));
            }

            return result;
        }

        // POST: Wbs/ReorderTasks
        [HttpPost]
        public async Task<IActionResult> ReorderTasks(int projectId, int? parentTaskId, [FromBody] List<int> taskIds)
        {
            try
            {
                var success = await _wbsService.ReorderTasksAsync(projectId, parentTaskId, taskIds);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "WBS", projectId);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to reorder tasks." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error reordering tasks." });
            }
        }

        // GET: Wbs/GetStructure/5
        [HttpGet]
        public async Task<IActionResult> GetStructure(int projectId)
        {
            try
            {
                var wbsStructure = await _wbsService.GetWbsStructureAsync(projectId);

                var hierarchicalData = BuildHierarchicalStructure(wbsStructure);

                return Json(hierarchicalData);
            }
            catch (Exception ex)
            {
                return Json(new { error = "Error loading WBS structure." });
            }
        }

        // GET: Wbs/GetTaskDetails/5
        [HttpGet]
        public async Task<IActionResult> GetTaskDetails(int taskId)
        {
            try
            {
                var task = await _taskService.GetTaskByIdAsync(taskId);
                if (task == null)
                {
                    return NotFound("Task not found");
                }

                // Check if user has access to this project
                if (!await UserCanAccessProject(task.ProjectId))
                {
                    return Forbid("Access denied to this project");
                }

                // Get additional task details
                var comments = await _taskService.GetTaskCommentsAsync(taskId);

                var viewModel = new
                {
                    Task = task,
                    Comments = comments
                };

                return PartialView("_TaskDetailsContent", viewModel);
            }
            catch (Exception ex)
            {
                return BadRequest("Error loading task details: " + ex.Message);
            }
        }

        // POST: Wbs/ValidateStructure
        [HttpPost]
        public async Task<IActionResult> ValidateStructure(int projectId)
        {
            try
            {
                var isValid = await _wbsService.ValidateWbsStructureAsync(projectId);

                return Json(new { isValid, message = isValid ? "WBS structure is valid." : "WBS structure has issues." });
            }
            catch (Exception ex)
            {
                return Json(new { isValid = false, message = "Error validating WBS structure." });
            }
        }

        // POST: Wbs/CreateTask
        [HttpPost]
        public async Task<IActionResult> CreateTask([FromBody] CreateTaskRequest request)
        {
            try
            {
                // Get current user ID from claims (user is already authenticated via [Authorize])
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                // Check if user has access to create tasks in this project
                if (!await UserCanAccessProject(request.ProjectId))
                {
                    return Json(new { success = false, message = "Access denied to this project" });
                }

                // Generate WBS code for the new task
                var nextSortOrder = await _wbsService.GetNextSortOrderAsync(request.ProjectId, request.ParentTaskId);
                var wbsCode = await _wbsService.GenerateWbsCodeAsync(request.ProjectId, request.ParentTaskId, nextSortOrder);

                // Create the task entity
                var task = new TaskEntity
                {
                    Title = request.Title,
                    Description = request.Description,
                    Priority = request.Priority,
                    ProjectId = request.ProjectId,
                    AssignedToUserId = request.AssignedToUserId,
                    ParentTaskId = request.ParentTaskId,
                    StartDate = request.StartDate,
                    DueDate = request.DueDate,
                    EstimatedHours = request.EstimatedHours ?? 0,
                    WbsCode = wbsCode,
                    SortOrder = nextSortOrder,
                    CreatedByUserId = userId!
                };

                var createdTask = await _taskService.CreateTaskAsync(task);

                if (createdTask != null)
                {
                    await LogAuditAsync(Core.Enums.AuditAction.Create, "Task", createdTask.Id);
                    return Json(new { success = true, taskId = createdTask.Id, message = "Task created successfully" });
                }

                return Json(new { success = false, message = "Failed to create task" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error creating task: " + ex.Message });
            }
        }

        // GET: Wbs/Export
        [HttpGet]
        public async Task<IActionResult> Export(int projectId, string format = "excel")
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var wbsStructure = await _wbsService.GetWbsStructureAsync(projectId);

                switch (format.ToLower())
                {
                    case "excel":
                        return await ExportToExcel(wbsStructure, project.Name);
                    case "pdf":
                        return await ExportToPdf(wbsStructure, project.Name);
                    case "csv":
                        return await ExportToCsv(wbsStructure, project.Name);
                    case "json":
                        return await ExportToJson(wbsStructure, project.Name);
                    default:
                        return BadRequest("Unsupported export format");
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error exporting WBS structure.";
                return RedirectToAction("Index", new { projectId });
            }
        }

        private async Task<IActionResult> ExportToExcel(IEnumerable<PM.Tool.Core.Entities.TaskEntity> tasks, string projectName)
        {
            // For now, return a simple CSV format
            // In a real implementation, you would use a library like EPPlus or ClosedXML
            return await ExportToCsv(tasks, projectName);
        }

        private async Task<IActionResult> ExportToPdf(IEnumerable<PM.Tool.Core.Entities.TaskEntity> tasks, string projectName)
        {
            // For now, return a simple text format
            // In a real implementation, you would use a library like iTextSharp or PdfSharp
            var content = GenerateWbsText(tasks, projectName);
            var bytes = System.Text.Encoding.UTF8.GetBytes(content);
            return File(bytes, "text/plain", $"WBS_{projectName}_{DateTime.Now:yyyyMMdd}.txt");
        }

        private async Task<IActionResult> ExportToCsv(IEnumerable<PM.Tool.Core.Entities.TaskEntity> tasks, string projectName)
        {
            var csv = new System.Text.StringBuilder();
            csv.AppendLine("WBS Code,Task Title,Description,Status,Priority,Assigned To,Start Date,Due Date,Progress,Estimated Hours,Actual Hours");

            var taskList = tasks.ToList();
            var rootTasks = taskList.Where(t => !t.ParentTaskId.HasValue).OrderBy(t => t.SortOrder);

            foreach (var task in rootTasks)
            {
                AppendTaskToCsv(csv, task, taskList);
            }

            var bytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
            return File(bytes, "text/csv", $"WBS_{projectName}_{DateTime.Now:yyyyMMdd}.csv");
        }

        private async Task<IActionResult> ExportToJson(IEnumerable<PM.Tool.Core.Entities.TaskEntity> tasks, string projectName)
        {
            var hierarchicalData = BuildHierarchicalStructure(tasks);
            var jsonData = new
            {
                projectName = projectName,
                exportDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                tasks = hierarchicalData
            };

            var jsonString = System.Text.Json.JsonSerializer.Serialize(jsonData, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });

            var bytes = System.Text.Encoding.UTF8.GetBytes(jsonString);
            return File(bytes, "application/json", $"WBS_{projectName}_{DateTime.Now:yyyyMMdd}.json");
        }

        private void AppendTaskToCsv(System.Text.StringBuilder csv, PM.Tool.Core.Entities.TaskEntity task, List<PM.Tool.Core.Entities.TaskEntity> allTasks)
        {
            csv.AppendLine($"\"{task.WbsCode}\",\"{task.Title}\",\"{task.Description?.Replace("\"", "\"\"")}\",\"{task.Status}\",\"{task.Priority}\",\"{task.AssignedTo?.FullName}\",\"{task.StartDate?.ToString("yyyy-MM-dd")}\",\"{task.DueDate?.ToString("yyyy-MM-dd")}\",\"{task.Progress}\",\"{task.EstimatedHours}\",\"{task.ActualHours}\"");

            var childTasks = allTasks.Where(t => t.ParentTaskId == task.Id).OrderBy(t => t.SortOrder);
            foreach (var child in childTasks)
            {
                AppendTaskToCsv(csv, child, allTasks);
            }
        }

        private string GenerateWbsText(IEnumerable<PM.Tool.Core.Entities.TaskEntity> tasks, string projectName)
        {
            var text = new System.Text.StringBuilder();
            text.AppendLine($"Work Breakdown Structure - {projectName}");
            text.AppendLine($"Generated on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            text.AppendLine(new string('=', 50));
            text.AppendLine();

            var taskList = tasks.ToList();
            var rootTasks = taskList.Where(t => !t.ParentTaskId.HasValue).OrderBy(t => t.SortOrder);

            foreach (var task in rootTasks)
            {
                AppendTaskToText(text, task, taskList, 0);
            }

            return text.ToString();
        }

        private void AppendTaskToText(System.Text.StringBuilder text, PM.Tool.Core.Entities.TaskEntity task, List<PM.Tool.Core.Entities.TaskEntity> allTasks, int level)
        {
            var indent = new string(' ', level * 2);
            text.AppendLine($"{indent}{task.WbsCode} - {task.Title}");

            if (!string.IsNullOrEmpty(task.Description))
            {
                text.AppendLine($"{indent}  Description: {task.Description}");
            }

            text.AppendLine($"{indent}  Status: {task.Status}, Priority: {task.Priority}");

            if (task.AssignedTo != null)
            {
                text.AppendLine($"{indent}  Assigned to: {task.AssignedTo.FullName}");
            }

            if (task.DueDate.HasValue)
            {
                text.AppendLine($"{indent}  Due: {task.DueDate.Value:yyyy-MM-dd}");
            }

            text.AppendLine($"{indent}  Progress: {task.Progress}%");
            text.AppendLine();

            var childTasks = allTasks.Where(t => t.ParentTaskId == task.Id).OrderBy(t => t.SortOrder);
            foreach (var child in childTasks)
            {
                AppendTaskToText(text, child, allTasks, level + 1);
            }
        }

        private object BuildHierarchicalStructure(IEnumerable<PM.Tool.Core.Entities.TaskEntity> tasks)
        {
            var taskList = tasks.ToList();
            var rootTasks = taskList.Where(t => !t.ParentTaskId.HasValue).OrderBy(t => t.SortOrder);

            return rootTasks.Select(task => new
            {
                id = task.Id,
                title = task.Title,
                wbsCode = task.WbsCode,
                status = task.Status.ToString(),
                priority = task.Priority.ToString(),
                assignedTo = task.AssignedTo?.FullName,
                startDate = task.StartDate?.ToString("yyyy-MM-dd"),
                dueDate = task.DueDate?.ToString("yyyy-MM-dd"),
                progress = task.Progress,
                estimatedHours = task.EstimatedHours,
                actualHours = task.ActualHours,
                children = BuildChildTasks(task.Id, taskList)
            });
        }

        private object[] BuildChildTasks(int parentId, List<PM.Tool.Core.Entities.TaskEntity> allTasks)
        {
            var childTasks = allTasks.Where(t => t.ParentTaskId == parentId).OrderBy(t => t.SortOrder);

            return childTasks.Select(task => new
            {
                id = task.Id,
                title = task.Title,
                wbsCode = task.WbsCode,
                status = task.Status.ToString(),
                priority = task.Priority.ToString(),
                assignedTo = task.AssignedTo?.FullName,
                startDate = task.StartDate?.ToString("yyyy-MM-dd"),
                dueDate = task.DueDate?.ToString("yyyy-MM-dd"),
                progress = task.Progress,
                estimatedHours = task.EstimatedHours,
                actualHours = task.ActualHours,
                children = BuildChildTasks(task.Id, allTasks)
            }).ToArray();
        }
    }

    public class CreateTaskRequest
    {
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public TaskPriority Priority { get; set; } = TaskPriority.Medium;
        public int ProjectId { get; set; }
        public string? AssignedToUserId { get; set; }
        public int? ParentTaskId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? DueDate { get; set; }
        public int? EstimatedHours { get; set; }
    }
}
