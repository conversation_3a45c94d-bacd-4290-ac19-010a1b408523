@model IEnumerable<PM.Tool.Core.Entities.Agile.Sprint>

@if (Model.Any())
{
    <div class="space-y-4">
        @foreach (var sprint in Model)
        {
            <div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-rocket text-blue-600 dark:text-blue-400 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-neutral-900 dark:text-white">@sprint.Name</h3>
                                <p class="text-sm text-neutral-500 dark:text-neutral-400">
                                    @sprint.StartDate.ToString("MMM dd") - @sprint.EndDate.ToString("MMM dd, yyyy")
                                </p>
                            </div>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(sprint.Goal))
                        {
                            <p class="text-sm text-neutral-600 dark:text-neutral-300 mt-2">
                                <i class="fas fa-bullseye mr-1"></i>@sprint.Goal
                            </p>
                        }
                    </div>
                    
                    <div class="flex items-center gap-2">
                        <!-- Sprint Status Badge -->
                        <span class="px-2 py-1 text-xs font-medium rounded-full
                            @switch (sprint.Status)
                            {
                                case PM.Tool.Core.Entities.Agile.SprintStatus.Planning:
                                    @("bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400")
                                    break;
                                case PM.Tool.Core.Entities.Agile.SprintStatus.Active:
                                    @("bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400")
                                    break;
                                case PM.Tool.Core.Entities.Agile.SprintStatus.Completed:
                                    @("bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400")
                                    break;
                                case PM.Tool.Core.Entities.Agile.SprintStatus.Cancelled:
                                    @("bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400")
                                    break;
                                default:
                                    @("bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400")
                                    break;
                            }">
                            @sprint.Status
                        </span>
                        
                        <!-- Action Buttons -->
                        <div class="flex items-center gap-1">
                            <!-- Sprint Planning Button -->
                            <a href="@Url.Action("SprintPlanning", "Agile", new { projectId = sprint.ProjectId, sprintId = sprint.Id })" 
                               class="btn-compact btn-compact-secondary" 
                               title="Sprint Planning">
                                <i class="fas fa-tasks"></i>
                            </a>
                            
                            <!-- Kanban Board Button -->
                            <a href="@Url.Action("Board", "Agile", new { projectId = sprint.ProjectId, sprintId = sprint.Id })" 
                               class="btn-compact btn-compact-secondary" 
                               title="Kanban Board">
                                <i class="fas fa-columns"></i>
                            </a>
                            
                            <!-- Sprint Details Button -->
                            <a href="@Url.Action("SprintDetails", "Agile", new { id = sprint.Id })" 
                               class="btn-compact btn-compact-secondary" 
                               title="Sprint Details">
                                <i class="fas fa-eye"></i>
                            </a>
                            
                            <!-- Edit Sprint Button -->
                            <a href="@Url.Action("EditSprint", "Agile", new { id = sprint.Id })" 
                               class="btn-compact btn-compact-secondary" 
                               title="Edit Sprint">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Sprint Progress Bar (if active) -->
                @if (sprint.Status == PM.Tool.Core.Entities.Agile.SprintStatus.Active)
                {
                    var totalDays = (sprint.EndDate - sprint.StartDate).Days;
                    var elapsedDays = (DateTime.Now - sprint.StartDate).Days;
                    var progressPercentage = totalDays > 0 ? Math.Min(100, Math.Max(0, (elapsedDays * 100) / totalDays)) : 0;
                    
                    <div class="mt-3">
                        <div class="flex items-center justify-between text-xs text-neutral-500 dark:text-neutral-400 mb-1">
                            <span>Sprint Progress</span>
                            <span>@elapsedDays / @totalDays days</span>
                        </div>
                        <div class="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: @progressPercentage%"></div>
                        </div>
                    </div>
                }
            </div>
        }
    </div>
    
    <!-- Quick Actions -->
    <div class="mt-6 flex items-center justify-center gap-3">
        <a href="@Url.Action("CreateSprint", "Agile")" class="btn-compact btn-compact-primary">
            <i class="fas fa-plus"></i> New Sprint
        </a>
        <a href="@Url.Action("SprintPlanning", "Agile")" class="btn-compact btn-compact-secondary">
            <i class="fas fa-tasks"></i> Sprint Planning
        </a>
    </div>
}
else
{
    <!-- Empty State -->
    <div class="text-center py-12">
        <div class="w-16 h-16 bg-neutral-100 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-rocket text-2xl text-neutral-400 dark:text-neutral-500"></i>
        </div>
        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-2">No Sprints Found</h3>
        <p class="text-neutral-500 dark:text-neutral-400 mb-6">
            Create your first sprint to start planning and tracking your work.
        </p>
        <div class="flex items-center justify-center gap-3">
            <a href="@Url.Action("CreateSprint", "Agile")" class="btn-compact btn-compact-primary">
                <i class="fas fa-plus"></i> Create Sprint
            </a>
            <a href="@Url.Action("SprintPlanning", "Agile")" class="btn-compact btn-compact-secondary">
                <i class="fas fa-tasks"></i> Sprint Planning
            </a>
        </div>
    </div>
}
