@model IEnumerable<PM.Tool.Core.Entities.ResourceAllocation>

@{
    ViewData["Title"] = "Resource Allocations";
    var resource = ViewBag.Resource as PM.Tool.Core.Entities.Resource;
    ViewData["PageTitle"] = $"{resource?.Name} - Allocations";
    ViewData["PageDescription"] = "Manage resource allocations and scheduling";
    ViewData["BreadcrumbItems"] = new List<(string Text, string? Url)>
    {
        ("Resources", Url.Action("Index")),
        (resource?.Name ?? "Resource", Url.Action("Details", new { id = resource?.Id })),
        ("Allocations", null)
    };
}

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
            @if (resource != null)
            {
                <div class="w-12 h-12 @GetResourceTypeIconBg(resource.Type) rounded-lg flex items-center justify-center">
                    <i class="@GetResourceTypeIcon(resource.Type) text-white text-xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@resource.Name</h1>
                    <p class="text-neutral-600 dark:text-dark-300">Resource Allocations</p>
                </div>
            }
        </div>

        <div class="flex space-x-3">
            <a asp-action="Details" asp-route-id="@resource?.Id" class="btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Details
            </a>
            <a asp-action="CreateAllocation" asp-route-resourceId="@resource?.Id" class="btn-primary">
                <i class="fas fa-plus mr-2"></i>
                New Allocation
            </a>
        </div>
    </div>

    <!-- Resource Summary -->
    @if (resource != null)
    {
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            @{
                ViewData["Title"] = "Daily Capacity";
                ViewData["Icon"] = "fas fa-clock";
                ViewData["IconColor"] = "bg-gradient-to-br from-blue-500 to-blue-600";
                ViewData["Value"] = $"{resource.Capacity} hrs";
                ViewData["Description"] = "per day";
            }
            <partial name="Components/_StatsCard" view-data="ViewData" />

            @{
                ViewData["Title"] = "Hourly Rate";
                ViewData["Icon"] = "fas fa-dollar-sign";
                ViewData["IconColor"] = "bg-gradient-to-br from-green-500 to-green-600";
                ViewData["Value"] = resource.HourlyRate.ToString("C");
                ViewData["Description"] = "per hour";
            }
            <partial name="Components/_StatsCard" view-data="ViewData" />

            @{
                ViewData["Title"] = "Total Allocations";
                ViewData["Icon"] = "fas fa-calendar-alt";
                ViewData["IconColor"] = "bg-gradient-to-br from-purple-500 to-purple-600";
                ViewData["Value"] = Model.Count().ToString();
                ViewData["Description"] = "allocations";
            }
            <partial name="Components/_StatsCard" view-data="ViewData" />

            @{
                var activeAllocations = Model.Count(a => a.Status == PM.Tool.Core.Entities.AllocationStatus.Active);
                ViewData["Title"] = "Active";
                ViewData["Icon"] = "fas fa-play-circle";
                ViewData["IconColor"] = "bg-gradient-to-br from-orange-500 to-orange-600";
                ViewData["Value"] = activeAllocations.ToString();
                ViewData["Description"] = "active now";
            }
            <partial name="Components/_StatsCard" view-data="ViewData" />
        </div>
    }

    <!-- Filters -->
    <div class="card-custom">
        <div class="card-header-custom">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                <i class="fas fa-filter mr-2 text-primary-500"></i>
                Filters
            </h3>
        </div>
        <div class="card-body-custom">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="statusFilter" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Status</label>
                    <select id="statusFilter" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="Active">Active</option>
                        <option value="Planned">Planned</option>
                        <option value="Completed">Completed</option>
                        <option value="Cancelled">Cancelled</option>
                    </select>
                </div>

                <div>
                    <label for="startDateFilter" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Start Date From</label>
                    <input type="date" id="startDateFilter" class="form-input" />
                </div>

                <div>
                    <label for="endDateFilter" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">End Date To</label>
                    <input type="date" id="endDateFilter" class="form-input" />
                </div>

                <div class="flex items-end">
                    <button type="button" onclick="clearFilters()" class="btn-secondary w-full">
                        <i class="fas fa-times mr-2"></i>
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Allocations List -->
    <div class="card-custom">
        <div class="card-header-custom">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                <i class="fas fa-list mr-2 text-primary-500"></i>
                Allocations
            </h3>
        </div>
        <div class="card-body-custom">
            @if (Model.Any())
            {
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-neutral-200 dark:divide-dark-600">
                        <thead class="bg-neutral-50 dark:bg-dark-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Project
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Period
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Allocation
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-dark-800 divide-y divide-neutral-200 dark:divide-dark-600" id="allocationsTable">
                            @foreach (var allocation in Model.OrderByDescending(a => a.StartDate))
                            {
                                <tr class="allocation-row hover:bg-neutral-50 dark:hover:bg-dark-700"
                                    data-status="@allocation.Status.ToString().ToLower()"
                                    data-start-date="@allocation.StartDate.ToString("yyyy-MM-dd")"
                                    data-end-date="@allocation.EndDate.ToString("yyyy-MM-dd")">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="text-sm font-medium text-neutral-900 dark:text-dark-100">
                                                @(allocation.Project?.Name ?? "Unknown Project")
                                            </div>
                                            @if (!string.IsNullOrEmpty(allocation.Notes))
                                            {
                                                <div class="text-sm text-neutral-500 dark:text-dark-400">
                                                    @allocation.Notes
                                                </div>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-neutral-900 dark:text-dark-100">
                                            @allocation.StartDate.ToString("MMM dd, yyyy")
                                        </div>
                                        <div class="text-sm text-neutral-500 dark:text-dark-400">
                                            to @allocation.EndDate.ToString("MMM dd, yyyy")
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-neutral-900 dark:text-dark-100">
                                            @allocation.AllocationPercentage% (@allocation.AllocatedHours hrs)
                                        </div>
                                        <div class="text-sm text-neutral-500 dark:text-dark-400">
                                            @((allocation.AllocatedHours * (allocation.Resource?.HourlyRate ?? 0)).ToString("C")) total
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @{
                                            var statusBadge = allocation.Status switch
                                            {
                                                PM.Tool.Core.Entities.AllocationStatus.Active => "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                                PM.Tool.Core.Entities.AllocationStatus.Planned => "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                                PM.Tool.Core.Entities.AllocationStatus.Completed => "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-neutral-900 text-neutral-800 dark:text-neutral-200",
                                                PM.Tool.Core.Entities.AllocationStatus.Cancelled => "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                                _ => "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-neutral-900 text-neutral-800 dark:text-neutral-200"
                                            };
                                        }
                                        <span class="@statusBadge">@allocation.Status</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button type="button" class="text-primary-600 hover:text-primary-900 dark:text-primary-400"
                                                    onclick="viewAllocation(@allocation.Id)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400"
                                                    onclick="editAllocation(@allocation.Id)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="text-red-600 hover:text-red-900 dark:text-red-400"
                                                    onclick="deleteAllocation(@allocation.Id)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-12">
                    <i class="fas fa-calendar-times text-6xl text-neutral-400 dark:text-dark-500 mb-4"></i>
                    <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Allocations Found</h3>
                    <p class="text-neutral-600 dark:text-dark-300 mb-6">This resource has no allocations yet.</p>
                    <a asp-action="CreateAllocation" asp-route-resourceId="@resource?.Id" class="btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Create First Allocation
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Filter functionality
        function filterAllocations() {
            const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
            const startDateFilter = document.getElementById('startDateFilter').value;
            const endDateFilter = document.getElementById('endDateFilter').value;

            const rows = document.querySelectorAll('.allocation-row');

            rows.forEach(row => {
                let show = true;

                // Status filter
                if (statusFilter && !row.dataset.status.includes(statusFilter)) {
                    show = false;
                }

                // Date filters
                if (startDateFilter && row.dataset.startDate < startDateFilter) {
                    show = false;
                }

                if (endDateFilter && row.dataset.endDate > endDateFilter) {
                    show = false;
                }

                row.style.display = show ? '' : 'none';
            });
        }

        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('startDateFilter').value = '';
            document.getElementById('endDateFilter').value = '';
            filterAllocations();
        }

        // Add event listeners
        document.getElementById('statusFilter').addEventListener('change', filterAllocations);
        document.getElementById('startDateFilter').addEventListener('change', filterAllocations);
        document.getElementById('endDateFilter').addEventListener('change', filterAllocations);

        // Allocation actions
        function viewAllocation(id) {
            // Implement view allocation details
            console.log('View allocation:', id);
        }

        function editAllocation(id) {
            // Implement edit allocation
            console.log('Edit allocation:', id);
        }

        function deleteAllocation(id) {
            if (confirm('Are you sure you want to delete this allocation?')) {
                // Implement delete allocation
                console.log('Delete allocation:', id);
            }
        }
    </script>
}

@functions {
    private string GetResourceTypeIcon(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "fas fa-user",
            PM.Tool.Core.Entities.ResourceType.Equipment => "fas fa-tools",
            PM.Tool.Core.Entities.ResourceType.Material => "fas fa-boxes",
            PM.Tool.Core.Entities.ResourceType.Facility => "fas fa-building",
            _ => "fas fa-cube"
        };
    }

    private string GetResourceTypeIconBg(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-gradient-to-br from-blue-500 to-blue-600",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-gradient-to-br from-orange-500 to-orange-600",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-gradient-to-br from-green-500 to-green-600",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-gradient-to-br from-purple-500 to-purple-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }
}
