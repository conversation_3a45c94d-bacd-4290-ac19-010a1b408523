@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus
@{
    var task = ViewData["Task"] as PM.Tool.Core.Entities.TaskEntity;
    var allTasks = ViewData["AllTasks"] as List<PM.Tool.Core.Entities.TaskEntity>;
    var level = (int)(ViewData["Level"] ?? 0);
    var childTasks = allTasks?.Where(t => t.ParentTaskId == task?.Id).OrderBy(t => t.SortOrder).ThenBy(t => t.CreatedAt).ToList() ?? new List<PM.Tool.Core.Entities.TaskEntity>();
    var hasChildren = childTasks.Any();
    
    // Status and priority styling
    var statusClass = task?.Status switch
    {
        TaskStatus.Done => "success",
        TaskStatus.InProgress => "warning", 
        TaskStatus.InReview => "info",
        TaskStatus.Cancelled => "danger",
        _ => "secondary"
    };
    
    var priorityClass = task?.Priority switch
    {
        PM.Tool.Core.Enums.TaskPriority.Critical => "danger",
        PM.Tool.Core.Enums.TaskPriority.High => "warning",
        PM.Tool.Core.Enums.TaskPriority.Medium => "info",
        PM.Tool.Core.Enums.TaskPriority.Low => "secondary",
        _ => "secondary"
    };
    
    var isOverdue = task?.DueDate.HasValue == true && task.DueDate.Value < DateTime.Now && task.Status != TaskStatus.Done;
    var progressPercentage = task?.Progress ?? 0;
}

@if (task != null)
{
    <div class="task-node border border-neutral-200 dark:border-neutral-600 rounded-md p-2 @(isOverdue ? "border-l-2 border-l-danger-500" : "")"
         data-task-id="@task.Id"
         data-status="@task.Status"
         data-priority="@task.Priority"
         style="margin-left: @(level * 1.5)rem;">

        <!-- Task Header -->
        <div class="flex items-start justify-between mb-2">
            <div class="flex items-start flex-1">
                <!-- Expand/Collapse Icon -->
                @if (hasChildren)
                {
                    <button type="button"
                            class="flex-shrink-0 mr-2 mt-0.5 w-5 h-5 rounded bg-neutral-100 dark:bg-neutral-700 flex items-center justify-center hover:bg-neutral-200 dark:hover:bg-neutral-600 transition-colors"
                            onclick="toggleChildren(this)"
                            aria-expanded="true"
                            aria-label="Toggle child tasks"
                            title="Click to expand/collapse child tasks">
                        <i class="fas fa-chevron-down text-xs text-neutral-600 dark:text-neutral-400 toggle-icon"></i>
                    </button>
                }
                else
                {
                    <div class="flex-shrink-0 mr-2 mt-0.5 w-5 h-5 rounded bg-neutral-100 dark:bg-neutral-700 flex items-center justify-center">
                        <div class="w-1.5 h-1.5 rounded-full bg-neutral-400 dark:bg-neutral-500"></div>
                    </div>
                }

                <!-- Task Content -->
                <div class="flex-1 min-w-0">
                    <!-- Task Title and Badges Row -->
                    <div class="flex items-center justify-between mb-1">
                        <h4 class="task-title text-sm font-medium text-neutral-900 dark:text-dark-100 truncate mr-2">
                            @if (!string.IsNullOrEmpty(task.WbsCode))
                            {
                                <span class="text-neutral-500 dark:text-neutral-400 mr-2">@task.WbsCode</span>
                            }
                            @task.Title
                        </h4>
                        <div class="flex items-center gap-1 flex-shrink-0">
                            <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium @(statusClass == "success" ? "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200" : statusClass == "warning" ? "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200" : statusClass == "info" ? "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200" : statusClass == "danger" ? "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200" : "bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-200")">@task.Status</span>
                            <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium @(priorityClass == "danger" ? "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200" : priorityClass == "warning" ? "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200" : priorityClass == "info" ? "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200" : "bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-200")">@task.Priority</span>
                            @if (isOverdue)
                            {
                                <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </span>
                            }
                        </div>
                    </div>

                    <!-- Task Details (Compact) -->
                    <div class="flex items-center gap-3 text-xs text-neutral-500 dark:text-dark-400">
                        @if (task.AssignedTo != null)
                        {
                            <span class="flex items-center">
                                <i class="fas fa-user mr-1"></i>
                                @task.AssignedTo.FullName
                            </span>
                        }
                        @if (task.DueDate.HasValue)
                        {
                            <span class="flex items-center @(isOverdue ? "text-danger-600 dark:text-danger-400" : "")">
                                <i class="fas fa-calendar mr-1"></i>
                                @task.DueDate.Value.ToString("MMM dd")
                            </span>
                        }
                        @if (task.EstimatedHours > 0)
                        {
                            <span class="flex items-center">
                                <i class="fas fa-clock mr-1"></i>
                                @task.EstimatedHours.ToString("F0")h
                            </span>
                        }
                        @if (progressPercentage > 0)
                        {
                            <span class="flex items-center">
                                <i class="fas fa-chart-line mr-1"></i>
                                @progressPercentage.ToString("F0")%
                            </span>
                        }
                    </div>


                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center gap-1 ml-2">
                @{
                    ViewData["Text"] = "";
                    ViewData["Variant"] = "ghost";
                    ViewData["Size"] = "xs";
                    ViewData["Icon"] = "fas fa-eye";
                    ViewData["Href"] = Url.Action("Details", "Tasks", new { id = task.Id });
                    ViewData["Title"] = "View Details";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "";
                    ViewData["Variant"] = "ghost";
                    ViewData["Size"] = "xs";
                    ViewData["Icon"] = "fas fa-edit";
                    ViewData["Href"] = Url.Action("Edit", "Tasks", new { id = task.Id });
                    ViewData["Title"] = "Edit Task";
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>

        <!-- Child Tasks -->
        @if (hasChildren)
        {
            <div class="children-container mt-2 space-y-1">
                @foreach (var childTask in childTasks)
                {
                    ViewData["Task"] = childTask;
                    ViewData["AllTasks"] = allTasks;
                    ViewData["Level"] = level + 1;
                    @await Html.PartialAsync("_TaskNode", ViewData)
                }
            </div>
        }
    </div>
}



<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
