@{
    ViewData["Title"] = "Data Encryption System Demo";
    ViewData["Breadcrumbs"] = new List<object>
    {
        new { Text = "Home", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Encryption Demo", Href = "#", Icon = "fas fa-shield-alt" }
    };
}

<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-100 mb-2">
            <i class="fas fa-shield-alt text-primary-600 mr-3"></i>
            Data Encryption System Demo
        </h1>
        <p class="text-neutral-600 dark:text-dark-300">
            Test and demonstrate the comprehensive data encryption capabilities of PM.Tool
        </p>
    </div>

    <!-- Test Results Display -->
    @if (ViewBag.TestResults != null)
    {
        <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6 mb-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                <i class="fas fa-test-tube text-green-600 mr-2"></i>
                Encryption Test Results
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Original Data:</label>
                    <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border text-sm">@ViewBag.TestResults.OriginalData</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Encrypted Data:</label>
                    <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border text-sm font-mono break-all">@ViewBag.TestResults.EncryptedData</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Decrypted Data:</label>
                    <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border text-sm">@ViewBag.TestResults.DecryptedData</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Search Hash:</label>
                    <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border text-sm font-mono break-all">@ViewBag.TestResults.Hash</div>
                </div>
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm font-medium text-neutral-700 dark:text-dark-200">Encryption Status:</span>
                        @if (ViewBag.TestResults.EncryptionWorking)
                        {
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                <i class="fas fa-check-circle mr-1"></i>
                                Working Correctly
                            </span>
                        }
                        else
                        {
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                Error Detected
                            </span>
                        }
                        <span class="text-sm text-neutral-600 dark:text-dark-300">Hash Verified: @ViewBag.TestResults.HashVerified</span>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- File Test Results -->
    @if (ViewBag.FileTestResults != null)
    {
        <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6 mb-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                <i class="fas fa-file-shield text-blue-600 mr-2"></i>
                File Encryption Test Results
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">File Name:</label>
                    <div class="text-sm text-neutral-900 dark:text-dark-100">@ViewBag.FileTestResults.FileName</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Original Size:</label>
                    <div class="text-sm text-neutral-900 dark:text-dark-100">@ViewBag.FileTestResults.OriginalSize bytes</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Encrypted Size:</label>
                    <div class="text-sm text-neutral-900 dark:text-dark-100">@ViewBag.FileTestResults.EncryptedSize bytes</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Status:</label>
                    @if (ViewBag.FileTestResults.IsValid)
                    {
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            <i class="fas fa-check mr-1"></i>Valid
                        </span>
                    }
                    else
                    {
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            <i class="fas fa-times mr-1"></i>Invalid
                        </span>
                    }
                </div>
            </div>
        </div>
    }

    <!-- Generated Key Display -->
    @if (ViewBag.GeneratedKey != null)
    {
        <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6 mb-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                <i class="fas fa-key text-yellow-600 mr-2"></i>
                Generated Secure Key
            </h3>
            <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border font-mono text-sm break-all">
                @ViewBag.GeneratedKey
            </div>
            <p class="text-xs text-neutral-500 dark:text-dark-400 mt-2">
                <i class="fas fa-info-circle mr-1"></i>
                This key is cryptographically secure and suitable for production use.
            </p>
        </div>
    }

    <!-- Test Forms -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- String Encryption Test -->
        <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                <i class="fas fa-text-width text-blue-600 mr-2"></i>
                Test String Encryption
            </h3>
            <form asp-action="TestEncryption" method="post">
                <div class="mb-4">
                    <label for="testData" class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-2">
                        Test Data to Encrypt:
                    </label>
                    <textarea name="testData" id="testData" rows="3" 
                             class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg bg-white dark:bg-surface-dark text-neutral-900 dark:text-dark-100 placeholder-neutral-500 dark:placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                             placeholder="Enter sensitive data to test encryption..."></textarea>
                </div>
                <button type="submit" 
                        class="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    <i class="fas fa-lock mr-2"></i>
                    Test Encryption
                </button>
            </form>
        </div>

        <!-- Create Test Person -->
        <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                <i class="fas fa-user-plus text-green-600 mr-2"></i>
                Create Test Person (Auto-Encrypted)
            </h3>
            <form asp-action="CreateTestPerson" method="post">
                <div class="grid grid-cols-2 gap-3 mb-3">
                    <div>
                        <label for="firstName" class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">First Name:</label>
                        <input type="text" name="firstName" id="firstName" required
                               class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg bg-white dark:bg-surface-dark text-neutral-900 dark:text-dark-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label for="lastName" class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Last Name:</label>
                        <input type="text" name="lastName" id="lastName" required
                               class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg bg-white dark:bg-surface-dark text-neutral-900 dark:text-dark-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="email" class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Email:</label>
                    <input type="email" name="email" id="email" required
                           class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg bg-white dark:bg-surface-dark text-neutral-900 dark:text-dark-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div class="mb-4">
                    <label for="bio" class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Bio (Optional):</label>
                    <textarea name="bio" id="bio" rows="2"
                             class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg bg-white dark:bg-surface-dark text-neutral-900 dark:text-dark-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>
                <button type="submit" 
                        class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    <i class="fas fa-user-plus mr-2"></i>
                    Create Person
                </button>
            </form>
        </div>

        <!-- Search Encrypted Data -->
        <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                <i class="fas fa-search text-purple-600 mr-2"></i>
                Search Encrypted Data
            </h3>
            <form asp-action="SearchEncryptedData" method="post">
                <div class="mb-4">
                    <label for="searchTerm" class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-2">
                        Search Term:
                    </label>
                    <input type="text" name="searchTerm" id="searchTerm" required
                           class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg bg-white dark:bg-surface-dark text-neutral-900 dark:text-dark-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                           placeholder="Search by first name, last name, or email...">
                </div>
                <button type="submit" 
                        class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    <i class="fas fa-search mr-2"></i>
                    Search Encrypted Data
                </button>
            </form>
        </div>

        <!-- File Encryption Test -->
        <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                <i class="fas fa-file-upload text-orange-600 mr-2"></i>
                Test File Encryption
            </h3>
            <form asp-action="TestFileEncryption" method="post" enctype="multipart/form-data">
                <div class="mb-4">
                    <label for="file" class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-2">
                        Select File to Encrypt:
                    </label>
                    <input type="file" name="file" id="file" required
                           class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg bg-white dark:bg-surface-dark text-neutral-900 dark:text-dark-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
                <button type="submit" 
                        class="w-full bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    <i class="fas fa-file-shield mr-2"></i>
                    Test File Encryption
                </button>
            </form>
        </div>
    </div>

    <!-- Additional Tools -->
    <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6 mb-8">
        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
            <i class="fas fa-tools text-indigo-600 mr-2"></i>
            Encryption Tools
        </h3>
        <div class="flex flex-wrap gap-3">
            <form asp-action="GenerateSecureKey" method="post" class="inline">
                <input type="hidden" name="length" value="32">
                <button type="submit" 
                        class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    <i class="fas fa-key mr-2"></i>
                    Generate 32-byte Key
                </button>
            </form>
            <form asp-action="GenerateSecureKey" method="post" class="inline">
                <input type="hidden" name="length" value="64">
                <button type="submit" 
                        class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    <i class="fas fa-key mr-2"></i>
                    Generate 64-byte Key
                </button>
            </form>
        </div>
    </div>

    <!-- Sample Data Display -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Encrypted Users -->
        @if (ViewBag.Users != null && ((List<PM.Tool.Core.Entities.ApplicationUser>)ViewBag.Users).Any())
        {
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-users text-blue-600 mr-2"></i>
                    Sample Encrypted Users
                </h3>
                <div class="space-y-3">
                    @foreach (var user in (List<PM.Tool.Core.Entities.ApplicationUser>)ViewBag.Users)
                    {
                        <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border">
                            <div class="font-medium text-sm">@user.FirstName @user.LastName</div>
                            <div class="text-xs text-neutral-600 dark:text-dark-300">@user.Email</div>
                            @if (!string.IsNullOrEmpty(user.Bio))
                            {
                                <div class="text-xs text-neutral-500 dark:text-dark-400 mt-1">@user.Bio</div>
                            }
                        </div>
                    }
                </div>
            </div>
        }

        <!-- Encrypted Comments -->
        @if (ViewBag.Comments != null && ((List<PM.Tool.Core.Entities.TaskComment>)ViewBag.Comments).Any())
        {
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-comments text-green-600 mr-2"></i>
                    Sample Encrypted Comments
                </h3>
                <div class="space-y-3">
                    @foreach (var comment in (List<PM.Tool.Core.Entities.TaskComment>)ViewBag.Comments)
                    {
                        <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border">
                            <div class="text-sm">@comment.Content</div>
                            <div class="text-xs text-neutral-600 dark:text-dark-300 mt-1">
                                Task: @comment.Task?.Title | @comment.CreatedAt.ToString("MMM dd, yyyy")
                            </div>
                        </div>
                    }
                </div>
            </div>
        }

        <!-- Encrypted People -->
        @if (ViewBag.People != null && ((List<PM.Tool.Core.Entities.Person>)ViewBag.People).Any())
        {
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-address-book text-purple-600 mr-2"></i>
                    Sample Encrypted People
                </h3>
                <div class="space-y-3">
                    @foreach (var person in (List<PM.Tool.Core.Entities.Person>)ViewBag.People)
                    {
                        <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border">
                            <div class="font-medium text-sm">@person.FirstName @person.LastName</div>
                            <div class="text-xs text-neutral-600 dark:text-dark-300">@person.Email</div>
                            <div class="flex justify-between items-center mt-2">
                                <span class="text-xs text-neutral-500 dark:text-dark-400">@person.Type</span>
                                <a asp-action="ViewRawData" asp-route-personId="@person.Id" 
                                   class="text-xs text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                    View Raw Data
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>

    <!-- Search Results -->
    @if (ViewBag.SearchResults != null)
    {
        <div class="mt-6 bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                <i class="fas fa-search-plus text-purple-600 mr-2"></i>
                Search Results for "@ViewBag.SearchTerm"
            </h3>
            @if (((List<PM.Tool.Core.Entities.Person>)ViewBag.SearchResults).Any())
            {
                <div class="space-y-3">
                    @foreach (var person in (List<PM.Tool.Core.Entities.Person>)ViewBag.SearchResults)
                    {
                        <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-800">
                            <div class="font-medium text-sm">@person.FirstName @person.LastName</div>
                            <div class="text-xs text-neutral-600 dark:text-dark-300">@person.Email</div>
                            <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                                <i class="fas fa-check-circle mr-1"></i>
                                Found via encrypted search hash
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <p class="text-neutral-600 dark:text-dark-300">No matching records found.</p>
            }
            <div class="mt-3 p-3 bg-neutral-50 dark:bg-dark-400 rounded border">
                <div class="text-xs text-neutral-600 dark:text-dark-300">
                    <strong>Search Hash:</strong> <span class="font-mono">@ViewBag.SearchHash</span>
                </div>
            </div>
        </div>
    }
</div>
