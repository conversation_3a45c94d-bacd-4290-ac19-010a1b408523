@{
    var title = ViewData["Title"]?.ToString() ?? "";
    var icon = ViewData["Icon"]?.ToString() ?? "";
    var iconColor = ViewData["IconColor"]?.ToString() ?? "text-neutral-600 dark:text-neutral-400";
    var value = ViewData["Value"]?.ToString() ?? "";
    var description = ViewData["Description"]?.ToString() ?? "";
    var showProgress = ViewData["ShowProgress"] as bool? ?? false;
    var progressValue = ViewData["ProgressValue"] as decimal? ?? 0;
}

<div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 rounded-lg p-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-sm font-medium text-neutral-500 dark:text-neutral-400">@title</h3>
        @if (!string.IsNullOrEmpty(icon))
        {
            <i class="@icon @iconColor"></i>
        }
    </div>
    
    <div class="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">@value</div>
    
    @if (!string.IsNullOrEmpty(description))
    {
        <p class="text-sm text-neutral-500 dark:text-neutral-400">@description</p>
    }
    
    @if (showProgress)
    {
        <div class="w-full bg-neutral-200 dark:bg-neutral-600 rounded-full h-2 mt-3">
            <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: @progressValue%"></div>
        </div>
    }
</div>
