using System.ComponentModel.DataAnnotations;
using PM.Tool.Application.Services;

namespace PM.Tool.Core.Entities
{
    public class TaskComment
    {
        public int Id { get; set; }

        [Required]
        [<PERSON><PERSON>eng<PERSON>(2000)]
        [Encrypted]
        public string Content { get; set; } = string.Empty;

        public int TaskId { get; set; }

        public string UserId { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual TaskEntity Task { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }
}
