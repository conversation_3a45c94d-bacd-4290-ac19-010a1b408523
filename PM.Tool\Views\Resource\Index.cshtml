@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model IEnumerable<PM.Tool.Core.Entities.Resource>
@{
    ViewData["Title"] = "Resource Management";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Resource Management", Href = "", Icon = "fas fa-users-cog" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Standardized Page Header -->
@{
    ViewData["Title"] = "Resource Management";
    ViewData["Description"] = "Manage team resources, allocations, and capacity planning";
    ViewData["Icon"] = "fas fa-users-cog";

    ViewData["Actions"] = new[] {
        new { Text = "Add Resource", Href = Url.Action("Create"), Icon = "fas fa-plus", Variant = "primary", OnClick = (string?)null },
        new { Text = "Schedule View", Href = Url.Action("Schedule"), Icon = "fas fa-calendar-alt", Variant = "outline", OnClick = (string?)null },
        new { Text = "Utilization", Href = Url.Action("Utilization"), Icon = "fas fa-chart-bar", Variant = "outline", OnClick = (string?)null },
        new { Text = "Export", Href = "#", Icon = "fas fa-download", Variant = "secondary", OnClick = "exportResources()" }
    };

    ViewData["Stats"] = new[] {
        new { Label = "Total Resources", Value = Model?.Count().ToString() ?? "0", Icon = "fas fa-users", Color = "blue" },
        new { Label = "Active", Value = Model?.Count(r => r.IsActive).ToString() ?? "0", Icon = "fas fa-check-circle", Color = "green" },
        new { Label = "People", Value = Model?.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Human).ToString() ?? "0", Icon = "fas fa-user-tie", Color = "purple" },
        new { Label = "Equipment", Value = Model?.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Equipment).ToString() ?? "0", Icon = "fas fa-tools", Color = "amber" }
    };
}
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Standardized Filter Bar -->
<div class="card-custom mb-8">
    <form id="filterForm" method="get" class="card-body-custom">
        <!-- Single Row Layout -->
        <div class="flex flex-wrap items-center gap-6">
            <!-- Quick Filters Section -->
            <div class="flex items-center gap-3">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-3">Quick:</span>
                <div class="flex items-center gap-2">
                    @{
                        ViewData["Text"] = "Active";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-check-circle";
                        ViewData["OnClick"] = "filterResources('active')";
                        ViewData["AdditionalClasses"] = "quick-filter-btn";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "People";
                        ViewData["Icon"] = "fas fa-user-tie";
                        ViewData["OnClick"] = "filterResources('people')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "Equipment";
                        ViewData["Icon"] = "fas fa-tools";
                        ViewData["OnClick"] = "filterResources('equipment')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "Available";
                        ViewData["Icon"] = "fas fa-calendar-check";
                        ViewData["OnClick"] = "filterResources('available')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>

            <!-- Main Filters Section -->
            <div class="flex items-center gap-4 flex-1">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text"
                           name="search"
                           placeholder="Search resources..."
                           class="w-64 pl-11 pr-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 transition-all duration-200">
                </div>

                <!-- Filter Controls -->
                <div class="flex items-center gap-3">
                    <!-- Type Filter -->
                    <select name="type" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Types</option>
                        <option value="Human">Human</option>
                        <option value="Equipment">Equipment</option>
                        <option value="Material">Material</option>
                        <option value="Facility">Facility</option>
                    </select>

                    <!-- Status Filter -->
                    <select name="status" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Status</option>
                        <option value="true">Active</option>
                        <option value="false">Inactive</option>
                    </select>

                    <!-- Department Filter -->
                    <select name="department" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Departments</option>
                        <option value="Engineering">Engineering</option>
                        <option value="Design">Design</option>
                        <option value="Marketing">Marketing</option>
                        <option value="Operations">Operations</option>
                    </select>

                    <!-- Clear Filters Button -->
                    @{
                        ViewData["Text"] = "Clear";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-times";
                        ViewData["OnClick"] = "clearAllFilters()";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Resource Table -->
<div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm overflow-hidden" id="resourceContainer">
    @if (Model != null && Model.Any())
    {
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
                <thead class="bg-neutral-50 dark:bg-neutral-900">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Resource
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Type
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Department
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Location
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-neutral-800 divide-y divide-neutral-200 dark:divide-neutral-700">
                    @foreach (var resource in Model)
                    {
                        <tr class="resource-card <EMAIL>().ToLower() hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-colors duration-200"
                            data-type="@resource.Type.ToString().ToLower()"
                            data-status="@resource.IsActive.ToString().ToLower()"
                            data-department="@(resource.Department ?? "")"
                            data-location="@(resource.Location ?? "")">

                            <!-- Resource Name & Icon -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="w-10 h-10 @GetResourceTypeIconBg(resource.Type) rounded-lg flex items-center justify-center">
                                            <i class="@GetResourceTypeIcon(resource.Type) text-white text-sm"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-neutral-900 dark:text-white">
                                            @resource.Name
                                        </div>
                                        @if (!string.IsNullOrEmpty(resource.Description))
                                        {
                                            <div class="text-sm text-neutral-500 dark:text-neutral-400 truncate max-w-xs">
                                                @resource.Description
                                            </div>
                                        }
                                    </div>
                                </div>
                            </td>

                            <!-- Type -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetResourceTypeBadgeClass(resource.Type)">
                                    @resource.Type.ToString()
                                </span>
                            </td>

                            <!-- Status -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if (resource.IsActive)
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200">
                                        <div class="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-1.5"></div>
                                        Active
                                    </span>
                                }
                                else
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-200">
                                        <div class="w-1.5 h-1.5 bg-neutral-400 rounded-full mr-1.5"></div>
                                        Inactive
                                    </span>
                                }
                            </td>

                            <!-- Department -->
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-900 dark:text-white">
                                @(resource.Department ?? "-")
                            </td>

                            <!-- Location -->
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-900 dark:text-white">
                                @(resource.Location ?? "-")
                            </td>

                            <!-- Details -->
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-500 dark:text-neutral-400">
                                @if (resource.Type == PM.Tool.Core.Entities.ResourceType.Human)
                                {
                                    <div class="space-y-1">
                                        @if (resource.Capacity > 0)
                                        {
                                            <div>@resource.Capacity hrs/day</div>
                                        }
                                        @if (resource.HourlyRate > 0)
                                        {
                                            <div>@resource.HourlyRate.ToString("C")/hr</div>
                                        }
                                    </div>
                                }
                                else
                                {
                                    <span>-</span>
                                }
                            </td>

                            <!-- Actions -->
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="@Url.Action("Details", new { id = resource.Id })"
                                       class="inline-flex items-center justify-center w-8 h-8 text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200"
                                       title="View Details">
                                        <i class="fas fa-eye text-xs"></i>
                                    </a>
                                    <a href="@Url.Action("Edit", new { id = resource.Id })"
                                       class="inline-flex items-center justify-center w-8 h-8 text-neutral-600 dark:text-neutral-400 hover:text-amber-600 dark:hover:text-amber-400 hover:bg-amber-50 dark:hover:bg-amber-900/20 rounded-md transition-all duration-200"
                                       title="Edit Resource">
                                        <i class="fas fa-edit text-xs"></i>
                                    </a>
                                    <a href="@Url.Action("Allocations", new { id = resource.Id })"
                                       class="inline-flex items-center justify-center w-8 h-8 text-neutral-600 dark:text-neutral-400 hover:text-emerald-600 dark:hover:text-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 rounded-md transition-all duration-200"
                                       title="View Allocations">
                                        <i class="fas fa-calendar-alt text-xs"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>

        <!-- Simple Pagination -->
        <div class="bg-neutral-50 dark:bg-neutral-900 px-6 py-4 border-t border-neutral-200 dark:border-neutral-700">
            <div class="flex items-center justify-between">
                <!-- Results Info -->
                <div class="text-sm text-neutral-600 dark:text-neutral-400">
                    Showing <span id="showingFrom" class="font-medium">1</span> to <span id="showingTo" class="font-medium">10</span> of <span id="totalResources" class="font-medium">@Model.Count()</span> results
                </div>

                <!-- Pagination Controls -->
                <div class="flex items-center space-x-2">
                    <button id="prevPage" class="inline-flex items-center px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md text-sm font-medium text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
                        <i class="fas fa-chevron-left mr-1"></i>
                        Previous
                    </button>

                    <!-- Page Numbers -->
                    <div id="pageNumbers" class="flex items-center space-x-1">
                        <!-- Dynamic page numbers will be inserted here -->
                    </div>

                    <button id="nextPage" class="inline-flex items-center px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md text-sm font-medium text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
                        Next
                        <i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </div>
            </div>

            <!-- Mobile Pagination -->
            <div class="sm:hidden mt-4 flex justify-between">
                <button id="prevPageMobile" class="inline-flex items-center px-4 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md text-sm font-medium text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed">
                    Previous
                </button>
                <button id="nextPageMobile" class="inline-flex items-center px-4 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md text-sm font-medium text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed">
                    Next
                </button>
            </div>
        </div>
    }
    else
    {
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-16 h-16 bg-neutral-100 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-users-cog text-2xl text-neutral-400 dark:text-neutral-500"></i>
            </div>
            <h3 class="text-lg font-medium text-neutral-900 dark:text-white mb-2">No resources found</h3>
            <p class="text-neutral-500 dark:text-neutral-400 mb-6">
                Start building your team by adding resources like team members, equipment, and facilities to your project.
            </p>
            <div class="flex justify-center space-x-3">
                @{
                    ViewData["Text"] = "Add Resource";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Href"] = Url.Action("Create");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
    }
</div>

@section Styles {
    <style>
        /* Resource Management Styles (Following MyTask Pattern) */
        .resource-card {
            transition: all 0.2s ease;
        }

        /* Table Row Hover Effects */
        .resource-card:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        .dark .resource-card:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }

        /* Table Styling */
        .resource-table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .resource-table th {
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* Table Action Buttons */
        .table-action-btn {
            opacity: 0.7;
            transition: all 0.2s ease;
        }

        .resource-card:hover .table-action-btn {
            opacity: 1;
        }

        .table-action-btn:hover {
            transform: scale(1.1);
        }

        /* Pagination Styling */
        .page-btn {
            transition: all 0.2s ease;
        }

        .page-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .dark .page-btn:hover {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* Pagination container */
        .pagination-container {
            background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,250,252,0.98));
            backdrop-filter: blur(8px);
        }

        .dark .pagination-container {
            background: linear-gradient(135deg, rgba(31,41,55,0.95), rgba(17,24,39,0.98));
        }

        /* Inline Analytics Badges */
        .inline-analytics-badge {
            transition: all 0.2s ease;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .inline-analytics-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .dark .inline-analytics-badge {
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dark .inline-analytics-badge:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        /* Badge animations */
        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .inline-analytics-badge {
            animation: fadeInUp 0.3s ease-out;
        }

        .inline-analytics-badge:nth-child(1) { animation-delay: 0.1s; }
        .inline-analytics-badge:nth-child(2) { animation-delay: 0.2s; }
        .inline-analytics-badge:nth-child(3) { animation-delay: 0.3s; }
        .inline-analytics-badge:nth-child(4) { animation-delay: 0.4s; }
        .inline-analytics-badge:nth-child(5) { animation-delay: 0.5s; }
        .inline-analytics-badge:nth-child(6) { animation-delay: 0.6s; }

        /* Quick Filter Buttons */
        .quick-filter-btn {
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .quick-filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .quick-filter-btn.active {
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
            border-color: var(--primary-500);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        .dark .quick-filter-btn.active {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            border-color: var(--primary-400);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        /* Filter Bar Enhancement */
        .filter-bar {
            background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,250,252,0.98));
            backdrop-filter: blur(8px);
        }

        .dark .filter-bar {
            background: linear-gradient(135deg, rgba(31,41,55,0.95), rgba(17,24,39,0.98));
        }

        /* Enhanced Form Controls */
        input[type="text"], select {
            transition: all 0.2s ease;
        }

        input[type="text"]:focus, select:focus {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        /* Resource Type Indicators */
        .resource-type-human { border-left: 4px solid #3b82f6; }
        .resource-type-equipment { border-left: 4px solid #f59e0b; }
        .resource-type-material { border-left: 4px solid #10b981; }
        .resource-type-facility { border-left: 4px solid #8b5cf6; }

        /* Status Colors */
        .bg-success-100 { background-color: #dcfce7 !important; }
        .text-success-800 { color: #166534 !important; }
        .bg-success-900 { background-color: #14532d !important; }
        .text-success-200 { color: #bbf7d0 !important; }

        /* Action Buttons Enhancement */
        .resource-card .card-body-custom a {
            opacity: 0.7;
            transition: all 0.2s ease;
        }

        .resource-card:hover .card-body-custom a {
            opacity: 1;
        }

        .resource-card .card-body-custom a:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Action Dropdown Enhancement */
        .action-dropdown {
            transition: all 0.2s ease;
        }

        .action-dropdown:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .dark .action-dropdown:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        /* Dropdown Menu Animation & Z-Index */
        #options-dropdown {
            transition: all 0.2s ease;
            transform-origin: top right;
            z-index: 9999 !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .dark #options-dropdown {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }

        #options-dropdown.hidden {
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
            pointer-events: none;
        }

        #options-dropdown:not(.hidden) {
            opacity: 1;
            transform: scale(1) translateY(0);
            pointer-events: auto;
        }

        /* Ensure dropdown appears above all content */
        .relative {
            position: relative;
            z-index: auto;
        }

        .dropdown-container {
            z-index: 1000;
        }

        /* Responsive adjustments */
        @@media (max-width: 768px) {
            .resource-card .card-body-custom {
                padding: 1rem;
            }

            .filter-bar .flex {
                flex-direction: column;
                gap: 1rem;
            }

            /* Stack action buttons on mobile */
            .header-actions {
                flex-direction: column;
                width: 100%;
                gap: 0.5rem;
            }
        }
    </style>
}

@section Scripts {
    <script>
        // Simple pagination variables
        let currentPage = 1;
        let pageSize = 10;
        let allRows = [];
        let filteredRows = [];

        $(document).ready(function() {
            // Store all table rows
            allRows = $('.resource-card').toArray();
            filteredRows = [...allRows];

            // Initialize filters
            initializeResourceFilters();

            // Initialize quick filters
            initializeQuickFilters();

            // Initialize dropdown
            initializeDropdown();

            // Initialize pagination
            initializePagination();

            // Show initial page
            showPage(1);
        });

        function initializeResourceFilters() {
            $('#typeFilter, #statusFilter').on('change', applyFiltersAndPaginate);
            $('#searchInput').on('input', debounce(applyFiltersAndPaginate, 300));
            $('#pageSizeFilter').on('change', function() {
                pageSize = parseInt($(this).val());
                currentPage = 1;
                applyFiltersAndPaginate();
            });
            $('#clearFilters').on('click', clearAllFilters);
        }

        function initializeQuickFilters() {
            $('.quick-filter-btn').on('click', function() {
                const filter = $(this).data('filter');
                const [type, value] = filter.split(':');

                // Toggle active state
                $(this).toggleClass('active');

                // Apply filter
                if (type === 'status') {
                    $('#statusFilter').val(value === 'active' ? 'true' : '');
                } else if (type === 'type') {
                    $('#typeFilter').val(value.charAt(0).toUpperCase() + value.slice(1));
                }

                filterResources();
            });
        }

        function initializeDropdown() {
            const dropdownButton = $('#options-menu');
            const dropdownMenu = $('#options-dropdown');

            dropdownButton.on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                dropdownMenu.toggleClass('hidden');
            });

            // Close dropdown when clicking outside
            $(document).on('click', function(e) {
                if (!dropdownButton.is(e.target) && !dropdownButton.has(e.target).length &&
                    !dropdownMenu.is(e.target) && !dropdownMenu.has(e.target).length) {
                    dropdownMenu.addClass('hidden');
                }
            });

            // Close dropdown when pressing escape
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape') {
                    dropdownMenu.addClass('hidden');
                }
            });
        }

        function filterResources() {
            const typeFilter = $('#typeFilter').val().toLowerCase();
            const statusFilter = $('#statusFilter').val();
            const searchTerm = $('#searchInput').val().toLowerCase();

            $('.resource-card').each(function() {
                const row = $(this);
                const type = row.data('type');
                const status = row.data('status').toString();
                const name = row.find('.text-sm.font-medium').text().toLowerCase();
                const description = row.find('.text-sm.text-neutral-500').text().toLowerCase();

                let show = true;

                if (typeFilter && type !== typeFilter) show = false;
                if (statusFilter && status !== statusFilter) show = false;
                if (searchTerm && !name.includes(searchTerm) && !description.includes(searchTerm)) show = false;

                if (show) {
                    row.show();
                } else {
                    row.hide();
                }
            });

            // Update result count
            const visibleCount = $('.resource-card:visible').length;
            updateResultCount(visibleCount);
        }

        function initializePagination() {
            $('#prevPage, #prevPageMobile').on('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    showPage(currentPage);
                }
            });

            $('#nextPage, #nextPageMobile').on('click', function() {
                const totalPages = Math.ceil(filteredRows.length / pageSize);
                if (currentPage < totalPages) {
                    currentPage++;
                    showPage(currentPage);
                }
            });

            // Handle page number clicks
            $(document).on('click', '.page-btn', function() {
                currentPage = parseInt($(this).data('page'));
                showPage(currentPage);
            });
        }

        function applyFiltersAndPaginate() {
            // Apply filters
            const typeFilter = $('#typeFilter').val().toLowerCase();
            const statusFilter = $('#statusFilter').val();
            const searchTerm = $('#searchInput').val().toLowerCase();

            filteredRows = allRows.filter(function(row) {
                const $row = $(row);
                const type = $row.data('type');
                const status = $row.data('status').toString();
                const name = $row.find('.text-sm.font-medium').text().toLowerCase();
                const description = $row.find('.text-sm.text-neutral-500').text().toLowerCase();

                let show = true;
                if (typeFilter && type !== typeFilter) show = false;
                if (statusFilter && status !== statusFilter) show = false;
                if (searchTerm && !name.includes(searchTerm) && !description.includes(searchTerm)) show = false;

                return show;
            });

            // Reset to page 1 and show
            currentPage = 1;
            showPage(1);
        }

        function showPage(page) {
            // Hide all rows
            $(allRows).hide();

            // Calculate pagination
            const totalPages = Math.ceil(filteredRows.length / pageSize);
            const startIndex = (page - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, filteredRows.length);

            // Show current page rows
            for (let i = startIndex; i < endIndex; i++) {
                if (filteredRows[i]) {
                    $(filteredRows[i]).show();
                }
            }

            // Update pagination controls
            updatePaginationControls(startIndex + 1, endIndex, filteredRows.length, totalPages);
        }

        function updatePaginationControls(from, to, total, totalPages) {
            // Update info
            $('#showingFrom').text(total > 0 ? from : 0);
            $('#showingTo').text(to);
            $('#totalResources').text(total);

            // Update buttons
            $('#prevPage, #prevPageMobile').prop('disabled', currentPage <= 1);
            $('#nextPage, #nextPageMobile').prop('disabled', currentPage >= totalPages);

            // Update page numbers
            updatePageNumbers(totalPages);
        }

        function updatePageNumbers(totalPages) {
            const $pageNumbers = $('#pageNumbers');
            $pageNumbers.empty();

            if (totalPages <= 1) return;

            // Simple page number generation (max 5 visible)
            const maxVisible = 5;
            let start = Math.max(1, currentPage - 2);
            let end = Math.min(totalPages, start + maxVisible - 1);

            if (end - start < maxVisible - 1) {
                start = Math.max(1, end - maxVisible + 1);
            }

            for (let i = start; i <= end; i++) {
                const isActive = i === currentPage;
                const btnClass = isActive
                    ? 'bg-primary-600 text-white border-primary-600'
                    : 'bg-white dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700';

                $pageNumbers.append(`
                    <button class="page-btn relative inline-flex items-center px-4 py-2 border text-sm font-medium ${btnClass} transition-all duration-200" data-page="${i}">
                        ${i}
                    </button>
                `);
            }
        }

        function clearAllFilters() {
            $('#typeFilter, #statusFilter').val('');
            $('#searchInput').val('');
            $('.quick-filter-btn').removeClass('active');

            // Reset to all rows
            filteredRows = [...allRows];
            currentPage = 1;
            showPage(1);
        }

        // Utility function for debouncing
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
}

<style>
    .resource-card {
        transition: all 0.2s ease-in-out;
    }

    .resource-card:hover {
        transform: translateY(-2px);
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>

@functions {
    string GetResourceTypeIcon(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "fas fa-user",
            PM.Tool.Core.Entities.ResourceType.Equipment => "fas fa-tools",
            PM.Tool.Core.Entities.ResourceType.Material => "fas fa-boxes",
            PM.Tool.Core.Entities.ResourceType.Facility => "fas fa-building",
            _ => "fas fa-question"
        };
    }

    string GetResourceTypeIconBg(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-gradient-to-br from-blue-500 to-blue-600",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-gradient-to-br from-orange-500 to-orange-600",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-gradient-to-br from-green-500 to-green-600",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-gradient-to-br from-purple-500 to-purple-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }

    string GetResourceTypeBadgeClass(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-200",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200",
            _ => "bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-200"
        };
    }
}
