@* Task Create Scripts *@
@{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

<script>
    $(document).ready(function() {
        // Initialize task creation functionality
        initializeTaskCreate();
    });

    function initializeTaskCreate() {
        // Initialize form validation
        initializeFormValidation();
        
        // Initialize date validation
        initializeDateValidation();
        
        // Initialize markdown editor
        initializeMarkdownEditor();
    }

    function initializeFormValidation() {
        // Form validation styling
        $('form').on('submit', function(e) {
            var isValid = true;

            // Check required fields
            $(this).find('[required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('border-danger-300 dark:border-danger-600');
                } else {
                    $(this).removeClass('border-danger-300 dark:border-danger-600');
                }
            });

            if (!isValid) {
                e.preventDefault();
                // Show error message
                if (!$('.alert-danger-custom').length) {
                    $('form').prepend(`
                        <div class="alert-danger-custom mb-6">
                            <i class="fas fa-exclamation-circle text-danger-600 dark:text-danger-400"></i>
                            <div>
                                <p class="font-medium">Validation Error</p>
                                <p class="text-sm">Please fill in all required fields.</p>
                            </div>
                        </div>
                    `);
                }
            }
        });
    }

    function initializeDateValidation() {
        // Date validation
        $('input[name="DueDate"]').on('change', function() {
            var startDate = $('input[name="StartDate"]').val();
            var dueDate = $(this).val();

            if (startDate && dueDate && new Date(dueDate) < new Date(startDate)) {
                $(this).addClass('border-danger-300 dark:border-danger-600');
                if (!$(this).siblings('.text-danger-600').length) {
                    $(this).after('<p class="text-sm text-danger-600 dark:text-danger-400 mt-1">Due date must be after start date</p>');
                }
            } else {
                $(this).removeClass('border-danger-300 dark:border-danger-600');
                $(this).siblings('.text-danger-600').remove();
            }
        });
    }

    function initializeMarkdownEditor() {
        // Initialize markdown editor if element exists
        var markdownElement = document.querySelector('.markdown-editor');
        if (markdownElement && typeof EasyMDE !== 'undefined') {
            var easyMDE = new EasyMDE({
                element: markdownElement,
                spellChecker: false,
                status: false,
                placeholder: 'Enter task description...',
                toolbar: ["bold", "italic", "heading", "|", "quote", "unordered-list", "ordered-list", "|", "link", "preview"]
            });
        }
    }
</script>
