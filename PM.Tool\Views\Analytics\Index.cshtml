@{
    ViewData["Title"] = "Analytics Dashboard";
    var projects = ViewBag.Projects as IEnumerable<PM.Tool.Core.Entities.Project> ?? new List<PM.Tool.Core.Entities.Project>();
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Analytics", Href = (string?)null, Icon = "fas fa-chart-line" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Standardized Page Header -->
@{
    ViewData["Title"] = "Analytics Dashboard";
    ViewData["Description"] = "Comprehensive project insights and performance metrics";
    ViewData["Icon"] = "fas fa-chart-line";

    ViewData["Actions"] = new[] {
        new { Text = "Export Data", Href = (string?)null, Icon = "fas fa-download", Variant = "primary", OnClick = (string?)"exportAnalyticsData()" },
        new { Text = "Filter Projects", Href = (string?)null, Icon = "fas fa-filter", Variant = "outline", OnClick = (string?)"toggleProjectFilter()" },
        new { Text = "Refresh Data", Href = (string?)null, Icon = "fas fa-sync-alt", Variant = "secondary", OnClick = (string?)"refreshAnalytics()" }
    };

    ViewData["Stats"] = new[] {
        new { Label = "Total Projects", Value = projects.Count().ToString(), Icon = "fas fa-project-diagram", Color = "blue" },
        new { Label = "Active Projects", Value = projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Active).ToString(), Icon = "fas fa-play-circle", Color = "green" },
        new { Label = "Completed", Value = projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Completed).ToString(), Icon = "fas fa-check-circle", Color = "emerald" },
        new { Label = "On Track", Value = "85%", Icon = "fas fa-chart-line", Color = "amber" }
    };
}
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Project Filter Dropdown (Hidden by default, shown when filter button is clicked) -->
<div id="project-filter-menu"
     class="fixed top-20 right-4 w-64 bg-white dark:bg-surface-dark rounded-xl shadow-lg border border-neutral-200 dark:border-dark-200 py-2 hidden max-h-64 overflow-y-auto z-50">
    @if (projects.Any())
    {
        <div class="px-4 py-2 border-b border-neutral-200 dark:border-dark-200">
            <p class="text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide">Select Project</p>
        </div>
        @foreach (var project in projects)
        {
            <a href="@Url.Action("Project", new { id = project.Id })"
               class="flex items-center px-4 py-3 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-project-diagram text-primary-600 dark:text-primary-400 text-xs"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">@project.Name</p>
                    <p class="text-xs text-neutral-500 dark:text-dark-400">@project.Status</p>
                </div>
            </a>
        }
    }
    else
    {
        <div class="px-4 py-8 text-center">
            <div class="w-12 h-12 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-project-diagram text-neutral-400 dark:text-dark-500"></i>
            </div>
            <p class="text-sm text-neutral-500 dark:text-dark-400">No projects available</p>
        </div>
    }
</div>



<!-- Analytics Charts Section -->
@if (projects.Any())
{
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
        <!-- Project Status Distribution -->
        @{
            ViewData["Title"] = "Project Status Distribution";
            ViewData["Icon"] = "fas fa-chart-pie";
            ViewData["Description"] = "Overview of project statuses across your portfolio";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="relative h-80">
                <canvas id="projectStatusChart" class="w-full h-full"></canvas>
            </div>
            <div class="mt-4 grid grid-cols-2 gap-4">
                <div class="text-center">
                    <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Active)</div>
                    <div class="text-xs text-neutral-500 dark:text-dark-400">Active Projects</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Completed)</div>
                    <div class="text-xs text-neutral-500 dark:text-dark-400">Completed Projects</div>
                </div>
            </div>
        </partial>

        <!-- Progress Overview -->
        @{
            ViewData["Title"] = "Progress Overview";
            ViewData["Icon"] = "fas fa-chart-bar";
            ViewData["Description"] = "Project completion progress and performance metrics";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="relative h-80">
                <canvas id="progressChart" class="w-full h-full"></canvas>
            </div>
            <div class="mt-4 flex justify-between text-center">
                <div>
                    <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@(projects.Any() ? projects.Average(p => p.ProgressPercentage).ToString("F1") : "0")%</div>
                    <div class="text-xs text-neutral-500 dark:text-dark-400">Average Progress</div>
                </div>
                <div>
                    <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@projects.Count(p => p.ProgressPercentage >= 75)</div>
                    <div class="text-xs text-neutral-500 dark:text-dark-400">Near Completion</div>
                </div>
                <div>
                    <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@projects.Count(p => p.ProgressPercentage < 25)</div>
                    <div class="text-xs text-neutral-500 dark:text-dark-400">Just Started</div>
                </div>
            </div>
        </partial>
    </div>

    <!-- Additional Analytics Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Activity -->
        @{
            ViewData["Title"] = "Recent Activity";
            ViewData["Icon"] = "fas fa-clock";
            ViewData["Description"] = "Latest project updates and milestones";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-4">
                @for (int i = 0; i < Math.Min(5, projects.Count()); i++)
                {
                    var project = projects.Skip(i).First();
                    <div class="flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-project-diagram text-primary-600 dark:text-primary-400 text-xs"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-neutral-900 dark:text-dark-100 truncate">@project.Name</p>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@project.ProgressPercentage.ToString("F0")% complete</p>
                        </div>
                        <div class="text-xs text-neutral-400 dark:text-dark-500">
                            @project.UpdatedAt?.ToString("MMM dd") ?? "N/A"
                        </div>
                    </div>
                }
            </div>
        </partial>

        <!-- Performance Metrics -->
        @{
            ViewData["Title"] = "Performance Metrics";
            ViewData["Icon"] = "fas fa-tachometer-alt";
            ViewData["Description"] = "Key performance indicators and trends";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-4">
                <div class="flex justify-between items-center p-3 bg-success-50 dark:bg-success-900/20 rounded-lg">
                    <div>
                        <p class="text-sm font-medium text-success-800 dark:text-success-200">On-Time Delivery</p>
                        <p class="text-xs text-success-600 dark:text-success-400">Projects completed on schedule</p>
                    </div>
                    <div class="text-lg font-bold text-success-800 dark:text-success-200">87%</div>
                </div>

                <div class="flex justify-between items-center p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
                    <div>
                        <p class="text-sm font-medium text-primary-800 dark:text-primary-200">Resource Utilization</p>
                        <p class="text-xs text-primary-600 dark:text-primary-400">Team capacity usage</p>
                    </div>
                    <div class="text-lg font-bold text-primary-800 dark:text-primary-200">92%</div>
                </div>

                <div class="flex justify-between items-center p-3 bg-warning-50 dark:bg-warning-900/20 rounded-lg">
                    <div>
                        <p class="text-sm font-medium text-warning-800 dark:text-warning-200">Budget Efficiency</p>
                        <p class="text-xs text-warning-600 dark:text-warning-400">Cost vs. planned budget</p>
                    </div>
                    <div class="text-lg font-bold text-warning-800 dark:text-warning-200">95%</div>
                </div>
            </div>
        </partial>

        <!-- Quick Actions -->
        @{
            ViewData["Title"] = "Quick Actions";
            ViewData["Icon"] = "fas fa-bolt";
            ViewData["Description"] = "Frequently used analytics tools";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-3">
                @{
                    ViewData["Text"] = "Export All Data";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-download";
                    ViewData["Classes"] = "w-full justify-center";
                    ViewData["OnClick"] = "exportAllData()";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Schedule Report";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-calendar";
                    ViewData["Classes"] = "w-full justify-center";
                    ViewData["OnClick"] = "scheduleReport()";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Custom Dashboard";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-chart-line";
                    ViewData["Classes"] = "w-full justify-center";
                    ViewData["Href"] = Url.Action("CustomDashboard");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </partial>
    </div>
}
else
{
    <!-- Empty State -->
        ViewData["Title"] = "Analytics Dashboard";
        ViewData["Icon"] = "fas fa-chart-line";
    <partial name="Components/_Card" view-data="ViewData">
        <div class="text-center py-16">
            <div class="w-24 h-24 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900 dark:to-primary-800 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-chart-line text-3xl text-primary-600 dark:text-primary-400"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-3">No Analytics Data Available</h3>
            <p class="text-neutral-600 dark:text-dark-300 mb-8 max-w-md mx-auto leading-relaxed">
                Create your first project to start seeing comprehensive analytics and insights about your team's performance and productivity.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @{
                    ViewData["Text"] = "Create First Project";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Href"] = Url.Action("Create", "Projects");
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Learn About Analytics";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-question-circle";
                    ViewData["OnClick"] = "showAnalyticsHelp()";
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>

            <!-- Feature Preview -->
            <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
                <div class="p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                    <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-chart-bar text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <h4 class="font-semibold text-neutral-900 dark:text-dark-100 mb-2">Project Insights</h4>
                    <p class="text-sm text-neutral-600 dark:text-dark-300">Track progress, deadlines, and team performance across all projects.</p>
                </div>

                <div class="p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                    <div class="w-10 h-10 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-users text-success-600 dark:text-success-400"></i>
                    </div>
                    <h4 class="font-semibold text-neutral-900 dark:text-dark-100 mb-2">Team Analytics</h4>
                    <p class="text-sm text-neutral-600 dark:text-dark-300">Monitor resource utilization and collaboration patterns.</p>
                </div>

                <div class="p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                    <div class="w-10 h-10 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-file-chart-line text-warning-600 dark:text-warning-400"></i>
                    </div>
                    <h4 class="font-semibold text-neutral-900 dark:text-dark-100 mb-2">Custom Reports</h4>
                    <p class="text-sm text-neutral-600 dark:text-dark-300">Generate detailed reports and export data for stakeholders.</p>
                </div>
            </div>
        </div>
    </partial>
}

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize dropdown toggles
            const projectFilterToggle = document.getElementById('project-filter-toggle');
            const projectFilterMenu = document.getElementById('project-filter-menu');
            const projectAnalyticsToggle = document.getElementById('project-analytics-toggle');
            const projectAnalyticsMenu = document.getElementById('project-analytics-menu');

            function toggleDropdown(toggle, menu) {
                if (toggle && menu) {
                    toggle.addEventListener('click', function(e) {
                        e.stopPropagation();
                        menu.classList.toggle('hidden');

                        // Close other dropdowns
                        document.querySelectorAll('[id$="-menu"]').forEach(function(otherMenu) {
                            if (otherMenu !== menu) {
                                otherMenu.classList.add('hidden');
                            }
                        });
                    });
                }
            }

            toggleDropdown(projectFilterToggle, projectFilterMenu);
            toggleDropdown(projectAnalyticsToggle, projectAnalyticsMenu);

            // Close dropdowns when clicking outside
            document.addEventListener('click', function() {
                document.querySelectorAll('[id$="-menu"]').forEach(function(menu) {
                    menu.classList.add('hidden');
                });
            });

            // Initialize charts if Chart.js is loaded and projects exist
            if (typeof Chart !== 'undefined' && @projects.Count() > 0) {
                try {
                    // Configure Chart.js defaults for dark mode
                    Chart.defaults.color = document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151';
                    Chart.defaults.borderColor = document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb';

                    // Project Status Chart
                    const statusCtx = document.getElementById('projectStatusChart');
                    if (statusCtx) {
                        new Chart(statusCtx.getContext('2d'), {
                            type: 'doughnut',
                            data: {
                                labels: ['Active', 'Planning', 'Completed', 'On Hold'],
                                datasets: [{
                                    data: [
                                        @projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Active),
                                        @projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Planning),
                                        @projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Completed),
                                        @projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.OnHold)
                                    ],
                                    backgroundColor: [
                                        '#10b981', // success-500
                                        '#3b82f6', // primary-500
                                        '#8b5cf6', // purple-500
                                        '#f59e0b'  // warning-500
                                    ],
                                    borderWidth: 2,
                                    borderColor: document.documentElement.classList.contains('dark') ? '#1f2937' : '#ffffff'
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                        labels: {
                                            padding: 20,
                                            usePointStyle: true,
                                            font: {
                                                size: 12
                                            }
                                        }
                                    },
                                    tooltip: {
                                        backgroundColor: document.documentElement.classList.contains('dark') ? '#1f2937' : '#ffffff',
                                        titleColor: document.documentElement.classList.contains('dark') ? '#f9fafb' : '#111827',
                                        bodyColor: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151',
                                        borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                                        borderWidth: 1
                                    }
                                }
                            }
                        });
                    }

                    // Progress Chart
                    const progressCtx = document.getElementById('progressChart');
                    if (progressCtx) {
                        new Chart(progressCtx.getContext('2d'), {
                            type: 'bar',
                            data: {
                                labels: [@Html.Raw(string.Join(",", projects.Take(8).Select(p => $"'{p.Name}'").ToArray()))],
                                datasets: [{
                                    label: 'Progress %',
                                    data: [@string.Join(",", projects.Take(8).Select(p => p.ProgressPercentage.ToString("F0")).ToArray())],
                                    backgroundColor: 'rgba(59, 130, 246, 0.8)', // primary-500 with opacity
                                    borderColor: '#3b82f6', // primary-500
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderSkipped: false,
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: false
                                    },
                                    tooltip: {
                                        backgroundColor: document.documentElement.classList.contains('dark') ? '#1f2937' : '#ffffff',
                                        titleColor: document.documentElement.classList.contains('dark') ? '#f9fafb' : '#111827',
                                        bodyColor: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151',
                                        borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                                        borderWidth: 1
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        max: 100,
                                        ticks: {
                                            callback: function(value) {
                                                return value + '%';
                                            }
                                        },
                                        grid: {
                                            color: document.documentElement.classList.contains('dark') ? '#374151' : '#f3f4f6'
                                        }
                                    },
                                    x: {
                                        grid: {
                                            display: false
                                        },
                                        ticks: {
                                            maxRotation: 45,
                                            minRotation: 0
                                        }
                                    }
                                }
                            }
                        });
                    }
                } catch (error) {
                    console.error('Chart initialization error:', error);
                }
            }

            // Animate stats cards on load
            $('.stats-card-custom').each(function(index) {
                $(this).delay(index * 150).queue(function() {
                    $(this).addClass('animate-fade-in-up').dequeue();
                });
            });
        });

        // Helper functions for new features
        function toggleProjectFilter() {
            try {
                const menu = document.getElementById('project-filter-menu');
                if (menu) {
                    menu.classList.toggle('hidden');
                }
            } catch (error) {
                console.error('Error in toggleProjectFilter:', error);
            }
        }

        function toggleProjectAnalytics() {
            try {
                const menu = document.getElementById('project-analytics-menu');
                if (menu) {
                    menu.classList.toggle('hidden');
                }
            } catch (error) {
                console.error('Error in toggleProjectAnalytics:', error);
            }
        }

        function exportAnalyticsData() {
            try {
                // Implementation for exporting analytics data
                console.log('Export analytics data requested');
                alert('Export functionality will be implemented');
            } catch (error) {
                console.error('Error in exportAnalyticsData:', error);
            }
        }

        function refreshAnalytics() {
            try {
                // Implementation for refreshing analytics data
                console.log('Refresh analytics requested');
                location.reload();
            } catch (error) {
                console.error('Error in refreshAnalytics:', error);
            }
        }

        function exportAllData() {
            try {
                // Implementation for exporting all data
                console.log('Export all data requested');
                alert('Export all data functionality will be implemented');
            } catch (error) {
                console.error('Error in exportAllData:', error);
            }
        }

        function scheduleReport() {
            try {
                // Implementation for scheduling reports
                console.log('Schedule report requested');
                alert('Schedule report functionality will be implemented');
            } catch (error) {
                console.error('Error in scheduleReport:', error);
            }
        }

        function showAnalyticsHelp() {
            try {
                // Implementation for showing analytics help
                console.log('Show analytics help requested');
                alert('Analytics help will be implemented');
            } catch (error) {
                console.error('Error in showAnalyticsHelp:', error);
            }
        }
    </script>
}
