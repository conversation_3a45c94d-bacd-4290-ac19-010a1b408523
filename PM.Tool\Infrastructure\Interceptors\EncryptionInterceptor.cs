using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using PM.Tool.Application.Services;
using System.Reflection;

namespace PM.Tool.Infrastructure.Interceptors
{
    /// <summary>
    /// Entity Framework interceptor that automatically encrypts/decrypts data
    /// based on the [Encrypted] attribute
    /// </summary>
    public class EncryptionInterceptor : SaveChangesInterceptor
    {
        private readonly IDataEncryptionService _encryptionService;
        private readonly ILogger<EncryptionInterceptor> _logger;

        public EncryptionInterceptor(
            IDataEncryptionService encryptionService,
            ILogger<EncryptionInterceptor> logger)
        {
            _encryptionService = encryptionService;
            _logger = logger;
        }

        public override InterceptionResult<int> SavingChanges(
            DbContextEventData eventData,
            InterceptionResult<int> result)
        {
            ProcessEntities(eventData.Context);
            return base.SavingChanges(eventData, result);
        }

        public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
            DbContextEventData eventData,
            InterceptionResult<int> result,
            CancellationToken cancellationToken = default)
        {
            ProcessEntities(eventData.Context);
            return base.SavingChangesAsync(eventData, result, cancellationToken);
        }

        private void ProcessEntities(DbContext? context)
        {
            if (context == null) return;

            try
            {
                var entries = context.ChangeTracker.Entries()
                    .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

                foreach (var entry in entries)
                {
                    EncryptEntityProperties(entry.Entity);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing entities for encryption");
                throw;
            }
        }

        private void EncryptEntityProperties(object entity)
        {
            if (entity == null) return;

            var entityType = entity.GetType();
            var properties = entityType.GetProperties()
                .Where(p => p.GetCustomAttribute<EncryptedAttribute>() != null);

            foreach (var property in properties)
            {
                try
                {
                    var value = property.GetValue(entity) as string;
                    if (string.IsNullOrEmpty(value)) continue;

                    // Check if already encrypted (basic check)
                    if (IsAlreadyEncrypted(value)) continue;

                    var encryptedValue = _encryptionService.EncryptString(value);
                    property.SetValue(entity, encryptedValue);

                    // Handle searchable hash if needed
                    var attribute = property.GetCustomAttribute<EncryptedAttribute>();
                    if (attribute?.SearchableHash == true)
                    {
                        var hashPropertyName = $"{property.Name}Hash";
                        var hashProperty = entityType.GetProperty(hashPropertyName);
                        if (hashProperty != null)
                        {
                            var hash = _encryptionService.HashForSearch(value);
                            hashProperty.SetValue(entity, hash);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error encrypting property {PropertyName} on entity {EntityType}",
                        property.Name, entityType.Name);
                    throw;
                }
            }
        }

        private static bool IsAlreadyEncrypted(string value)
        {
            // Simple heuristic: encrypted values are typically base64 encoded
            // and don't contain common readable patterns
            if (string.IsNullOrEmpty(value)) return false;
            
            // Check if it looks like base64 (basic check)
            try
            {
                var buffer = Convert.FromBase64String(value);
                return buffer.Length > 0 && !value.Any(char.IsWhiteSpace);
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// Entity Framework interceptor for automatically decrypting data when reading
    /// </summary>
    public class DecryptionInterceptor : IDbCommandInterceptor
    {
        private readonly IDataEncryptionService _encryptionService;
        private readonly ILogger<DecryptionInterceptor> _logger;

        public DecryptionInterceptor(
            IDataEncryptionService encryptionService,
            ILogger<DecryptionInterceptor> logger)
        {
            _encryptionService = encryptionService;
            _logger = logger;
        }

        // Note: For reading, we'll handle decryption in the repository layer
        // or through a custom value converter to avoid performance issues
        // This interceptor is kept for future enhancements
    }

    /// <summary>
    /// Extension methods for encryption interceptor registration
    /// </summary>
    public static class EncryptionInterceptorExtensions
    {
        public static DbContextOptionsBuilder AddEncryptionInterceptors(
            this DbContextOptionsBuilder optionsBuilder,
            IServiceProvider serviceProvider)
        {
            var encryptionService = serviceProvider.GetRequiredService<IDataEncryptionService>();
            var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();

            optionsBuilder.AddInterceptors(
                new EncryptionInterceptor(
                    encryptionService,
                    loggerFactory.CreateLogger<EncryptionInterceptor>()),
                new DecryptionInterceptor(
                    encryptionService,
                    loggerFactory.CreateLogger<DecryptionInterceptor>())
            );

            return optionsBuilder;
        }
    }

    /// <summary>
    /// Repository extension methods for handling encrypted entities
    /// </summary>
    public static class EncryptedEntityExtensions
    {
        public static void DecryptEntity<T>(this T entity, IDataEncryptionService encryptionService) 
            where T : class
        {
            EncryptionHelper.DecryptEntity(entity, encryptionService);
        }

        public static void EncryptEntity<T>(this T entity, IDataEncryptionService encryptionService) 
            where T : class
        {
            EncryptionHelper.EncryptEntity(entity, encryptionService);
        }

        public static IQueryable<T> DecryptResults<T>(
            this IQueryable<T> query, 
            IDataEncryptionService encryptionService) 
            where T : class
        {
            return query.AsEnumerable().Select(entity =>
            {
                entity.DecryptEntity(encryptionService);
                return entity;
            }).AsQueryable();
        }

        public static async Task<List<T>> DecryptResultsAsync<T>(
            this IQueryable<T> query, 
            IDataEncryptionService encryptionService) 
            where T : class
        {
            var results = await query.ToListAsync();
            foreach (var entity in results)
            {
                entity.DecryptEntity(encryptionService);
            }
            return results;
        }

        public static async Task<T?> DecryptResultAsync<T>(
            this IQueryable<T> query, 
            IDataEncryptionService encryptionService) 
            where T : class
        {
            var result = await query.FirstOrDefaultAsync();
            if (result != null)
            {
                result.DecryptEntity(encryptionService);
            }
            return result;
        }
    }
}
