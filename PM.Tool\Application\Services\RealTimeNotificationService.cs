using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Infrastructure.Hubs;

namespace PM.Tool.Application.Services
{
    public class RealTimeNotificationService : IRealTimeNotificationService
    {
        private readonly IHubContext<CollaborationHub> _hubContext;
        private readonly ApplicationDbContext _context;
        private readonly INotificationService _notificationService;
        private readonly ILogger<RealTimeNotificationService> _logger;

        public RealTimeNotificationService(
            IHubContext<CollaborationHub> hubContext,
            ApplicationDbContext context,
            INotificationService notificationService,
            ILogger<RealTimeNotificationService> logger)
        {
            _hubContext = hubContext;
            _context = context;
            _notificationService = notificationService;
            _logger = logger;
        }

        public async Task NotifyTaskUpdatedAsync(int taskId, string updateType, object updateData, string updatedByUserId)
        {
            try
            {
                var task = await _context.Tasks
                    .Include(t => t.Project)
                    .Include(t => t.AssignedTo)
                    .FirstOrDefaultAsync(t => t.Id == taskId);

                if (task == null) return;

                var updateInfo = new
                {
                    TaskId = taskId,
                    TaskTitle = task.Title,
                    UpdateType = updateType,
                    UpdateData = updateData,
                    UpdatedBy = await GetUserNameAsync(updatedByUserId),
                    UpdatedAt = DateTime.UtcNow,
                    ProjectId = task.ProjectId,
                    ProjectName = task.Project?.Name
                };

                // Send real-time update to project members
                await _hubContext.Clients.Group($"project_{task.ProjectId}")
                    .SendAsync("TaskUpdated", updateInfo);

                // Create persistent notification for assigned user if different from updater
                if (task.AssignedToUserId != null && task.AssignedToUserId != updatedByUserId)
                {
                    var title = $"Task Updated: {task.Title}";
                    var message = $"Task '{task.Title}' was updated by {await GetUserNameAsync(updatedByUserId)}.";
                    
                    await _notificationService.CreateNotificationAsync(
                        task.AssignedToUserId, 
                        title, 
                        message, 
                        NotificationType.TaskUpdated, 
                        task.ProjectId, 
                        taskId);

                    // Send real-time notification to assigned user
                    await _hubContext.Clients.User(task.AssignedToUserId)
                        .SendAsync("NotificationReceived", new
                        {
                            Title = title,
                            Message = message,
                            Type = "task_updated",
                            TaskId = taskId,
                            ProjectId = task.ProjectId,
                            Timestamp = DateTime.UtcNow
                        });
                }

                _logger.LogInformation("Real-time task update sent for task {TaskId} by user {UserId}", taskId, updatedByUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending real-time task update for task {TaskId}", taskId);
            }
        }

        public async Task NotifyTaskAssignedAsync(int taskId, string assignedToUserId, string assignedByUserId)
        {
            try
            {
                var task = await _context.Tasks
                    .Include(t => t.Project)
                    .FirstOrDefaultAsync(t => t.Id == taskId);

                if (task == null) return;

                var assignedByName = await GetUserNameAsync(assignedByUserId);
                var title = $"New Task Assigned: {task.Title}";
                var message = $"You have been assigned to task '{task.Title}' by {assignedByName}.";

                // Create persistent notification
                await _notificationService.CreateNotificationAsync(
                    assignedToUserId, 
                    title, 
                    message, 
                    NotificationType.TaskAssigned, 
                    task.ProjectId, 
                    taskId);

                // Send real-time notification
                await _hubContext.Clients.User(assignedToUserId)
                    .SendAsync("NotificationReceived", new
                    {
                        Title = title,
                        Message = message,
                        Type = "task_assigned",
                        TaskId = taskId,
                        ProjectId = task.ProjectId,
                        Timestamp = DateTime.UtcNow,
                        Priority = "high"
                    });

                // Notify project members about assignment
                await _hubContext.Clients.Group($"project_{task.ProjectId}")
                    .SendAsync("TaskAssigned", new
                    {
                        TaskId = taskId,
                        TaskTitle = task.Title,
                        AssignedTo = await GetUserNameAsync(assignedToUserId),
                        AssignedBy = assignedByName,
                        ProjectId = task.ProjectId,
                        Timestamp = DateTime.UtcNow
                    });

                _logger.LogInformation("Task assignment notification sent for task {TaskId} to user {AssignedToUserId}", taskId, assignedToUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending task assignment notification for task {TaskId}", taskId);
            }
        }

        public async Task NotifyCommentAddedAsync(int taskId, string commentContent, string authorUserId)
        {
            try
            {
                var task = await _context.Tasks
                    .Include(t => t.Project)
                    .Include(t => t.AssignedTo)
                    .FirstOrDefaultAsync(t => t.Id == taskId);

                if (task == null) return;

                var authorName = await GetUserNameAsync(authorUserId);
                var commentInfo = new
                {
                    TaskId = taskId,
                    TaskTitle = task.Title,
                    Comment = commentContent,
                    AuthorName = authorName,
                    AuthorId = authorUserId,
                    CreatedAt = DateTime.UtcNow,
                    ProjectId = task.ProjectId
                };

                // Send real-time update to project members
                await _hubContext.Clients.Group($"project_{task.ProjectId}")
                    .SendAsync("CommentAdded", commentInfo);

                // Notify assigned user if different from comment author
                if (task.AssignedToUserId != null && task.AssignedToUserId != authorUserId)
                {
                    var title = $"New Comment on: {task.Title}";
                    var message = $"{authorName} commented on task '{task.Title}'.";
                    
                    await _notificationService.CreateNotificationAsync(
                        task.AssignedToUserId, 
                        title, 
                        message, 
                        NotificationType.CommentAdded, 
                        task.ProjectId, 
                        taskId);

                    await _hubContext.Clients.User(task.AssignedToUserId)
                        .SendAsync("NotificationReceived", new
                        {
                            Title = title,
                            Message = message,
                            Type = "comment_added",
                            TaskId = taskId,
                            ProjectId = task.ProjectId,
                            Timestamp = DateTime.UtcNow
                        });
                }

                _logger.LogInformation("Comment notification sent for task {TaskId} by user {AuthorUserId}", taskId, authorUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending comment notification for task {TaskId}", taskId);
            }
        }

        public async Task NotifyMentionAsync(string mentionedUserId, string mentionerUserId, string content, int? taskId = null, int? projectId = null)
        {
            try
            {
                var mentionerName = await GetUserNameAsync(mentionerUserId);
                var title = $"You were mentioned by {mentionerName}";
                var message = $"{mentionerName} mentioned you: \"{content}\"";

                // Create persistent notification
                await _notificationService.CreateNotificationAsync(
                    mentionedUserId, 
                    title, 
                    message, 
                    NotificationType.Mention, 
                    projectId, 
                    taskId);

                // Send real-time notification
                await _hubContext.Clients.User(mentionedUserId)
                    .SendAsync("NotificationReceived", new
                    {
                        Title = title,
                        Message = message,
                        Type = "mention",
                        TaskId = taskId,
                        ProjectId = projectId,
                        MentionedBy = mentionerName,
                        Content = content,
                        Timestamp = DateTime.UtcNow,
                        Priority = "high"
                    });

                _logger.LogInformation("Mention notification sent to user {MentionedUserId} by user {MentionerUserId}", mentionedUserId, mentionerUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending mention notification to user {MentionedUserId}", mentionedUserId);
            }
        }

        public async Task NotifyProjectUpdateAsync(int projectId, string updateType, object updateData, string updatedByUserId)
        {
            try
            {
                var project = await _context.Projects
                    .Include(p => p.Members)
                    .FirstOrDefaultAsync(p => p.Id == projectId);

                if (project == null) return;

                var updateInfo = new
                {
                    ProjectId = projectId,
                    ProjectName = project.Name,
                    UpdateType = updateType,
                    UpdateData = updateData,
                    UpdatedBy = await GetUserNameAsync(updatedByUserId),
                    UpdatedAt = DateTime.UtcNow
                };

                // Send real-time update to all project members
                await _hubContext.Clients.Group($"project_{projectId}")
                    .SendAsync("ProjectUpdated", updateInfo);

                _logger.LogInformation("Project update notification sent for project {ProjectId} by user {UserId}", projectId, updatedByUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending project update notification for project {ProjectId}", projectId);
            }
        }

        public async Task NotifyUserActivityAsync(string userId, string activity, int? projectId = null, int? taskId = null)
        {
            try
            {
                var userActivity = new
                {
                    UserId = userId,
                    UserName = await GetUserNameAsync(userId),
                    Activity = activity,
                    ProjectId = projectId,
                    TaskId = taskId,
                    Timestamp = DateTime.UtcNow
                };

                // Broadcast activity to relevant project groups
                if (projectId.HasValue)
                {
                    await _hubContext.Clients.Group($"project_{projectId}")
                        .SendAsync("UserActivityUpdated", userActivity);
                }

                _logger.LogDebug("User activity updated for user {UserId}: {Activity}", userId, activity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error broadcasting user activity for user {UserId}", userId);
            }
        }

        public async Task BroadcastToProjectAsync(int projectId, string method, object data)
        {
            try
            {
                await _hubContext.Clients.Group($"project_{projectId}")
                    .SendAsync(method, data);

                _logger.LogDebug("Broadcast sent to project {ProjectId}: {Method}", projectId, method);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error broadcasting to project {ProjectId}", projectId);
            }
        }

        public async Task SendDirectNotificationAsync(string userId, string title, string message, string type = "info", object? data = null)
        {
            try
            {
                await _hubContext.Clients.User(userId)
                    .SendAsync("NotificationReceived", new
                    {
                        Title = title,
                        Message = message,
                        Type = type,
                        Data = data,
                        Timestamp = DateTime.UtcNow
                    });

                _logger.LogInformation("Direct notification sent to user {UserId}: {Title}", userId, title);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending direct notification to user {UserId}", userId);
            }
        }

        private async Task<string> GetUserNameAsync(string userId)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                return user?.FullName ?? "Unknown User";
            }
            catch
            {
                return "Unknown User";
            }
        }
    }
}
