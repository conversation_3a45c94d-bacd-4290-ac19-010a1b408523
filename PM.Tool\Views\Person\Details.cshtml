@model PM.Tool.Core.Entities.Person
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "Person Details";
    ViewData["PageTitle"] = $"{Model.FullName} - Details";
    ViewData["PageDescription"] = $"Detailed information for {Model.FullName}";
}



<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 @GetPersonTypeIconBg(Model.Type) rounded-xl flex items-center justify-center">
                <i class="@GetPersonTypeIcon(Model.Type) text-white text-2xl"></i>
            </div>
            <div>
                <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                    @Model.FullName
                </h1>
                <div class="flex items-center space-x-3 mt-1">
                    <span class="text-sm text-neutral-500 dark:text-dark-400">@Model.PersonCode</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetPersonStatusBadgeClass(Model.Status)">
                        @Model.Status
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @GetPersonTypeBadgeClass(Model.Type)">
                        @Model.Type
                    </span>
                </div>
            </div>
        </div>
        <div class="flex space-x-3 mt-4 sm:mt-0">
            @{
                ViewData["Text"] = "Edit Person";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to People";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
    <!-- Left Column - Main Information -->
    <div class="xl:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Basic Information</h3>
                        <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Personal details and contact information</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Full Name</label>
                        <p class="text-neutral-900 dark:text-dark-100">@Model.FullName</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Email</label>
                        <p class="text-neutral-900 dark:text-dark-100">
                            <a href="mailto:@Model.Email" class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">@Model.Email</a>
                        </p>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Phone))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Phone</label>
                            <p class="text-neutral-900 dark:text-dark-100">
                                <a href="tel:@Model.Phone" class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">@Model.Phone</a>
                            </p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Title))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Job Title</label>
                            <p class="text-neutral-900 dark:text-dark-100">@Model.Title</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Department))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Department</label>
                            <p class="text-neutral-900 dark:text-dark-100">@Model.Department</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Organization))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Organization</label>
                            <p class="text-neutral-900 dark:text-dark-100">@Model.Organization</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Location))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Location</label>
                            <p class="text-neutral-900 dark:text-dark-100">@Model.Location</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.EmployeeId))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Employee ID</label>
                            <p class="text-neutral-900 dark:text-dark-100">@Model.EmployeeId</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- System Access & Dates -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cog text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">System & Employment Information</h3>
                        <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Access rights and employment details</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">System Access</label>
                        <div class="flex items-center space-x-2">
                            @if (Model.HasSystemAccess)
                            {
                                <i class="fas fa-check-circle text-success-500"></i>
                                <span class="text-success-600 dark:text-success-400">Has Access</span>
                            }
                            else
                            {
                                <i class="fas fa-times-circle text-neutral-400"></i>
                                <span class="text-neutral-600 dark:text-dark-400">No Access</span>
                            }
                        </div>
                    </div>

                    @if (Model.HireDate.HasValue)
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Hire Date</label>
                            <p class="text-neutral-900 dark:text-dark-100">@Model.HireDate.Value.ToString("MMMM dd, yyyy")</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.TimeZone))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Time Zone</label>
                            <p class="text-neutral-900 dark:text-dark-100">@Model.TimeZone</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.WorkingHours))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Working Hours</label>
                            <p class="text-neutral-900 dark:text-dark-100">@Model.WorkingHours</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Bio & Notes -->
        @if (!string.IsNullOrEmpty(Model.Bio) || !string.IsNullOrEmpty(Model.Notes))
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-edit text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Additional Information</h3>
                            <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Personal details and notes</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    @if (!string.IsNullOrEmpty(Model.Bio))
                    {
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Bio</label>
                            <p class="text-neutral-900 dark:text-dark-100 leading-relaxed">@Model.Bio</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Notes</label>
                            <p class="text-neutral-900 dark:text-dark-100 leading-relaxed">@Model.Notes</p>
                        </div>
                    }
                </div>
            </div>
        }
    </div>

    <!-- Right Column - Quick Actions & Summary -->
    <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bolt text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Quick Actions</h3>
                        <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Manage person settings</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">

                <div class="space-y-3">
                    @{
                        ViewData["Text"] = "Edit Person";
                        ViewData["Variant"] = "primary";
                        ViewData["Icon"] = "fas fa-edit";
                        ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });
                        ViewData["FullWidth"] = true;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @if (Model.HasSystemAccess)
                    {
                        <button type="button" class="w-full btn-warning" onclick="toggleSystemAccess(@Model.Id, false)">
                            <i class="fas fa-user-slash mr-2"></i>
                            Revoke Access
                        </button>
                    }
                    else
                    {
                        <button type="button" class="w-full btn-success" onclick="toggleSystemAccess(@Model.Id, true)">
                            <i class="fas fa-user-check mr-2"></i>
                            Grant Access
                        </button>
                    }

                    @if (Model.Status == PersonStatus.Active)
                    {
                            ViewData["Text"] = "Deactivate";
                            ViewData["Variant"] = "secondary";
                            ViewData["Icon"] = "fas fa-user-times";
                            ViewData["OnClick"] = $"togglePersonStatus({Model.Id}, 'Inactive')";
                            ViewData["FullWidth"] = true;
                        <partial name="Components/_Button" view-data="ViewData" />
                    }
                    else
                    {
                            ViewData["Text"] = "Activate";
                            ViewData["Variant"] = "success";
                            ViewData["Icon"] = "fas fa-user-check";
                            ViewData["OnClick"] = $"togglePersonStatus({Model.Id}, 'Active')";
                            ViewData["FullWidth"] = true;
                        <partial name="Components/_Button" view-data="ViewData" />
                    }
                </div>
            </div>
        </div>

        <!-- Summary Stats -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Summary</h3>
                        <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Key information overview</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-400">Person Code</span>
                        <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">@Model.PersonCode</span>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-400">Type</span>
                        <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">@Model.Type</span>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-400">Status</span>
                        <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">@Model.Status</span>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-400">Created</span>
                        <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy")</span>
                    </div>

                    @if (Model.UpdatedAt.HasValue)
                    {
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-neutral-600 dark:text-dark-400">Last Updated</span>
                            <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</span>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Communication Preferences -->
        @if (!string.IsNullOrEmpty(Model.CommunicationPreferences))
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-comments text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Communication</h3>
                            <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Preferred communication methods</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <p class="text-sm text-neutral-600 dark:text-dark-400 leading-relaxed">@Model.CommunicationPreferences</p>
                </div>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function toggleSystemAccess(personId, hasAccess) {
            if (confirm(`Are you sure you want to ${hasAccess ? 'grant' : 'revoke'} system access for this person?`)) {
                $.post('@Url.Action("ToggleSystemAccess")', { id: personId, hasAccess: hasAccess })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error updating system access: ' + (response.message || 'Unknown error'));
                        }
                    })
                    .fail(function() {
                        alert('Error updating system access');
                    });
            }
        }

        function togglePersonStatus(personId, status) {
            if (confirm(`Are you sure you want to ${status.toLowerCase()} this person?`)) {
                $.post('@Url.Action("ToggleStatus")', { id: personId, status: status })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error updating person status: ' + (response.message || 'Unknown error'));
                        }
                    })
                    .fail(function() {
                        alert('Error updating person status');
                    });
            }
        }
    </script>
}

@functions {
    private string GetPersonTypeIcon(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "fas fa-id-badge",
            PersonType.Contractor => "fas fa-user-tie",
            PersonType.External => "fas fa-user",
            PersonType.Customer => "fas fa-handshake",
            PersonType.Vendor => "fas fa-truck",
            PersonType.Partner => "fas fa-users",
            _ => "fas fa-user"
        };
    }

    private string GetPersonTypeIconBg(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "bg-blue-500",
            PersonType.Contractor => "bg-purple-500",
            PersonType.External => "bg-green-500",
            PersonType.Customer => "bg-pink-500",
            PersonType.Vendor => "bg-orange-500",
            PersonType.Partner => "bg-indigo-500",
            _ => "bg-neutral-500"
        };
    }

    private string GetPersonTypeBadgeClass(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            PersonType.Contractor => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
            PersonType.External => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            PersonType.Customer => "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",
            PersonType.Vendor => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            PersonType.Partner => "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }

    private string GetPersonStatusBadgeClass(PersonStatus status)
    {
        return status switch
        {
            PersonStatus.Active => "bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200",
            PersonStatus.Inactive => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200",
            PersonStatus.Terminated => "bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200",
            PersonStatus.OnLeave => "bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }
}
