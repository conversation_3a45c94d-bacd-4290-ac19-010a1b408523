using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Data;
using PM.Tool.Core.Interfaces;
using System.Collections.Concurrent;
using System.Security.Claims;

namespace PM.Tool.Infrastructure.Hubs
{
    [Authorize]
    public class CollaborationHub : Hub
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CollaborationHub> _logger;
        private readonly INotificationService _notificationService;
        
        // Static collections to track user presence and activities
        private static readonly ConcurrentDictionary<string, UserPresence> _userPresence = new();
        private static readonly ConcurrentDictionary<string, HashSet<string>> _projectGroups = new();
        private static readonly ConcurrentDictionary<string, UserActivity> _userActivities = new();

        public CollaborationHub(
            ApplicationDbContext context,
            ILogger<CollaborationHub> logger,
            INotificationService notificationService)
        {
            _context = context;
            _logger = logger;
            _notificationService = notificationService;
        }

        public override async Task OnConnectedAsync()
        {
            var userId = Context.UserIdentifier;
            var userName = Context.User?.Identity?.Name ?? "Unknown";
            
            if (userId != null)
            {
                // Update user presence
                _userPresence[userId] = new UserPresence
                {
                    UserId = userId,
                    UserName = userName,
                    ConnectionId = Context.ConnectionId,
                    IsOnline = true,
                    LastSeen = DateTime.UtcNow,
                    CurrentActivity = "Online"
                };

                // Get user's projects and join project groups
                var userProjects = await GetUserProjectsAsync(userId);
                foreach (var projectId in userProjects)
                {
                    var groupName = $"project_{projectId}";
                    await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
                    
                    // Track project membership
                    if (!_projectGroups.ContainsKey(groupName))
                        _projectGroups[groupName] = new HashSet<string>();
                    _projectGroups[groupName].Add(userId);
                }

                // Notify other users in the same projects about user coming online
                await NotifyUserPresenceChange(userId, true);
                
                _logger.LogInformation("User {UserId} ({UserName}) connected to CollaborationHub", userId, userName);
            }

            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var userId = Context.UserIdentifier;
            
            if (userId != null && _userPresence.TryGetValue(userId, out var presence))
            {
                // Update presence to offline
                presence.IsOnline = false;
                presence.LastSeen = DateTime.UtcNow;
                presence.CurrentActivity = "Offline";

                // Remove from project groups
                var userProjects = await GetUserProjectsAsync(userId);
                foreach (var projectId in userProjects)
                {
                    var groupName = $"project_{projectId}";
                    await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
                    
                    if (_projectGroups.TryGetValue(groupName, out var members))
                        members.Remove(userId);
                }

                // Notify other users about user going offline
                await NotifyUserPresenceChange(userId, false);
                
                _logger.LogInformation("User {UserId} disconnected from CollaborationHub", userId);
            }

            await base.OnDisconnectedAsync(exception);
        }

        // Real-time activity tracking
        public async Task UpdateUserActivity(string activity, int? projectId = null, int? taskId = null)
        {
            var userId = Context.UserIdentifier;
            if (userId == null) return;

            var userActivity = new UserActivity
            {
                UserId = userId,
                Activity = activity,
                ProjectId = projectId,
                TaskId = taskId,
                Timestamp = DateTime.UtcNow
            };

            _userActivities[userId] = userActivity;

            // Update presence
            if (_userPresence.TryGetValue(userId, out var presence))
            {
                presence.CurrentActivity = activity;
                presence.LastSeen = DateTime.UtcNow;
            }

            // Broadcast activity to relevant project groups
            if (projectId.HasValue)
            {
                await Clients.Group($"project_{projectId}").SendAsync("UserActivityUpdated", userActivity);
            }
        }

        // Real-time task updates
        public async Task TaskUpdated(int taskId, string updateType, object updateData)
        {
            try
            {
                var task = await _context.Tasks
                    .Include(t => t.Project)
                    .FirstOrDefaultAsync(t => t.Id == taskId);

                if (task != null)
                {
                    var updateInfo = new
                    {
                        TaskId = taskId,
                        UpdateType = updateType,
                        UpdateData = updateData,
                        UpdatedBy = Context.User?.Identity?.Name,
                        UpdatedAt = DateTime.UtcNow,
                        ProjectId = task.ProjectId
                    };

                    // Broadcast to project group
                    await Clients.Group($"project_{task.ProjectId}").SendAsync("TaskUpdated", updateInfo);
                    
                    _logger.LogInformation("Task {TaskId} updated: {UpdateType} by {User}", taskId, updateType, Context.User?.Identity?.Name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error broadcasting task update for task {TaskId}", taskId);
            }
        }

        // Real-time kanban board updates
        public async Task CardMoved(int taskId, string fromColumn, string toColumn, int newPosition)
        {
            try
            {
                var task = await _context.Tasks
                    .Include(t => t.Project)
                    .FirstOrDefaultAsync(t => t.Id == taskId);

                if (task != null)
                {
                    var moveInfo = new
                    {
                        TaskId = taskId,
                        FromColumn = fromColumn,
                        ToColumn = toColumn,
                        NewPosition = newPosition,
                        MovedBy = Context.User?.Identity?.Name,
                        MovedAt = DateTime.UtcNow,
                        ProjectId = task.ProjectId
                    };

                    // Broadcast to project group (excluding sender)
                    await Clients.GroupExcept($"project_{task.ProjectId}", Context.ConnectionId).SendAsync("CardMoved", moveInfo);
                    
                    _logger.LogInformation("Card {TaskId} moved from {FromColumn} to {ToColumn} by {User}", taskId, fromColumn, toColumn, Context.User?.Identity?.Name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error broadcasting card move for task {TaskId}", taskId);
            }
        }

        // Real-time comments
        public async Task CommentAdded(int taskId, string comment, string authorName)
        {
            try
            {
                var task = await _context.Tasks
                    .Include(t => t.Project)
                    .FirstOrDefaultAsync(t => t.Id == taskId);

                if (task != null)
                {
                    var commentInfo = new
                    {
                        TaskId = taskId,
                        Comment = comment,
                        AuthorName = authorName,
                        CreatedAt = DateTime.UtcNow,
                        ProjectId = task.ProjectId
                    };

                    // Broadcast to project group
                    await Clients.Group($"project_{task.ProjectId}").SendAsync("CommentAdded", commentInfo);
                    
                    _logger.LogInformation("Comment added to task {TaskId} by {Author}", taskId, authorName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error broadcasting comment for task {TaskId}", taskId);
            }
        }

        // Real-time notifications
        public async Task SendNotification(string targetUserId, string title, string message, string type = "info")
        {
            try
            {
                var notification = new
                {
                    Title = title,
                    Message = message,
                    Type = type,
                    Timestamp = DateTime.UtcNow,
                    From = Context.User?.Identity?.Name
                };

                // Send to specific user
                await Clients.User(targetUserId).SendAsync("NotificationReceived", notification);
                
                _logger.LogInformation("Notification sent to user {TargetUserId} from {Sender}", targetUserId, Context.User?.Identity?.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to user {TargetUserId}", targetUserId);
            }
        }

        // Get online users for a project
        public async Task<List<UserPresence>> GetOnlineUsers(int projectId)
        {
            var groupName = $"project_{projectId}";
            var onlineUsers = new List<UserPresence>();

            if (_projectGroups.TryGetValue(groupName, out var members))
            {
                foreach (var userId in members)
                {
                    if (_userPresence.TryGetValue(userId, out var presence) && presence.IsOnline)
                    {
                        onlineUsers.Add(presence);
                    }
                }
            }

            return onlineUsers;
        }

        // Private helper methods
        private async Task<List<int>> GetUserProjectsAsync(string userId)
        {
            try
            {
                return await _context.ProjectMembers
                    .Where(pm => pm.UserId == userId && pm.IsActive)
                    .Select(pm => pm.ProjectId)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting projects for user {UserId}", userId);
                return new List<int>();
            }
        }

        private async Task NotifyUserPresenceChange(string userId, bool isOnline)
        {
            try
            {
                if (_userPresence.TryGetValue(userId, out var presence))
                {
                    var userProjects = await GetUserProjectsAsync(userId);
                    foreach (var projectId in userProjects)
                    {
                        await Clients.GroupExcept($"project_{projectId}", Context.ConnectionId)
                            .SendAsync("UserPresenceChanged", new
                            {
                                UserId = userId,
                                UserName = presence.UserName,
                                IsOnline = isOnline,
                                LastSeen = presence.LastSeen,
                                CurrentActivity = presence.CurrentActivity
                            });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error notifying presence change for user {UserId}", userId);
            }
        }
    }

    // Supporting classes
    public class UserPresence
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string ConnectionId { get; set; } = string.Empty;
        public bool IsOnline { get; set; }
        public DateTime LastSeen { get; set; }
        public string CurrentActivity { get; set; } = string.Empty;
    }

    public class UserActivity
    {
        public string UserId { get; set; } = string.Empty;
        public string Activity { get; set; } = string.Empty;
        public int? ProjectId { get; set; }
        public int? TaskId { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
