@*
    Standardized Pagination Component - Usage Examples:

    1. Basic pagination:
    @{
        ViewData["CurrentPage"] = 3;
        ViewData["TotalPages"] = 10;
        ViewData["BaseUrl"] = "/Projects";
    }
    <partial name="Components/_Pagination" view-data="ViewData" />

    2. Pagination with page size selector:
    @{
        ViewData["CurrentPage"] = 1;
        ViewData["TotalPages"] = 15;
        ViewData["TotalItems"] = 150;
        ViewData["PageSize"] = 10;
        ViewData["BaseUrl"] = "/Tasks";
        ViewData["ShowPageSize"] = true;
        ViewData["PageSizeOptions"] = new[] { 10, 25, 50, 100 };
    }
    <partial name="Components/_Pagination" view-data="ViewData" />

    3. Compact pagination:
    @{
        ViewData["CurrentPage"] = 5;
        ViewData["TotalPages"] = 20;
        ViewData["BaseUrl"] = "/Resources";
        ViewData["Compact"] = true;
        ViewData["ShowInfo"] = false;
    }
    <partial name="Components/_Pagination" view-data="ViewData" />
*@

@model dynamic

@{
    var currentPage = ViewData["CurrentPage"] as int? ?? 1;
    var totalPages = ViewData["TotalPages"] as int? ?? 1;
    var totalItems = ViewData["TotalItems"] as int? ?? 0;
    var pageSize = ViewData["PageSize"] as int? ?? 10;
    var baseUrl = ViewData["BaseUrl"]?.ToString() ?? "";
    var queryString = ViewData["QueryString"]?.ToString() ?? "";
    var compact = ViewData["Compact"] as bool? ?? false;
    var showInfo = ViewData["ShowInfo"] as bool? ?? true;
    var showPageSize = ViewData["ShowPageSize"] as bool? ?? false;
    var pageSizeOptions = ViewData["PageSizeOptions"] as int[] ?? new[] { 10, 25, 50, 100 };
    var maxVisiblePages = ViewData["MaxVisiblePages"] as int? ?? 5;

    // Calculate pagination info
    var startItem = totalItems > 0 ? (currentPage - 1) * pageSize + 1 : 0;
    var endItem = Math.Min(currentPage * pageSize, totalItems);
    var hasPrevious = currentPage > 1;
    var hasNext = currentPage < totalPages;

    // Calculate visible page numbers
    var startPage = Math.Max(1, currentPage - maxVisiblePages / 2);
    var endPage = Math.Min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages)
    {
        startPage = Math.Max(1, endPage - maxVisiblePages + 1);
    }

    // Build query string helper
    string BuildUrl(int page, int? size = null)
    {
        var url = baseUrl;
        var separator = url.Contains("?") ? "&" : "?";
        
        if (!string.IsNullOrEmpty(queryString))
        {
            // Remove existing page and pageSize parameters
            var cleanQuery = System.Web.HttpUtility.ParseQueryString(queryString);
            cleanQuery.Remove("page");
            if (size.HasValue) cleanQuery.Remove("pageSize");
            
            if (cleanQuery.Count > 0)
            {
                url += separator + cleanQuery.ToString();
                separator = "&";
            }
        }
        
        url += $"{separator}page={page}";
        if (size.HasValue)
        {
            url += $"&pageSize={size}";
        }
        
        return url;
    }
}

<!-- Standardized Pagination -->
<div class="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 sm:space-x-4">
    
    @if (showInfo && !compact)
    {
        <!-- Pagination Info -->
        <div class="text-sm text-neutral-600 dark:text-dark-400">
            @if (totalItems > 0)
            {
                <span>Showing <span class="font-medium text-neutral-900 dark:text-dark-100">@startItem</span> to <span class="font-medium text-neutral-900 dark:text-dark-100">@endItem</span> of <span class="font-medium text-neutral-900 dark:text-dark-100">@totalItems</span> results</span>
            }
            else
            {
                <span>No results found</span>
            }
        </div>
    }

    @if (totalPages > 1)
    {
        <!-- Pagination Controls -->
        <nav class="flex items-center space-x-1" aria-label="Pagination">
            
            <!-- Previous Button -->
            @if (hasPrevious)
            {
                <a href="@BuildUrl(currentPage - 1)" 
                   class="inline-flex items-center px-3 py-2 text-sm font-medium text-neutral-700 dark:text-dark-300 bg-white dark:bg-dark-800 border border-neutral-300 dark:border-dark-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors duration-200"
                   aria-label="Previous page">
                    @if (compact)
                    {
                        <i class="fas fa-chevron-left text-xs"></i>
                    }
                    else
                    {
                        <i class="fas fa-chevron-left text-xs mr-2"></i>
                        <span>Previous</span>
                    }
                </a>
            }
            else
            {
                <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-neutral-400 dark:text-dark-500 bg-neutral-100 dark:bg-dark-700 border border-neutral-200 dark:border-dark-600 rounded-lg cursor-not-allowed">
                    @if (compact)
                    {
                        <i class="fas fa-chevron-left text-xs"></i>
                    }
                    else
                    {
                        <i class="fas fa-chevron-left text-xs mr-2"></i>
                        <span>Previous</span>
                    }
                </span>
            }

            @if (!compact)
            {
                <!-- First Page -->
                @if (startPage > 1)
                {
                    <a href="@BuildUrl(1)" 
                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-neutral-700 dark:text-dark-300 bg-white dark:bg-dark-800 border border-neutral-300 dark:border-dark-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors duration-200">
                        1
                    </a>
                    
                    @if (startPage > 2)
                    {
                        <span class="inline-flex items-center px-2 py-2 text-sm font-medium text-neutral-500 dark:text-dark-400">
                            ...
                        </span>
                    }
                }

                <!-- Page Numbers -->
                @for (int pageNum = startPage; pageNum <= endPage; pageNum++)
                {
                    @if (pageNum == currentPage)
                    {
                        <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900 border border-primary-300 dark:border-primary-700 rounded-lg">
                            @pageNum
                        </span>
                    }
                    else
                    {
                        <a href="@BuildUrl(pageNum)"
                           class="inline-flex items-center px-3 py-2 text-sm font-medium text-neutral-700 dark:text-dark-300 bg-white dark:bg-dark-800 border border-neutral-300 dark:border-dark-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors duration-200">
                            @pageNum
                        </a>
                    }
                }

                <!-- Last Page -->
                @if (endPage < totalPages)
                {
                    @if (endPage < totalPages - 1)
                    {
                        <span class="inline-flex items-center px-2 py-2 text-sm font-medium text-neutral-500 dark:text-dark-400">
                            ...
                        </span>
                    }
                    
                    <a href="@BuildUrl(totalPages)" 
                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-neutral-700 dark:text-dark-300 bg-white dark:bg-dark-800 border border-neutral-300 dark:border-dark-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors duration-200">
                        @totalPages
                    </a>
                }
            }
            else
            {
                <!-- Compact Page Info -->
                <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-neutral-700 dark:text-dark-300 bg-white dark:bg-dark-800 border border-neutral-300 dark:border-dark-600 rounded-lg">
                    @currentPage of @totalPages
                </span>
            }

            <!-- Next Button -->
            @if (hasNext)
            {
                <a href="@BuildUrl(currentPage + 1)" 
                   class="inline-flex items-center px-3 py-2 text-sm font-medium text-neutral-700 dark:text-dark-300 bg-white dark:bg-dark-800 border border-neutral-300 dark:border-dark-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors duration-200"
                   aria-label="Next page">
                    @if (compact)
                    {
                        <i class="fas fa-chevron-right text-xs"></i>
                    }
                    else
                    {
                        <span>Next</span>
                        <i class="fas fa-chevron-right text-xs ml-2"></i>
                    }
                </a>
            }
            else
            {
                <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-neutral-400 dark:text-dark-500 bg-neutral-100 dark:bg-dark-700 border border-neutral-200 dark:border-dark-600 rounded-lg cursor-not-allowed">
                    @if (compact)
                    {
                        <i class="fas fa-chevron-right text-xs"></i>
                    }
                    else
                    {
                        <span>Next</span>
                        <i class="fas fa-chevron-right text-xs ml-2"></i>
                    }
                </span>
            }
        </nav>
    }

    @if (showPageSize && !compact)
    {
        <!-- Page Size Selector -->
        <div class="flex items-center space-x-2">
            <label for="pageSize" class="text-sm font-medium text-neutral-700 dark:text-dark-300">
                Show:
            </label>
            <select id="pageSize" 
                    class="form-select-custom text-sm"
                    onchange="changePaginationPageSize(this.value)">
                @foreach (var option in pageSizeOptions)
                {
                    @if (option == pageSize)
                    {
                        <option value="@option" selected>
                            @option
                        </option>
                    }
                    else
                    {
                        <option value="@option">
                            @option
                        </option>
                    }
                }
            </select>
            <span class="text-sm text-neutral-600 dark:text-dark-400">per page</span>
        </div>
    }
</div>

<script>
    // Pagination System
    window.PaginationSystem = window.PaginationSystem || {
        // Change page size and reload
        changePageSize: function(newSize, baseUrl = '', queryString = '') {
            const url = new URL(window.location);
            url.searchParams.set('pageSize', newSize);
            url.searchParams.set('page', '1'); // Reset to first page
            window.location.href = url.toString();
        },
        
        // Navigate to specific page
        goToPage: function(page, baseUrl = '', queryString = '') {
            const url = new URL(window.location);
            url.searchParams.set('page', page);
            window.location.href = url.toString();
        },
        
        // Get current pagination state
        getCurrentState: function() {
            const url = new URL(window.location);
            return {
                page: parseInt(url.searchParams.get('page')) || 1,
                pageSize: parseInt(url.searchParams.get('pageSize')) || 10
            };
        }
    };

    // Global function for page size change
    function changePaginationPageSize(newSize) {
        window.PaginationSystem.changePageSize(newSize);
    }

    // Keyboard navigation for pagination
    document.addEventListener('keydown', function(e) {
        // Only handle if no input is focused
        if (document.activeElement.tagName === 'INPUT' || 
            document.activeElement.tagName === 'TEXTAREA' || 
            document.activeElement.tagName === 'SELECT') {
            return;
        }

        const state = window.PaginationSystem.getCurrentState();
        
        // Left arrow for previous page
        if (e.key === 'ArrowLeft' && e.ctrlKey) {
            e.preventDefault();
            if (state.page > 1) {
                window.PaginationSystem.goToPage(state.page - 1);
            }
        }
        
        // Right arrow for next page
        if (e.key === 'ArrowRight' && e.ctrlKey) {
            e.preventDefault();
            // Note: We'd need total pages to implement this properly
            // This is a placeholder for the functionality
        }
    });
</script>
