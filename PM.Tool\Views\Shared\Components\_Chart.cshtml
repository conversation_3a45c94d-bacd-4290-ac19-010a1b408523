@*
    Professional Chart Component - Usage Examples:

    1. Basic chart:
    @{
        ViewData["Title"] = "Project Progress";
        ViewData["Icon"] = "fas fa-chart-bar";
        ViewData["ChartType"] = "bar"; // bar, line, pie, doughnut
        ViewData["ChartId"] = "progressChart";
        ViewData["Height"] = "400px";
    }
    <partial name="Components/_Chart" view-data="ViewData" />

    2. Chart with data:
    @{
        ViewData["Title"] = "Team Performance";
        ViewData["ChartType"] = "line";
        ViewData["Labels"] = new[] { "Jan", "Feb", "Mar", "Apr", "May" };
        ViewData["Data"] = new[] { 10, 20, 30, 40, 50 };
        ViewData["ShowLegend"] = true;
        ViewData["ShowTooltips"] = true;
    }
    <partial name="Components/_Chart" view-data="ViewData" />
*@

@{
    var title = ViewData["Title"]?.ToString() ?? "Chart";
    var subtitle = ViewData["Subtitle"]?.ToString();
    var icon = ViewData["Icon"]?.ToString() ?? "fas fa-chart-bar";
    var chartType = ViewData["ChartType"]?.ToString() ?? "bar";
    var chartId = ViewData["ChartId"]?.ToString() ?? $"chart_{Guid.NewGuid().ToString("N")[..8]}";
    var height = ViewData["Height"]?.ToString() ?? "300px";
    var showLegend = ViewData["ShowLegend"] as bool? ?? true;
    var showTooltips = ViewData["ShowTooltips"] as bool? ?? true;
    var showExport = ViewData["ShowExport"] as bool? ?? false;
    var responsive = ViewData["Responsive"] as bool? ?? true;
    var maintainAspectRatio = ViewData["MaintainAspectRatio"] as bool? ?? false;

    // Data properties
    var labels = ViewData["Labels"] as string[] ?? new string[0];
    var data = ViewData["Data"] as decimal[] ?? new decimal[0];
    var datasets = ViewData["Datasets"] as object[] ?? new object[0];
    var colors = ViewData["Colors"] as string[] ?? new[] { "#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4" };

    // Configuration
    var showGrid = ViewData["ShowGrid"] as bool? ?? true;
    var showAxes = ViewData["ShowAxes"] as bool? ?? true;
    var animation = ViewData["Animation"] as bool? ?? true;
    var tension = ViewData["Tension"] as decimal? ?? 0.4m;

    var headerActions = ViewData["HeaderActions"]?.ToString();
}

<div class="card-custom">
    <!-- Chart Header -->
    <div class="card-header-custom">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="@icon text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@title</h3>
                    @if (!string.IsNullOrEmpty(subtitle))
                    {
                        <p class="text-sm text-neutral-500 dark:text-dark-400">@subtitle</p>
                    }
                </div>
            </div>

            <div class="flex items-center space-x-2">
                @if (!string.IsNullOrEmpty(headerActions))
                {
                    @Html.Raw(headerActions)
                }

                @if (showExport)
                {
                    <div class="relative">
                        @{
                            ViewData["Text"] = "";
                            ViewData["Variant"] = "outline";
                            ViewData["Icon"] = "fas fa-ellipsis-v";
                            ViewData["Size"] = "sm";
                            ViewData["OnClick"] = $"toggleChartMenu('{chartId}')";
                            ViewData["Id"] = $"{chartId}MenuToggle";
                        }
                        <partial name="Components/_Button" view-data="ViewData" />

                        <div id="@(chartId)Menu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-surface-dark rounded-lg shadow-lg border border-neutral-200 dark:border-dark-200 py-2 hidden z-50">
                            <button onclick="exportChart('@chartId', 'png')" class="w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-50 dark:hover:bg-dark-700">
                                <i class="fas fa-image mr-2"></i>Export as PNG
                            </button>
                            <button onclick="exportChart('@chartId', 'jpg')" class="w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-50 dark:hover:bg-dark-700">
                                <i class="fas fa-file-image mr-2"></i>Export as JPG
                            </button>
                            <button onclick="exportChart('@chartId', 'pdf')" class="w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-50 dark:hover:bg-dark-700">
                                <i class="fas fa-file-pdf mr-2"></i>Export as PDF
                            </button>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Chart Content -->
    <div class="card-body-custom">
        <div class="relative" style="height: @height;">
            <canvas id="@chartId" class="w-full h-full"></canvas>
        </div>

        <!-- Chart Loading State -->
        <div id="@(chartId)Loading" class="absolute inset-0 flex items-center justify-center bg-white dark:bg-surface-dark bg-opacity-75 hidden">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p class="text-neutral-600 dark:text-dark-300">Loading chart...</p>
            </div>
        </div>

        <!-- Chart Error State -->
        <div id="@(chartId)Error" class="text-center py-8 hidden">
            <div class="w-16 h-16 bg-danger-100 dark:bg-danger-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-exclamation-triangle text-2xl text-danger-600 dark:text-danger-400"></i>
            </div>
            <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">Chart Error</h4>
            <p class="text-neutral-500 dark:text-dark-400">Unable to load chart data. Please try again.</p>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        $(document).ready(function() {
            initializeChart_@(chartId.Replace("-", "_"))();
        });

        function initializeChart_@(chartId.Replace("-", "_"))() {
            var ctx = document.getElementById('@chartId');
            if (!ctx) return;

            // Show loading state
            showChartLoading('@chartId');

            try {
                // Chart configuration
                var config = {
                    type: '@chartType',
                    data: {
                        labels: @Html.Raw(Json.Serialize(labels)),
                        datasets: @Html.Raw(datasets.Any() ? Json.Serialize(datasets) : Json.Serialize(new[] {
                            new {
                                label = title,
                                data = data,
                                backgroundColor = colors.Take(data.Length),
                                borderColor = colors.Take(data.Length),
                                borderWidth = 2,
                                tension = tension,
                                fill = chartType == "area"
                            }
                        }))
                    },
                    options: {
                        responsive: @responsive.ToString().ToLower(),
                        maintainAspectRatio: @maintainAspectRatio.ToString().ToLower(),
                        animation: {
                            duration: @(animation ? "1000" : "0")
                        },
                        plugins: {
                            legend: {
                                display: @showLegend.ToString().ToLower(),
                                position: 'top',
                                labels: {
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--text-color') || '#374151',
                                    usePointStyle: true,
                                    padding: 20
                                }
                            },
                            tooltip: {
                                enabled: @showTooltips.ToString().ToLower(),
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: '#3b82f6',
                                borderWidth: 1,
                                cornerRadius: 8,
                                displayColors: true
                            }
                        },
                        @if (showAxes && (chartType == "bar" || chartType == "line"))
                        {
                            <text>scales: {
                                x: {
                                    display: true,
                                    grid: {
                                        display: @showGrid.ToString().ToLower(),
                                        color: 'rgba(0, 0, 0, 0.1)'
                                    },
                                    ticks: {
                                        color: getComputedStyle(document.documentElement).getPropertyValue('--text-color') || '#6b7280'
                                    }
                                },
                                y: {
                                    display: true,
                                    grid: {
                                        display: @showGrid.ToString().ToLower(),
                                        color: 'rgba(0, 0, 0, 0.1)'
                                    },
                                    ticks: {
                                        color: getComputedStyle(document.documentElement).getPropertyValue('--text-color') || '#6b7280'
                                    }
                                }
                            }</text>
                        }
                        else
                        {
                            <text>scales: {}</text>
                        }
                    }
                };

                // Create chart
                var chart = new Chart(ctx, config);

                // Store chart instance for theme updates
                window.chartInstances = window.chartInstances || {};
                window.chartInstances['@chartId'] = chart;

                // Hide loading state
                hideChartLoading('@chartId');

                // Listen for theme changes
                window.addEventListener('themeChanged', function(e) {
                    updateChartTheme('@chartId', e.detail.theme);
                });

            } catch (error) {
                console.error('Chart initialization error:', error);
                showChartError('@chartId');
            }
        }

        function showChartLoading(chartId) {
            document.getElementById(chartId + 'Loading').classList.remove('hidden');
        }

        function hideChartLoading(chartId) {
            document.getElementById(chartId + 'Loading').classList.add('hidden');
        }

        function showChartError(chartId) {
            hideChartLoading(chartId);
            document.getElementById(chartId + 'Error').classList.remove('hidden');
        }

        function toggleChartMenu(chartId) {
            var menu = document.getElementById(chartId + 'Menu');
            menu.classList.toggle('hidden');
        }

        function exportChart(chartId, format) {
            var chart = window.chartInstances[chartId];
            if (!chart) return;

            var url = chart.toBase64Image();
            var link = document.createElement('a');
            link.download = chartId + '_chart.' + format;
            link.href = url;
            link.click();

            // Hide menu
            document.getElementById(chartId + 'Menu').classList.add('hidden');
        }

        function updateChartTheme(chartId, theme) {
            var chart = window.chartInstances[chartId];
            if (!chart) return;

            var textColor = theme === 'dark' ? '#ffffff' : '#374151';
            var gridColor = theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

            // Update chart colors
            if (chart.options.plugins.legend) {
                chart.options.plugins.legend.labels.color = textColor;
            }

            if (chart.options.scales) {
                if (chart.options.scales.x) {
                    chart.options.scales.x.ticks.color = textColor;
                    chart.options.scales.x.grid.color = gridColor;
                }
                if (chart.options.scales.y) {
                    chart.options.scales.y.ticks.color = textColor;
                    chart.options.scales.y.grid.color = gridColor;
                }
            }

            chart.update();
        }

        // Close chart menus when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('[id$="MenuToggle"]')) {
                document.querySelectorAll('[id$="Menu"]').forEach(function(menu) {
                    menu.classList.add('hidden');
                });
            }
        });
    </script>
}
