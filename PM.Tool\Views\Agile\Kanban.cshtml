@model IEnumerable<PM.Tool.Core.Entities.Agile.UserStory>
@{
    ViewData["Title"] = "Kanban Board";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
    var projectId = ViewBag.ProjectId as int? ?? 0;
    ViewData["Breadcrumbs"] = new List<object>
    {
        new { Text = "Home", Href = "/", Icon = "fas fa-home" },
        new { Text = "Agile", Href = "/Agile", Icon = "fas fa-tasks" },
        new { Text = "Kanban Board", Href = (string?)null, Icon = "fas fa-columns" }
    };
}

<div class="min-h-screen bg-neutral-50 dark:bg-dark-950 transition-colors duration-200">
    <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Enhanced Header -->
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
            <div class="mb-4 lg:mb-0">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary-600 to-primary-700 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-columns text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-50">Kanban Board</h1>
                        <p class="text-neutral-600 dark:text-dark-300 mt-1">@project?.Name - Agile Project Management</p>
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap items-center gap-3">
                <a href="@Url.Action("SprintPlanning", "Agile", new { projectId })" class="btn-outline-custom">
                    <i class="fas fa-tasks mr-2"></i>
                    Sprint Planning
                </a>
                <a href="@Url.Action("Burndown", "Agile", new { projectId })" class="btn-outline-custom">
                    <i class="fas fa-chart-line mr-2"></i>
                    Burndown Charts
                </a>
                <a href="@Url.Action("CreateUserStory", new { projectId })" class="btn-primary-custom">
                    <i class="fas fa-plus mr-2"></i>
                    Add Story
                </a>
                <button onclick="openBoardSettings()" class="btn-secondary-custom">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </div>

        <!-- Enhanced Control Panel -->
        <div class="card-custom mb-8">
            <div class="card-body-custom">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <!-- Sprint Filter Section -->
                    <div class="flex flex-col sm:flex-row sm:items-center gap-4">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                                <i class="fas fa-filter text-primary-600 dark:text-primary-400"></i>
                            </div>
                            <span class="text-sm font-medium text-neutral-900 dark:text-dark-50">Sprint Filter:</span>
                        </div>
                        <select id="sprintFilter" class="form-select-custom min-w-48" onchange="filterBySprint()">
                            <option value="">All Stories</option>
                            @{
                                var sprints = ViewBag.Sprints as IEnumerable<PM.Tool.Core.Entities.Agile.Sprint>;
                                var activeSprint = ViewBag.ActiveSprint as PM.Tool.Core.Entities.Agile.Sprint;
                            }
                            @if (sprints != null)
                            {
                                @foreach (var sprint in sprints.OrderByDescending(s => s.StartDate))
                                {
                                    <option value="@sprint.Id" selected="@(sprint.Id == activeSprint?.Id)">
                                        @sprint.Name (@sprint.Status)
                                    </option>
                                }
                            }
                        </select>
                    </div>

                    <!-- Board Controls -->
                    <div class="flex items-center gap-4">
                        <div class="flex items-center gap-2 text-sm text-neutral-600 dark:text-dark-300">
                            <i class="fas fa-list-ul"></i>
                            <span id="totalStories">0</span> stories
                        </div>
                        <div class="flex items-center gap-2">
                            <button onclick="refreshBoard()" class="btn-outline-custom" title="Refresh Board">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button onclick="toggleCompactView()" class="btn-outline-custom" title="Toggle Compact View">
                                <i class="fas fa-compress-alt"></i>
                            </button>
                            <button onclick="openBoardSettings()" class="btn-secondary-custom" title="Board Settings">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sprint Progress Dashboard -->
        <div class="card-custom mb-8">
            <div class="card-header-custom">
                <h2 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                    <i class="fas fa-clock text-primary-600 dark:text-primary-400 mr-3"></i>
                    Sprint Dashboard
                </h2>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Sprint Selection -->
                    <div>
                        <label for="sprintSelect" class="form-label-custom">Current Sprint:</label>
                        <select id="sprintSelect" class="form-select-custom">
                            <option value="">Select Sprint...</option>
                            <!-- Options will be populated via AJAX -->
                        </select>
                    </div>

                    <!-- Sprint Progress -->
                    <div class="flex items-center justify-center">
                        <div class="text-center">
                            <p class="text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Sprint Progress</p>
                            <div class="relative w-20 h-20">
                                <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-neutral-200 dark:text-dark-700" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path id="sprintProgressCircle" class="text-primary-600 dark:text-primary-400" stroke="currentColor" stroke-width="3" stroke-linecap="round" fill="none" stroke-dasharray="0, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-lg font-bold text-primary-600 dark:text-primary-400" id="sprintProgress">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sprint Stats -->
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center p-3 bg-neutral-50 dark:bg-dark-800 rounded-lg">
                            <div class="text-2xl font-bold text-neutral-900 dark:text-dark-50" id="completedStories">0</div>
                            <div class="text-sm text-neutral-600 dark:text-dark-300">Completed</div>
                        </div>
                        <div class="text-center p-3 bg-neutral-50 dark:bg-dark-800 rounded-lg">
                            <div class="text-2xl font-bold text-neutral-900 dark:text-dark-50" id="remainingStories">0</div>
                            <div class="text-sm text-neutral-600 dark:text-dark-300">Remaining</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Kanban Board -->
        <div class="kanban-board overflow-x-auto">
            <div class="flex gap-6 min-w-max pb-6">
                <!-- Backlog Column -->
                <div class="kanban-column flex-shrink-0 w-80" data-status="Backlog">
                    <div class="card-custom">
                        <div class="card-header-custom">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                                    <div class="w-3 h-3 rounded-full mr-3 bg-neutral-500"></div>
                                    Backlog
                                </h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200" id="backlogCount">0</span>
                            </div>
                            <p class="text-xs text-neutral-600 dark:text-dark-400 mt-1">Ready for development</p>
                        </div>
                        <div class="kanban-cards bg-neutral-50 dark:bg-dark-900/50 min-h-96 p-4 space-y-3 rounded-b-xl"
                             id="backlogColumn" data-status="Backlog">
                            <!-- User stories will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Ready Column -->
                <div class="kanban-column flex-shrink-0 w-80" data-status="Ready">
                    <div class="card-custom">
                        <div class="card-header-custom">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                                    <div class="w-3 h-3 rounded-full mr-3 bg-blue-500"></div>
                                    Ready
                                </h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300" id="readyCount">0</span>
                            </div>
                            <p class="text-xs text-neutral-600 dark:text-dark-400 mt-1">Ready to start</p>
                        </div>
                        <div class="kanban-cards bg-neutral-50 dark:bg-dark-900/50 min-h-96 p-4 space-y-3 rounded-b-xl"
                             id="readyColumn" data-status="Ready">
                            <!-- User stories will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- In Progress Column -->
                <div class="kanban-column flex-shrink-0 w-80" data-status="InProgress">
                    <div class="card-custom border-l-4 border-l-amber-500">
                        <div class="card-header-custom">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                                    <div class="w-3 h-3 rounded-full mr-3 bg-amber-500"></div>
                                    In Progress
                                </h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300" id="inprogressCount">0</span>
                            </div>
                            <p class="text-xs text-neutral-600 dark:text-dark-400 mt-1">Being worked on</p>
                            <div class="mt-3">
                                <div class="flex items-center justify-between text-xs mb-2">
                                    <span class="text-neutral-600 dark:text-dark-400 font-medium">WIP Limit</span>
                                    <span class="text-neutral-700 dark:text-dark-300 font-semibold" id="wipStatus">0 / 3</span>
                                </div>
                                <div class="w-full bg-neutral-200 dark:bg-dark-700 rounded-full h-2">
                                    <div id="wipProgressBar" class="bg-amber-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="kanban-cards bg-neutral-50 dark:bg-dark-900/50 min-h-96 p-4 space-y-3 rounded-b-xl"
                             id="inprogressColumn" data-status="InProgress">
                            <!-- User stories will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Review Column -->
                <div class="kanban-column flex-shrink-0 w-80" data-status="Review">
                    <div class="card-custom">
                        <div class="card-header-custom">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                                    <div class="w-3 h-3 rounded-full mr-3 bg-purple-500"></div>
                                    Review
                                </h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300" id="reviewCount">0</span>
                            </div>
                            <p class="text-xs text-neutral-600 dark:text-dark-400 mt-1">Under review</p>
                        </div>
                        <div class="kanban-cards bg-neutral-50 dark:bg-dark-900/50 min-h-96 p-4 space-y-3 rounded-b-xl"
                             id="reviewColumn" data-status="Review">
                            <!-- User stories will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Done Column -->
                <div class="kanban-column flex-shrink-0 w-80" data-status="Done">
                    <div class="card-custom border-l-4 border-l-green-500">
                        <div class="card-header-custom">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                                    <div class="w-3 h-3 rounded-full mr-3 bg-green-500"></div>
                                    Done
                                </h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300" id="doneCount">0</span>
                            </div>
                            <p class="text-xs text-neutral-600 dark:text-dark-400 mt-1">Completed</p>
                        </div>
                        <div class="kanban-cards bg-neutral-50 dark:bg-dark-900/50 min-h-96 p-4 space-y-3 rounded-b-xl"
                             id="doneColumn" data-status="Done">
                            <!-- User stories will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced User Story Modal -->
<div id="userStoryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-dark-900 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        <div class="flex items-center justify-between p-6 border-b border-neutral-200 dark:border-dark-700">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-50">User Story Details</h3>
            <button onclick="closeUserStoryModal()" class="text-neutral-400 hover:text-neutral-600 dark:hover:text-dark-300 transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="userStoryModalContent" class="p-6">
            <!-- Content will be loaded dynamically -->
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    @Html.AntiForgeryToken()
    <script>
        $(document).ready(function() {
            loadSprints();
            initializeKanban();
            loadAllUserStories(); // Load initial stories

            $('#sprintSelect').on('change', function() {
                const sprintId = $(this).val();
                if (sprintId) {
                    loadUserStories(sprintId);
                } else {
                    loadAllUserStories();
                }
            });
        });

        function loadSprints() {
            $.get('@Url.Action("GetActiveSprints", "Agile")', { projectId: @projectId })
                .done(function(data) {
                    const select = $('#sprintSelect');
                    select.empty().append('<option value="">All Stories</option>');

                    if (data && data.length) {
                        data.forEach(function(sprint) {
                            select.append(`<option value="${sprint.id}">${sprint.name} (${sprint.status})</option>`);
                        });

                        // Auto-select current sprint if available
                        const currentSprint = data.find(s => s.status === 'Active');
                        if (currentSprint) {
                            select.val(currentSprint.id).trigger('change');
                        }
                    }
                })
                .fail(function() {
                    console.error('Failed to load sprints');
                });
        }

        function loadUserStories(sprintId) {
            // Clear all columns
            $('.kanban-cards').empty();

            $.get('@Url.Action("GetSprintUserStories", "Agile")', { sprintId: sprintId })
                .done(function(data) {
                    if (data && data.length) {
                        data.forEach(function(story) {
                            const storyCard = createUserStoryCard(story);
                            const columnId = getColumnIdFromStatus(story.status);
                            $(`#${columnId}`).append(storyCard);
                        });
                    }

                    updateColumnCounts();
                    updateSprintProgress(data || []);
                })
                .fail(function() {
                    console.error('Failed to load user stories');
                });
        }

        function createUserStoryCard(story) {
            const priorityClasses = {
                'critical': 'bg-danger-100 text-danger-700 dark:bg-danger-900/30 dark:text-danger-300',
                'high': 'bg-warning-100 text-warning-700 dark:bg-warning-900/30 dark:text-warning-300',
                'medium': 'bg-info-100 text-info-700 dark:bg-info-900/30 dark:text-info-300',
                'low': 'bg-success-100 text-success-700 dark:bg-success-900/30 dark:text-success-300'
            };

            const priorityClass = priorityClasses[story.priority.toLowerCase()] || 'bg-neutral-100 text-neutral-700 dark:bg-dark-700 dark:text-dark-300';
            const storyPointsBadge = story.storyPoints ?
                `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300">${story.storyPoints} SP</span>` : '';

            const epicBadge = story.epic ?
                `<span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300" title="Epic: ${story.epic.title}">
                    <i class="fas fa-mountain mr-1"></i>${story.epic.epicKey}
                </span>` : '';

            const assigneeBadge = story.assignedTo ?
                `<div class="w-7 h-7 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-sm" title="Assigned to: ${story.assignedTo.userName}">
                    <span class="text-xs font-semibold text-white">${story.assignedTo.userName.substring(0, 1).toUpperCase()}</span>
                </div>` :
                `<div class="w-7 h-7 bg-neutral-200 dark:bg-dark-700 rounded-full flex items-center justify-center" title="Unassigned">
                    <i class="fas fa-user text-neutral-400 dark:text-dark-400 text-xs"></i>
                </div>`;

            return `
                <div class="kanban-card bg-white dark:bg-dark-800 border border-neutral-200 dark:border-dark-600 rounded-xl p-4 cursor-move shadow-sm hover:shadow-lg transition-all duration-200 group"
                     draggable="true"
                     ondragstart="drag(event)"
                     data-story-id="${story.id}"
                     data-sprint-id="${story.sprintId || ''}"
                     data-priority="${story.priority.toLowerCase()}"
                     onclick="openUserStoryModal(${story.id})">

                    <!-- Card Header -->
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center gap-2">
                            <span class="text-xs font-mono text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-700 px-2 py-1 rounded">US-${story.id}</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${priorityClass}">
                                ${story.priority}
                            </span>
                        </div>
                        ${storyPointsBadge}
                    </div>

                    <!-- Card Content -->
                    <h4 class="font-semibold text-neutral-900 dark:text-dark-50 mb-3 text-sm line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                        ${story.title}
                    </h4>

                    <!-- Card Footer -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            ${epicBadge}
                        </div>
                        ${assigneeBadge}
                    </div>
                </div>
            `;
        }

        function initializeKanban() {
            // Make columns sortable
            $('.kanban-cards').each(function() {
                new Sortable(this, {
                    group: 'kanban',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    onEnd: function(evt) {
                        const storyId = $(evt.item).data('story-id');
                        const newStatus = $(evt.to).data('status');

                        updateUserStoryStatus(storyId, newStatus);
                        updateColumnCounts();
                        updateWipLimit();
                    }
                });
            });
        }

        // Drag and Drop functionality
        function allowDrop(ev) {
            ev.preventDefault();
        }

        function drag(ev) {
            ev.dataTransfer.setData("text", ev.target.getAttribute('data-story-id'));
            ev.target.style.opacity = '0.5';
        }

        function drop(ev) {
            ev.preventDefault();
            const storyId = ev.dataTransfer.getData("text");
            const columnElement = ev.currentTarget.closest('.kanban-column');
            const newStatus = columnElement.getAttribute('data-status');

            // Reset opacity
            document.querySelector(`[data-story-id="${storyId}"]`).style.opacity = '1';

            // Move the card
            updateUserStoryStatus(storyId, newStatus);
        }

        async function updateUserStoryStatus(storyId, newStatus) {
            try {
                const response = await fetch('@Url.Action("MoveCard")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({
                        cardId: parseInt(storyId),
                        columnId: newStatus
                    })
                });

                const result = await response.json();

                if (result.success) {
                    updateColumnCounts();
                    updateWipLimit();
                } else {
                    console.error('Failed to update user story status:', result.message);
                    location.reload(); // Reload to reset the UI
                }
            } catch (error) {
                console.error('Error updating user story status:', error);
                location.reload();
            }
        }

        function updateColumnCounts() {
            $('#backlogCount').text($('#backlogColumn .kanban-card').length);
            $('#readyCount').text($('#readyColumn .kanban-card').length);
            $('#inprogressCount').text($('#inprogressColumn .kanban-card').length);
            $('#reviewCount').text($('#reviewColumn .kanban-card').length);
            $('#doneCount').text($('#doneColumn .kanban-card').length);
        }

        function updateWipLimit() {
            const wipLimit = 3; // This could be configurable
            const inProgressCount = $('#inprogressColumn .kanban-card').length;
            const wipPercentage = Math.min((inProgressCount / wipLimit) * 100, 100);

            $('#wipStatus').text(`${inProgressCount} / ${wipLimit}`);
            $('#wipProgressBar').css('width', wipPercentage + '%');

            // Change color based on WIP limit
            const progressBar = $('#wipProgressBar');
            progressBar.removeClass('bg-success-500 bg-warning-500 bg-danger-500');

            if (wipPercentage > 100) {
                progressBar.addClass('bg-danger-500');
            } else if (wipPercentage > 80) {
                progressBar.addClass('bg-warning-500');
            } else {
                progressBar.addClass('bg-success-500');
            }
        }

        function updateSprintProgress(stories) {
            const totalStories = stories.length;
            const doneStories = stories.filter(s => s.status === 'Done').length;
            const inProgressStories = stories.filter(s => s.status === 'InProgress').length;
            const progress = totalStories > 0 ? Math.round((doneStories / totalStories) * 100) : 0;

            // Update progress circle
            const progressCircle = document.getElementById('sprintProgressCircle');
            if (progressCircle) {
                progressCircle.style.strokeDasharray = `${progress}, 100`;
            }

            // Update progress text
            document.getElementById('sprintProgress').textContent = progress + '%';

            // Update stats
            document.getElementById('completedStories').textContent = doneStories;
            document.getElementById('remainingStories').textContent = totalStories - doneStories;
        }

        async function openUserStoryModal(storyId) {
            const modal = document.getElementById('userStoryModal');
            const content = document.getElementById('userStoryModalContent');

            // Show loading with enhanced design
            content.innerHTML = `
                <div class="flex flex-col items-center justify-center py-12">
                    <div class="relative">
                        <div class="animate-spin rounded-full h-12 w-12 border-4 border-primary-200 dark:border-primary-800"></div>
                        <div class="animate-spin rounded-full h-12 w-12 border-4 border-transparent border-t-primary-600 dark:border-t-primary-400 absolute top-0 left-0"></div>
                    </div>
                    <p class="mt-6 text-neutral-600 dark:text-dark-400 font-medium">Loading user story details...</p>
                </div>
            `;
            modal.classList.remove('hidden');

            try {
                const response = await fetch(`@Url.Action("UserStoryDetails")/${storyId}`);
                if (response.ok) {
                    const html = await response.text();
                    content.innerHTML = html;
                } else {
                    throw new Error('Failed to fetch user story details');
                }
            } catch (error) {
                content.innerHTML = `
                    <div class="flex flex-col items-center justify-center py-12">
                        <div class="w-16 h-16 bg-danger-100 dark:bg-danger-900/30 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 mb-2">Failed to Load</h3>
                        <p class="text-neutral-600 dark:text-dark-400 text-center mb-6">Unable to load user story details. Please try again.</p>
                        <div class="flex gap-3">
                            <button onclick="openUserStoryModal(${storyId})" class="btn-primary-custom">
                                <i class="fas fa-redo mr-2"></i>
                                Retry
                            </button>
                            <button onclick="closeUserStoryModal()" class="btn-outline-custom">
                                Close
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        function closeUserStoryModal() {
            document.getElementById('userStoryModal').classList.add('hidden');
        }

        function openBoardSettings() {
            // Implementation for board settings
            alert('Board settings functionality to be implemented');
        }

        // Close modal when clicking outside
        document.getElementById('userStoryModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeUserStoryModal();
            }
        });

        // Prevent drag events from bubbling up
        document.addEventListener('dragend', function(e) {
            e.target.style.opacity = '1';
        });

        // Sprint filtering functions
        function filterBySprint() {
            const sprintId = document.getElementById('sprintFilter').value;
            const cards = document.querySelectorAll('.kanban-card');
            let visibleCount = 0;

            cards.forEach(card => {
                const cardSprintId = card.getAttribute('data-sprint-id');

                if (!sprintId || cardSprintId === sprintId) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // Update story count
            document.getElementById('totalStories').textContent = visibleCount;

            // Update column counts
            updateColumnCounts();
        }

        function refreshBoard() {
            // Show loading state
            const refreshBtn = event.target.closest('button');
            const originalIcon = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Reload kanban data
            const currentSprintId = document.getElementById('sprintSelect').value;
            if (currentSprintId) {
                loadUserStories(currentSprintId);
            } else {
                // Load all stories if no sprint selected
                loadAllUserStories();
            }

            // Reset button after a delay
            setTimeout(() => {
                refreshBtn.innerHTML = originalIcon;
                filterBySprint();
            }, 1000);
        }

        function loadAllUserStories() {
            // Clear all columns
            $('.kanban-cards').empty();

            $.get('@Url.Action("GetKanbanData", "Agile")', { projectId: @projectId })
                .done(function(data) {
                    if (data && data.length) {
                        data.forEach(function(story) {
                            const storyCard = createUserStoryCard(story);
                            const columnId = getColumnIdFromStatus(story.status);
                            $(`#${columnId}`).append(storyCard);
                        });
                    }
                    updateColumnCounts();
                    updateSprintProgress(data || []);
                })
                .fail(function() {
                    console.error('Failed to load user stories');
                });
        }

        function getColumnIdFromStatus(status) {
            const statusMap = {
                'Backlog': 'backlogColumn',
                'Ready': 'readyColumn',
                'InProgress': 'inprogressColumn',
                'Review': 'reviewColumn',
                'Done': 'doneColumn'
            };
            return statusMap[status] || 'backlogColumn';
        }

        function toggleCompactView() {
            const board = document.querySelector('.kanban-board');
            const columns = document.querySelectorAll('.kanban-column');

            board.classList.toggle('compact-view');

            if (board.classList.contains('compact-view')) {
                columns.forEach(col => {
                    col.classList.remove('w-72');
                    col.classList.add('w-64');
                });
            } else {
                columns.forEach(col => {
                    col.classList.remove('w-64');
                    col.classList.add('w-72');
                });
            }
        }

        function updateColumnCounts() {
            const columns = ['backlog', 'ready', 'inprogress', 'review', 'done'];

            columns.forEach(status => {
                const column = document.getElementById(status + 'Column');
                const visibleCards = column.querySelectorAll('.kanban-card[style*="display: block"], .kanban-card:not([style*="display: none"])');
                const countElement = document.getElementById(status + 'Count');

                if (countElement) {
                    countElement.textContent = visibleCards.length;
                }
            });

            // Update WIP limit indicator
            const inProgressCards = document.getElementById('inprogressColumn').querySelectorAll('.kanban-card[style*="display: block"], .kanban-card:not([style*="display: none"])');
            const wipLimit = 3; // This could be configurable
            const wipPercentage = Math.min(100, (inProgressCards.length / wipLimit) * 100);

            const wipProgressBar = document.getElementById('wipProgressBar');
            const wipStatus = document.getElementById('wipStatus');

            if (wipProgressBar && wipStatus) {
                wipProgressBar.style.width = wipPercentage + '%';
                wipStatus.textContent = `${inProgressCards.length} / ${wipLimit}`;

                // Update color based on WIP limit
                wipProgressBar.className = 'h-1 rounded-full transition-all duration-300 ' +
                    (wipPercentage >= 100 ? 'bg-red-500' :
                     wipPercentage >= 80 ? 'bg-amber-500' : 'bg-amber-500');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial filter to active sprint if available
            const sprintFilter = document.getElementById('sprintFilter');
            const activeOption = sprintFilter.querySelector('option[selected]');
            if (activeOption) {
                sprintFilter.value = activeOption.value;
            }

            // Apply initial filter
            setTimeout(() => {
                filterBySprint();
            }, 500); // Wait for stories to load
        });
    </script>
}

@section Styles {
    <style>
        /* Enhanced Kanban Board Styles */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .kanban-card {
            transition: all 0.2s ease-in-out;
        }

        .kanban-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .dark .kanban-card:hover {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }

        .kanban-cards {
            min-height: 500px;
            transition: background-color 0.2s ease-in-out;
        }

        .kanban-cards.drag-over {
            background-color: rgba(59, 130, 246, 0.05);
            border: 2px dashed rgba(59, 130, 246, 0.3);
        }

        .kanban-column {
            min-width: 320px;
        }

        .sortable-ghost {
            opacity: 0.3;
            transform: rotate(5deg);
        }

        .sortable-chosen {
            transform: scale(1.05);
            z-index: 999;
        }

        .sortable-drag {
            transform: rotate(3deg);
            z-index: 1000;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
        }

        .kanban-board {
            min-height: 700px;
        }

        .kanban-board.compact-view .kanban-column {
            width: 18rem !important;
        }

        .kanban-board.compact-view .kanban-card {
            padding: 0.75rem !important;
        }

        .kanban-board.compact-view .kanban-card h4 {
            font-size: 0.8rem !important;
            line-height: 1.1rem !important;
        }

        /* WIP Limit Indicators */
        .wip-limit-warning .kanban-cards {
            background-color: rgba(251, 191, 36, 0.1) !important;
            border-left: 4px solid #f59e0b !important;
        }

        .wip-limit-exceeded .kanban-cards {
            background-color: rgba(239, 68, 68, 0.1) !important;
            border-left: 4px solid #ef4444 !important;
        }

        /* Sprint Progress Circle Animation */
        #sprintProgressCircle {
            transition: stroke-dasharray 0.6s ease-in-out;
        }

        /* Enhanced Modal */
        #userStoryModal {
            backdrop-filter: blur(4px);
        }

        /* Responsive Design */
        @@media (max-width: 768px) {
            .kanban-column {
                min-width: 280px;
                width: 280px !important;
            }

            .kanban-card {
                padding: 0.75rem !important;
            }
        }

        /* Loading Animation */
        @@keyframes pulse-ring {
            0% {
                transform: scale(0.33);
            }
            40%, 50% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: scale(1.33);
            }
        }

        .pulse-ring {
            animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }

        /* Card Priority Indicators */
        .kanban-card[data-priority="critical"] {
            border-left: 4px solid #ef4444;
        }

        .kanban-card[data-priority="high"] {
            border-left: 4px solid #f59e0b;
        }

        .kanban-card[data-priority="medium"] {
            border-left: 4px solid #3b82f6;
        }

        .kanban-card[data-priority="low"] {
            border-left: 4px solid #10b981;
        }
    </style>
}
