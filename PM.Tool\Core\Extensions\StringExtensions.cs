namespace PM.Tool.Core.Extensions
{
    public static class StringExtensions
    {
        /// <summary>
        /// Truncates a string to the specified maximum length and adds ellipsis if truncated
        /// </summary>
        /// <param name="value">The string to truncate</param>
        /// <param name="maxLength">Maximum length of the string</param>
        /// <param name="ellipsis">The ellipsis string to append (default: "...")</param>
        /// <returns>The truncated string</returns>
        public static string Truncate(this string value, int maxLength, string ellipsis = "...")
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            if (value.Length <= maxLength)
                return value;

            return value.Substring(0, maxLength - ellipsis.Length) + ellipsis;
        }

        /// <summary>
        /// Truncates a string to the specified maximum length at word boundaries
        /// </summary>
        /// <param name="value">The string to truncate</param>
        /// <param name="maxLength">Maximum length of the string</param>
        /// <param name="ellipsis">The ellipsis string to append (default: "...")</param>
        /// <returns>The truncated string</returns>
        public static string TruncateAtWord(this string value, int maxLength, string ellipsis = "...")
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            if (value.Length <= maxLength)
                return value;

            var truncated = value.Substring(0, maxLength - ellipsis.Length);
            var lastSpace = truncated.LastIndexOf(' ');
            
            if (lastSpace > 0)
                truncated = truncated.Substring(0, lastSpace);

            return truncated + ellipsis;
        }

        /// <summary>
        /// Converts a string to title case
        /// </summary>
        /// <param name="value">The string to convert</param>
        /// <returns>The string in title case</returns>
        public static string ToTitleCase(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            return System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(value.ToLower());
        }

        /// <summary>
        /// Converts a string to kebab-case
        /// </summary>
        /// <param name="value">The string to convert</param>
        /// <returns>The string in kebab-case</returns>
        public static string ToKebabCase(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            return System.Text.RegularExpressions.Regex.Replace(value, "([a-z])([A-Z])", "$1-$2").ToLower();
        }

        /// <summary>
        /// Converts a string to camelCase
        /// </summary>
        /// <param name="value">The string to convert</param>
        /// <returns>The string in camelCase</returns>
        public static string ToCamelCase(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            if (value.Length == 1)
                return value.ToLower();

            return char.ToLower(value[0]) + value.Substring(1);
        }

        /// <summary>
        /// Removes HTML tags from a string
        /// </summary>
        /// <param name="value">The string containing HTML</param>
        /// <returns>The string with HTML tags removed</returns>
        public static string StripHtml(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            return System.Text.RegularExpressions.Regex.Replace(value, "<.*?>", string.Empty);
        }

        /// <summary>
        /// Checks if a string is a valid email address
        /// </summary>
        /// <param name="value">The string to validate</param>
        /// <returns>True if the string is a valid email address</returns>
        public static bool IsValidEmail(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(value);
                return addr.Address == value;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Converts a string to a URL-friendly slug
        /// </summary>
        /// <param name="value">The string to convert</param>
        /// <returns>A URL-friendly slug</returns>
        public static string ToSlug(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            // Convert to lowercase
            value = value.ToLower();

            // Remove invalid characters
            value = System.Text.RegularExpressions.Regex.Replace(value, @"[^a-z0-9\s-]", "");

            // Convert multiple spaces into one space
            value = System.Text.RegularExpressions.Regex.Replace(value, @"\s+", " ").Trim();

            // Replace spaces with hyphens
            value = value.Replace(" ", "-");

            return value;
        }

        /// <summary>
        /// Masks sensitive information in a string
        /// </summary>
        /// <param name="value">The string to mask</param>
        /// <param name="visibleChars">Number of characters to keep visible at the start</param>
        /// <param name="maskChar">Character to use for masking</param>
        /// <returns>The masked string</returns>
        public static string Mask(this string value, int visibleChars = 4, char maskChar = '*')
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            if (value.Length <= visibleChars)
                return new string(maskChar, value.Length);

            return value.Substring(0, visibleChars) + new string(maskChar, value.Length - visibleChars);
        }

        /// <summary>
        /// Extracts initials from a full name
        /// </summary>
        /// <param name="value">The full name</param>
        /// <param name="maxInitials">Maximum number of initials to return</param>
        /// <returns>The initials</returns>
        public static string GetInitials(this string value, int maxInitials = 2)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            var words = value.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var initials = words.Take(maxInitials).Select(w => char.ToUpper(w[0]));
            
            return string.Join("", initials);
        }
    }
}
