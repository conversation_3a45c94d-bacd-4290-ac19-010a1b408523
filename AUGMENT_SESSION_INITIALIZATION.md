# Augment Session Initialization Guide

## 🎯 Purpose
This document provides step-by-step instructions for Augment to properly initialize any development session, establish project context, and maintain consistency across sessions regardless of memory resets or account changes.

## 📋 Session Initialization Checklist

### Phase 1: Project Discovery & Context Setting (5-10 minutes)

#### Step 1: Identify Project Type and Documentation
```
1. First, examine the workspace root directory structure
2. Look for these key documentation files (in order of priority):
   - AUGMENT_DEVELOPMENT_GUIDE.md (Project-specific guide)
   - AUGMENT_PROJECT_PLAN.md (Current project plan and sprints)
   - AUGMENT_SPRINT_TRACKER.md (Active sprint tracking)
   - AUGMENT_FEATURE_BACKLOG.md (Feature roadmap)
   - AUGMENT_QUICK_REFERENCE.md (Common patterns and commands)
   - AUGMENT_DEVELOPMENT_CHECKLIST.md (Quality checklist)
   - README.md (General project information)
```

#### Step 2: Load Project Context
```
If Augment documentation exists:
1. Read AUGMENT_DEVELOPMENT_GUIDE.md completely
2. Review current sprint status in AUGMENT_SPRINT_TRACKER.md
3. Check today's goals and yesterday's progress
4. Identify current feature being worked on

If no Augment documentation exists:
1. Read README.md and any existing documentation
2. Analyze project structure and technology stack
3. Offer to create the Augment documentation system
```

#### Step 3: Establish Current Session Context
```
Based on the documentation, determine:
- Current sprint and day number
- Active feature being developed
- Last completed tasks
- Today's specific goals
- Any blockers or challenges from previous session
```

### Phase 2: Technical Context Gathering (5-10 minutes)

#### Step 4: Understand Current Codebase State
```
Use these tools to gather technical context:

1. codebase-retrieval: 
   - Query for the specific feature being worked on
   - Understand existing patterns and architecture
   - Identify related components and services

2. git-commit-retrieval:
   - Review recent commits (last 5-10)
   - Understand recent implementation patterns
   - Identify any ongoing work or partial implementations

3. view tool:
   - Examine key files mentioned in sprint tracker
   - Check current state of files being modified
   - Understand project structure if new to project
```

#### Step 5: Validate Development Environment
```
Check these key aspects:
- Project builds successfully
- Tests are passing
- Database is accessible (if applicable)
- Required tools and dependencies are available
```

### Phase 3: Session Planning & Goal Setting (2-5 minutes)

#### Step 6: Confirm Today's Objectives
```
Based on sprint tracker and project plan:
1. Confirm today's specific goals
2. Identify any dependencies or prerequisites
3. Plan the development approach
4. Estimate time requirements
5. Identify potential challenges
```

#### Step 7: Set Session Expectations
```
Communicate to user:
- Current project and sprint context
- Today's planned objectives
- Estimated time and complexity
- Any questions or clarifications needed
- Proposed approach for the session
```

## 🔄 Session Initialization Script Template

### For Existing Projects with Augment Documentation
```
"I'm initializing our development session for [PROJECT_NAME]. 

Let me establish context:
- Current Sprint: [SPRINT_NAME] - Day [X] of [Y]
- Today's Focus: [MAIN_FEATURE/TASK]
- Yesterday's Progress: [BRIEF_SUMMARY]
- Today's Goals: [LIST_OF_GOALS]

I've reviewed the codebase and recent commits. Based on our sprint plan, 
I recommend we focus on [SPECIFIC_RECOMMENDATION].

Are you ready to proceed with [TODAY'S_MAIN_TASK], or would you like to 
adjust today's priorities?"
```

### For New Projects or Missing Documentation
```
"I notice this project doesn't have Augment-specific documentation yet. 

Based on my analysis:
- Project Type: [TECHNOLOGY_STACK]
- Architecture: [ARCHITECTURE_PATTERN]
- Current State: [ASSESSMENT]

I recommend we first establish the Augment documentation system to ensure 
consistent development across sessions. This includes:
1. Development guide with project patterns
2. Sprint planning and tracking system
3. Feature backlog and roadmap
4. Quick reference for common tasks

Would you like me to create this documentation system first, or proceed 
with your immediate development needs?"
```

## 📚 Documentation System Templates

### For Different Project Types

#### Web Application Projects
```
Required Documentation:
- AUGMENT_DEVELOPMENT_GUIDE.md (Architecture, patterns, conventions)
- AUGMENT_PROJECT_PLAN.md (Roadmap, sprints, milestones)
- AUGMENT_SPRINT_TRACKER.md (Current sprint progress)
- AUGMENT_FEATURE_BACKLOG.md (Prioritized features)
- AUGMENT_QUICK_REFERENCE.md (Commands, patterns, troubleshooting)
- AUGMENT_DEVELOPMENT_CHECKLIST.md (Quality gates, standards)
```

#### API/Backend Projects
```
Additional Focus Areas:
- API design patterns and versioning
- Database schema and migration strategies
- Testing approaches (unit, integration, E2E)
- Performance and scalability considerations
- Security and authentication patterns
```

#### Frontend Projects
```
Additional Focus Areas:
- Component architecture and reusability
- State management patterns
- UI/UX design system
- Performance optimization
- Cross-browser compatibility
```

#### Mobile Projects
```
Additional Focus Areas:
- Platform-specific considerations
- Native vs cross-platform decisions
- App store deployment processes
- Device testing strategies
- Performance on mobile devices
```

## 🛠️ Context Maintenance During Session

### Continuous Context Updates
```
Throughout the session:
1. Update sprint tracker with completed tasks
2. Note any architectural decisions made
3. Document new patterns or conventions established
4. Record any blockers or challenges encountered
5. Update tomorrow's planned tasks
```

### Session Handoff Preparation
```
Before ending session:
1. Update daily progress in sprint tracker
2. Commit all code changes with descriptive messages
3. Note any incomplete work or next steps
4. Update project documentation if patterns changed
5. Prepare context for next session
```

## 🚨 Common Initialization Scenarios

### Scenario 1: Memory Reset - Existing Project
```
1. Read all Augment documentation files
2. Review recent git commits for context
3. Check current sprint status and today's goals
4. Gather technical context for active features
5. Confirm understanding with user before proceeding
```

### Scenario 2: New Feature Development
```
1. Review feature requirements in backlog
2. Understand existing related code patterns
3. Check dependencies and prerequisites
4. Plan implementation approach
5. Break down into manageable tasks
```

### Scenario 3: Bug Fix or Maintenance
```
1. Understand the issue from description
2. Review related code and recent changes
3. Identify root cause and impact
4. Plan fix approach with minimal risk
5. Consider testing and rollback strategies
```

### Scenario 4: Project Setup/New Project
```
1. Understand project requirements and goals
2. Recommend appropriate architecture and tools
3. Create initial project structure
4. Set up development environment
5. Create Augment documentation system
```

## 📝 Session Documentation Template

### Daily Session Summary
```
## [Date] - [Project] Session Summary

### Context Established
- Sprint: [CURRENT_SPRINT]
- Focus: [MAIN_FEATURE]
- Goals: [TODAY'S_GOALS]

### Work Completed
- [COMPLETED_TASK_1]
- [COMPLETED_TASK_2]

### Decisions Made
- [ARCHITECTURAL_DECISION_1]
- [PATTERN_ESTABLISHED_1]

### Next Session Preparation
- Priority: [NEXT_PRIORITY]
- Context: [IMPORTANT_CONTEXT]
- Files: [KEY_FILES_TO_REVIEW]
```

## 🎯 Success Criteria

### Successful Session Initialization Includes:
- [ ] Clear understanding of project context and current state
- [ ] Identification of today's specific goals and priorities
- [ ] Technical context gathered from codebase and recent changes
- [ ] Development approach planned and confirmed
- [ ] User expectations set and aligned
- [ ] Ready to begin productive development work

### Quality Indicators:
- [ ] No time wasted on context gathering during development
- [ ] Consistent patterns and approaches maintained
- [ ] Progress tracked and documented
- [ ] Smooth handoff prepared for next session

This initialization guide ensures every Augment session starts with proper context and maintains consistency across all development work, regardless of memory resets or project complexity.
