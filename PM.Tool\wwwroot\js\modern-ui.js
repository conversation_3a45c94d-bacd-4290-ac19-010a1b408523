/**
 * Modern UI Framework for PM Tool
 * Professional-grade UI components with accessibility and internationalization
 */

class ModernUI {
    static init() {
        this.initializeComponents();
        this.setupEventListeners();
        this.initializeTooltips();
        this.initializeModals();
        this.setupKeyboardShortcuts();
        this.initializeSearch();
        this.setupThemeToggle();
        this.initializeNotifications();
    }

    static initializeComponents() {
        // Initialize all modern UI components
        this.initializeCards();
        this.initializeButtons();
        this.initializeForms();
        this.initializeTables();
        this.initializeProgressBars();
        this.initializeAlerts();
    }

    static setupEventListeners() {
        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const sidebar = document.getElementById('sidebar');
        
        if (mobileMenuToggle && sidebar) {
            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
            });
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (sidebar && !sidebar.contains(e.target) && !mobileMenuToggle?.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    static initializeTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    static initializeModals() {
        // Initialize Bootstrap modals with custom options
        const modalElements = document.querySelectorAll('.modal');
        modalElements.forEach(modal => {
            const bsModal = new bootstrap.Modal(modal, {
                backdrop: 'static',
                keyboard: true
            });
            
            // Add fade-in animation
            modal.addEventListener('show.bs.modal', () => {
                modal.classList.add('fade-in');
            });
        });
    }

    static setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for global search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.getElementById('global-search');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Escape to close modals and dropdowns
            if (e.key === 'Escape') {
                // Close any open dropdowns
                document.querySelectorAll('.dropdown-menu.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
                
                // Close mobile menu
                const sidebar = document.getElementById('sidebar');
                if (sidebar) {
                    sidebar.classList.remove('show');
                }
            }

            // Alt + M for mobile menu toggle
            if (e.altKey && e.key === 'm') {
                e.preventDefault();
                const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
                if (mobileMenuToggle) {
                    mobileMenuToggle.click();
                }
            }
        });
    }

    static initializeSearch() {
        const searchInput = document.getElementById('global-search');
        if (!searchInput) return;

        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();
            
            if (query.length < 2) {
                this.hideSearchResults();
                return;
            }
            
            searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, 300);
        });

        // Handle search keyboard navigation
        searchInput.addEventListener('keydown', (e) => {
            const results = document.querySelector('.search-results');
            if (!results) return;

            const items = results.querySelectorAll('.search-result-item');
            let currentIndex = Array.from(items).findIndex(item => item.classList.contains('active'));

            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    currentIndex = Math.min(currentIndex + 1, items.length - 1);
                    this.highlightSearchResult(items, currentIndex);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    currentIndex = Math.max(currentIndex - 1, 0);
                    this.highlightSearchResult(items, currentIndex);
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (currentIndex >= 0 && items[currentIndex]) {
                        items[currentIndex].click();
                    }
                    break;
            }
        });
    }

    static async performSearch(query) {
        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            const results = await response.json();
            this.displaySearchResults(results);
        } catch (error) {
            console.error('Search error:', error);
        }
    }

    static displaySearchResults(results) {
        const searchContainer = document.querySelector('.search-container');
        if (!searchContainer) return;

        let resultsContainer = searchContainer.querySelector('.search-results');
        if (!resultsContainer) {
            resultsContainer = document.createElement('div');
            resultsContainer.className = 'search-results';
            searchContainer.appendChild(resultsContainer);
        }

        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="search-no-results">No results found</div>';
        } else {
            resultsContainer.innerHTML = results.map(result => `
                <div class="search-result-item" data-url="${result.url}">
                    <div class="search-result-icon">
                        <i class="${result.icon || 'fas fa-file'}"></i>
                    </div>
                    <div class="search-result-content">
                        <div class="search-result-title">${result.title}</div>
                        <div class="search-result-description">${result.description}</div>
                    </div>
                </div>
            `).join('');

            // Add click handlers
            resultsContainer.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', () => {
                    window.location.href = item.dataset.url;
                });
            });
        }

        resultsContainer.style.display = 'block';
    }

    static hideSearchResults() {
        const resultsContainer = document.querySelector('.search-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
    }

    static highlightSearchResult(items, index) {
        items.forEach((item, i) => {
            item.classList.toggle('active', i === index);
        });
    }

    static setupThemeToggle() {
        // Check for saved theme preference or default to 'light'
        const currentTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', currentTheme);

        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                
                // Update toggle icon
                const icon = themeToggle.querySelector('i');
                if (icon) {
                    icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
                }
            });
        }
    }

    static initializeNotifications() {
        try {
            // Only load notifications if the API endpoint exists
            this.loadNotifications();

            // Set up real-time notifications if SignalR is available
            if (typeof signalR !== 'undefined') {
                this.setupRealTimeNotifications();
            }
        } catch (error) {
            console.warn('Notifications initialization skipped:', error);
        }
    }

    static async loadNotifications() {
        try {
            // Check if notifications API is available
            const response = await fetch('/api/notifications');
            if (response.ok) {
                const notifications = await response.json();
                this.displayNotifications(notifications);
            } else {
                console.info('Notifications API not available, skipping notification loading');
            }
        } catch (error) {
            console.info('Notifications API not available, skipping notification loading');
            // Don't throw error, just skip notifications
        }
    }

    static displayNotifications(notifications) {
        try {
            const notificationList = document.querySelector('.notification-list');
            if (!notificationList) {
                console.info('Notification list element not found, skipping notification display');
                return;
            }

            if (!notifications || notifications.length === 0) {
                notificationList.innerHTML = '<div class="notification-empty">No new notifications</div>';
            } else {
                notificationList.innerHTML = notifications.map(notification => `
                    <div class="notification-item ${notification.isRead ? '' : 'unread'}" data-id="${notification.id}">
                        <div class="notification-icon">
                            <i class="${notification.icon || 'fas fa-bell'}"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">${notification.title || 'Notification'}</div>
                            <div class="notification-message">${notification.message || ''}</div>
                            <div class="notification-time">${this.formatRelativeTime(notification.createdAt)}</div>
                        </div>
                        ${!notification.isRead ? '<div class="notification-badge"></div>' : ''}
                    </div>
                `).join('');

                // Add click handlers
                notificationList.querySelectorAll('.notification-item').forEach(item => {
                    item.addEventListener('click', () => {
                        this.markNotificationAsRead(item.dataset.id);
                        if (item.dataset.url) {
                            window.location.href = item.dataset.url;
                        }
                    });
                });
            }

            // Update notification count
            const notificationBadge = document.querySelector('.notification-badge');
            if (notificationBadge && notifications) {
                const unreadCount = notifications.filter(n => !n.isRead).length;
                notificationBadge.textContent = unreadCount;
                notificationBadge.style.display = unreadCount > 0 ? 'block' : 'none';
            }
        } catch (error) {
            console.warn('Error displaying notifications:', error);
        }
    }

    static async markNotificationAsRead(notificationId) {
        try {
            if (!notificationId) return;

            const response = await fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                console.info('Notification API not available for marking as read');
            }
        } catch (error) {
            console.info('Notification API not available for marking as read');
        }
    }

    static formatRelativeTime(dateString) {
        try {
            if (!dateString) return 'Unknown time';

            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Invalid date';

            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

            return date.toLocaleDateString();
        } catch (error) {
            console.warn('Error formatting relative time:', error);
            return 'Unknown time';
        }
    }

    static setupRealTimeNotifications() {
        try {
            console.info('Real-time notifications setup - SignalR integration would go here');
            // TODO: Implement SignalR real-time notifications when needed
        } catch (error) {
            console.warn('Error setting up real-time notifications:', error);
        }
    }

    // Component initialization methods
    static initializeCards() {
        document.querySelectorAll('.modern-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    }

    static initializeButtons() {
        document.querySelectorAll('.btn-modern').forEach(button => {
            button.addEventListener('click', function(e) {
                // Add ripple effect
                const ripple = document.createElement('span');
                ripple.className = 'ripple';
                
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }

    static initializeForms() {
        document.querySelectorAll('.form-control-modern').forEach(input => {
            // Add floating label effect
            const handleInputState = () => {
                if (input.value || input === document.activeElement) {
                    input.classList.add('has-value');
                } else {
                    input.classList.remove('has-value');
                }
            };

            input.addEventListener('focus', handleInputState);
            input.addEventListener('blur', handleInputState);
            input.addEventListener('input', handleInputState);
            
            // Initial state
            handleInputState();
        });
    }

    static initializeTables() {
        document.querySelectorAll('.table-modern').forEach(table => {
            // Add sorting functionality
            const headers = table.querySelectorAll('th[data-sortable]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    this.sortTable(table, header);
                });
            });
        });
    }

    static sortTable(table, header) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = !header.classList.contains('sort-asc');

        rows.sort((a, b) => {
            const aValue = a.children[columnIndex].textContent.trim();
            const bValue = b.children[columnIndex].textContent.trim();
            
            if (isAscending) {
                return aValue.localeCompare(bValue, undefined, { numeric: true });
            } else {
                return bValue.localeCompare(aValue, undefined, { numeric: true });
            }
        });

        // Update header classes
        table.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');

        // Reorder rows
        rows.forEach(row => tbody.appendChild(row));
    }

    static initializeProgressBars() {
        document.querySelectorAll('.progress-bar-modern').forEach(bar => {
            const targetWidth = bar.style.width || bar.getAttribute('data-width');
            bar.style.width = '0%';
            
            setTimeout(() => {
                bar.style.width = targetWidth;
            }, 100);
        });
    }

    static initializeAlerts() {
        document.querySelectorAll('.alert-modern .btn-close').forEach(closeBtn => {
            closeBtn.addEventListener('click', () => {
                const alert = closeBtn.closest('.alert-modern');
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-10px)';
                
                setTimeout(() => {
                    alert.remove();
                }, 300);
            });
        });
    }

    // Utility methods
    static showToast(message, type = 'info', duration = 5000) {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;

        // Map types to Tailwind colors
        const typeColors = {
            'info': 'bg-blue-500',
            'success': 'bg-green-500',
            'warning': 'bg-yellow-500',
            'danger': 'bg-red-500',
            'error': 'bg-red-500'
        };

        const bgColor = typeColors[type] || 'bg-blue-500';

        const toast = document.createElement('div');
        toast.className = `${bgColor} text-white px-4 py-3 rounded-lg shadow-lg flex items-center justify-between min-w-80 transform transition-all duration-300 translate-x-full opacity-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="flex items-center">
                <div class="mr-3">
                    ${type === 'success' ? '<i class="fas fa-check-circle"></i>' :
                      type === 'danger' || type === 'error' ? '<i class="fas fa-exclamation-circle"></i>' :
                      type === 'warning' ? '<i class="fas fa-exclamation-triangle"></i>' :
                      '<i class="fas fa-info-circle"></i>'}
                </div>
                <div class="flex-1">${message}</div>
            </div>
            <button type="button" class="ml-3 text-white hover:text-gray-200 focus:outline-none" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        toastContainer.appendChild(toast);

        // Trigger animation
        requestAnimationFrame(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
            toast.classList.add('translate-x-0', 'opacity-100');
        });

        // Auto-dismiss
        setTimeout(() => {
            if (toast.parentElement) {
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }
        }, duration);
    }

    static showModal(title, content, actions = []) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">${content}</div>
                    <div class="modal-footer">
                        ${actions.map(action => `
                            <button type="button" class="btn btn-${action.type || 'secondary'}" 
                                    onclick="${action.onclick || ''}" 
                                    ${action.dismiss ? 'data-bs-dismiss="modal"' : ''}>
                                ${action.text}
                            </button>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });

        return bsModal;
    }

    static confirmDialog(message, onConfirm, onCancel = null) {
        return this.showModal(
            'Confirm Action',
            message,
            [
                {
                    text: 'Cancel',
                    type: 'secondary',
                    dismiss: true,
                    onclick: onCancel ? `(${onCancel})()` : ''
                },
                {
                    text: 'Confirm',
                    type: 'primary',
                    dismiss: true,
                    onclick: `(${onConfirm})()`
                }
            ]
        );
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => ModernUI.init());
} else {
    ModernUI.init();
}

// Export for global use
window.ModernUI = ModernUI;
