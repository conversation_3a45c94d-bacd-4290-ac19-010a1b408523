using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Models.ViewModels;
using System.Security.Claims;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class ActivityController : SecureBaseController
    {
        private readonly ApplicationDbContext _context;
        private readonly IRealTimeNotificationService _realTimeService;
        private readonly ILogger<ActivityController> _logger;

        public ActivityController(
            ApplicationDbContext context,
            IRealTimeNotificationService realTimeService,
            ILogger<ActivityController> logger,
            IAuditService auditService) : base(auditService)
        {
            _context = context;
            _realTimeService = realTimeService;
            _logger = logger;
        }

        // GET: Activity/Feed
        public async Task<IActionResult> Feed(int? projectId = null)
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var userProjects = await GetUserProjectsAsync(userId);

                var viewModel = new ActivityFeedViewModel
                {
                    ProjectId = projectId,
                    UserProjects = userProjects,
                    Activities = await GetRecentActivitiesAsync(projectId, userId)
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading activity feed");
                return View(new ActivityFeedViewModel());
            }
        }

        // GET: Activity/GetActivities
        [HttpGet]
        public async Task<IActionResult> GetActivities(int? projectId = null, int page = 1, int pageSize = 20)
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var activities = await GetRecentActivitiesAsync(projectId, userId, page, pageSize);

                return Json(new
                {
                    success = true,
                    activities = activities.Select(a => new
                    {
                        id = a.Id,
                        type = a.Type,
                        title = a.Title,
                        description = a.Description,
                        userName = a.UserName,
                        projectName = a.ProjectName,
                        taskTitle = a.TaskTitle,
                        timestamp = a.Timestamp,
                        relativeTime = GetRelativeTime(a.Timestamp)
                    })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting activities");
                return Json(new { success = false, message = "Error loading activities" });
            }
        }

        // GET: Activity/GetProjectActivities
        [HttpGet]
        public async Task<IActionResult> GetProjectActivities(int projectId, int hours = 24)
        {
            try
            {
                var since = DateTime.UtcNow.AddHours(-hours);
                var activities = await GetProjectActivitiesAsync(projectId, since);

                return Json(new
                {
                    success = true,
                    activities = activities.Select(a => new
                    {
                        type = a.Type,
                        title = a.Title,
                        userName = a.UserName,
                        timestamp = a.Timestamp,
                        relativeTime = GetRelativeTime(a.Timestamp)
                    })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting project activities for project {ProjectId}", projectId);
                return Json(new { success = false, message = "Error loading project activities" });
            }
        }

        // POST: Activity/MarkAsRead
        [HttpPost]
        public async Task<IActionResult> MarkAsRead([FromBody] MarkAsReadRequest request)
        {
            try
            {
                // Mark activities as read (if we implement read status)
                // For now, just return success
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking activities as read");
                return Json(new { success = false, message = "Error marking activities as read" });
            }
        }

        // Private helper methods
        private async Task<List<ProjectSelectViewModel>> GetUserProjectsAsync(string userId)
        {
            return await _context.ProjectMembers
                .Where(pm => pm.UserId == userId && pm.IsActive)
                .Include(pm => pm.Project)
                .Select(pm => new ProjectSelectViewModel
                {
                    Id = pm.Project.Id,
                    Name = pm.Project.Name
                })
                .ToListAsync();
        }

        private async Task<List<ActivityItemViewModel>> GetRecentActivitiesAsync(int? projectId, string userId, int page = 1, int pageSize = 20)
        {
            var activities = new List<ActivityItemViewModel>();

            // Get user's projects if no specific project is selected
            var projectIds = new List<int>();
            if (projectId.HasValue)
            {
                projectIds.Add(projectId.Value);
            }
            else
            {
                projectIds = await _context.ProjectMembers
                    .Where(pm => pm.UserId == userId && pm.IsActive)
                    .Select(pm => pm.ProjectId)
                    .ToListAsync();
            }

            if (!projectIds.Any()) return activities;

            // Get audit logs for activities (Note: AuditLog doesn't have ProjectId, so we'll get all recent logs)
            var auditLogs = await _context.AuditLogs
                .Where(al => al.CreatedAt >= DateTime.UtcNow.AddDays(-7))
                .OrderByDescending(al => al.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            foreach (var log in auditLogs)
            {
                var activity = new ActivityItemViewModel
                {
                    Id = log.Id,
                    Type = GetActivityType(log.Action.ToString(), log.EntityName),
                    Title = GetActivityTitle(log.Action.ToString(), log.EntityName),
                    Description = GetActivityDescription(log),
                    UserName = await GetUserNameAsync(log.UserId),
                    ProjectName = "", // AuditLog doesn't have ProjectId
                    TaskTitle = await GetTaskTitleAsync(log.EntityName, log.EntityId),
                    Timestamp = log.CreatedAt
                };

                activities.Add(activity);
            }

            return activities;
        }

        private async Task<List<ActivityItemViewModel>> GetProjectActivitiesAsync(int projectId, DateTime since)
        {
            var activities = new List<ActivityItemViewModel>();

            var auditLogs = await _context.AuditLogs
                .Where(al => al.CreatedAt >= since)
                .OrderByDescending(al => al.CreatedAt)
                .Take(50)
                .ToListAsync();

            foreach (var log in auditLogs)
            {
                var activity = new ActivityItemViewModel
                {
                    Id = log.Id,
                    Type = GetActivityType(log.Action.ToString(), log.EntityName),
                    Title = GetActivityTitle(log.Action.ToString(), log.EntityName),
                    UserName = await GetUserNameAsync(log.UserId),
                    Timestamp = log.CreatedAt
                };

                activities.Add(activity);
            }

            return activities;
        }

        private string GetActivityType(string action, string entityName)
        {
            return $"{entityName.ToLower()}_{action.ToLower()}";
        }

        private string GetActivityTitle(string action, string entityName)
        {
            return action switch
            {
                "Create" => $"Created {entityName}",
                "Update" => $"Updated {entityName}",
                "Delete" => $"Deleted {entityName}",
                _ => $"{action} {entityName}"
            };
        }

        private string GetActivityDescription(dynamic log)
        {
            // Create a meaningful description based on the log data
            return $"{log.Action} performed on {log.EntityName}";
        }

        private async Task<string> GetUserNameAsync(string userId)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                return user?.FullName ?? "Unknown User";
            }
            catch
            {
                return "Unknown User";
            }
        }

        private async Task<string> GetProjectNameAsync(int? projectId)
        {
            if (!projectId.HasValue) return "";

            try
            {
                var project = await _context.Projects.FirstOrDefaultAsync(p => p.Id == projectId);
                return project?.Name ?? "";
            }
            catch
            {
                return "";
            }
        }

        private async Task<string> GetTaskTitleAsync(string entityName, int? entityId)
        {
            if (entityName != "Task" || !entityId.HasValue) return "";

            try
            {
                var task = await _context.Tasks.FirstOrDefaultAsync(t => t.Id == entityId);
                return task?.Title ?? "";
            }
            catch
            {
                return "";
            }
        }

        private string GetRelativeTime(DateTime timestamp)
        {
            var timeSpan = DateTime.UtcNow - timestamp;

            if (timeSpan.TotalMinutes < 1)
                return "Just now";
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} minutes ago";
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} hours ago";
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} days ago";

            return timestamp.ToString("MMM dd, yyyy");
        }
    }

    // Request models
    public class MarkAsReadRequest
    {
        public List<int> ActivityIds { get; set; } = new();
    }
}
