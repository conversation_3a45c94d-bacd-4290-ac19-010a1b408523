# PM.Tool - Augment Development Guide

## Project Overview
**PM.Tool** is an enterprise-grade project management application built with ASP.NET Core 9.0, following Clean Architecture principles. It's designed to compete with Azure DevOps and provides comprehensive project management, agile planning, and collaboration features.

### Key Technologies
- **Backend**: ASP.NET Core 9.0, Entity Framework Core, PostgreSQL
- **Frontend**: <PERSON><PERSON>s, Bootstrap 5, TailwindCSS, SignalR
- **Architecture**: Clean Architecture, CQRS with MediatR, Repository Pattern
- **Authentication**: ASP.NET Core Identity with role-based authorization
- **Real-time**: SignalR for collaboration and live updates
- **Validation**: FluentValidation
- **Logging**: Serilog with structured logging
- **API**: RESTful APIs with Swagger documentation

## Architecture & Patterns

### Clean Architecture Layers
```
PM.Tool/
├── Core/                    # Domain layer (entities, interfaces, business logic)
├── Application/             # Application layer (services, DTOs, validators)
├── Infrastructure/          # Infrastructure layer (external services, repositories)
├── Data/                   # Data access layer (EF Core, DbContext)
├── Controllers/            # Presentation layer (MVC controllers, API endpoints)
├── Views/                  # Razor views and components
└── Tests/                  # Test projects (unit, integration, E2E)
```

### Key Patterns Used
- **Repository Pattern**: Generic `IRepository<T>` with specific implementations
- **CQRS**: MediatR for command/query separation
- **Specification Pattern**: Complex query logic encapsulation
- **Dependency Injection**: Built-in ASP.NET Core DI container
- **Authorization**: Policy-based authorization with custom handlers

## Core Entities & Domain Model

### Primary Entities
- **ApplicationUser**: Extended Identity user with profile information
- **Project**: Core project entity with status, dates, budget tracking
- **TaskEntity**: Tasks with WBS support, dependencies, time tracking
- **Person**: Unified people management (employees, contractors, stakeholders)
- **Epic/Feature/UserStory**: Agile hierarchy for requirements
- **Sprint**: Agile sprint management with capacity planning
- **Risk**: Risk management with mitigation actions
- **Resource**: Resource allocation and skill management

### Key Features
- **Data Encryption**: Sensitive fields encrypted with `[Encrypted]` attribute
- **Audit Logging**: Comprehensive audit trail for all changes
- **Real-time Collaboration**: SignalR-based live updates and chat
- **Internationalization**: Multi-language support (25+ languages)
- **WBS Support**: Work Breakdown Structure with hierarchical tasks

## Development Standards & Conventions

### Coding Standards
1. **Naming Conventions**:
   - Controllers: `{Entity}Controller` (e.g., `ProjectsController`)
   - Services: `I{Entity}Service` interface, `{Entity}Service` implementation
   - Repositories: `I{Entity}Repository` interface, `{Entity}Repository` implementation
   - ViewModels: `{Entity}{Action}ViewModel` (e.g., `ProjectCreateViewModel`)

2. **File Organization**:
   - Group related functionality in folders
   - Use partial views for reusable components (`Views/Shared/Components/`)
   - Separate scripts and styles into partial views (`Views/{Entity}/Partials/`)

3. **Service Registration**:
   - All services registered in `Program.cs` with appropriate lifetime
   - Use `AddScoped` for most application services
   - Repository pattern with generic base repository

### UI/UX Standards
1. **Design System**: Consistent UI components with standardized styling
2. **Responsive Design**: Bootstrap 5 + TailwindCSS for modern responsive layouts
3. **Component Reusability**: Shared components in `Views/Shared/Components/`
4. **Page Structure**: Standardized page headers, breadcrumbs, and filter bars

### Database Conventions
1. **Entity Framework**: Code-first approach with migrations
2. **Soft Delete**: Use `IsDeleted` flag instead of hard deletes
3. **Audit Fields**: `CreatedAt`, `UpdatedAt`, `CreatedBy`, `UpdatedBy` on all entities
4. **Encryption**: Sensitive fields marked with `[Encrypted]` attribute

## Common Development Patterns

### Adding New Features
1. **Create Entity** in `Core/Entities/`
2. **Add Repository Interface** in `Core/Interfaces/`
3. **Implement Repository** in `Infrastructure/Repositories/`
4. **Create Service Interface** in `Core/Interfaces/`
5. **Implement Service** in `Application/Services/`
6. **Add Controller** with proper authorization
7. **Create ViewModels** for data transfer
8. **Build Views** using shared components
9. **Register Services** in `Program.cs`
10. **Add Database Migration**

### Controller Pattern
```csharp
[Authorize]
public class EntityController : SecureBaseController
{
    private readonly IEntityService _entityService;
    private readonly IAuthorizationService _authorizationService;

    // Standard CRUD actions with proper authorization
    // Use ViewModels for data transfer
    // Include audit logging for changes
    // Add real-time notifications where appropriate
}
```

### Service Pattern
```csharp
public class EntityService : IEntityService
{
    private readonly IRepository<Entity> _repository;
    private readonly IAuditService _auditService;
    
    // Implement business logic
    // Use async/await throughout
    // Include proper error handling
    // Log audit events for changes
}
```

## Testing Approach
- **Unit Tests**: Test business logic in services
- **Integration Tests**: Test controller actions and database interactions
- **E2E Tests**: Test complete user workflows
- **Test Structure**: Arrange-Act-Assert pattern
- **Mocking**: Use appropriate mocking for external dependencies

## Security Considerations
1. **Data Encryption**: Sensitive data encrypted at rest
2. **Authorization**: Policy-based authorization with resource-specific handlers
3. **Input Validation**: FluentValidation for all user inputs
4. **CSRF Protection**: Anti-forgery tokens on all forms
5. **SQL Injection**: Entity Framework prevents SQL injection
6. **XSS Protection**: Razor automatically encodes output

## Performance Optimization
1. **Async/Await**: All database operations are asynchronous
2. **Caching**: Memory cache for frequently accessed data
3. **Pagination**: Use `PaginatedList<T>` for large datasets
4. **Lazy Loading**: Careful use of Include() for related data
5. **SignalR**: Efficient real-time updates without polling

## Deployment & Configuration
- **Docker Support**: Dockerfile and docker-compose.yml included
- **Environment Configuration**: appsettings.json for different environments
- **Database**: PostgreSQL with connection string configuration
- **Logging**: Serilog with file and console outputs
- **Health Checks**: Built-in health check endpoints

## Key Files to Reference
- `Program.cs`: Service registration and middleware configuration
- `ApplicationDbContext.cs`: Database context and entity configuration
- `README.md`: Project setup and running instructions
- `Documentation/`: Comprehensive project documentation
- `SYSTEM_OVERVIEW.md`: High-level system architecture
- `DEVELOPER_GUIDE.md`: Detailed development guidelines

## Recent Development Focus
Based on recent commits, the project has been focusing on:
- Real-time collaboration features with SignalR
- Data encryption and security enhancements
- UI standardization and design system implementation
- Agile module enhancements with sprint analytics
- Comprehensive testing and quality improvements

## Next Steps for New Features
1. Review existing similar features in the codebase
2. Check recent commits for implementation patterns
3. Follow the established architecture and naming conventions
4. Implement with proper authorization and audit logging
5. Add comprehensive tests
6. Update documentation as needed

This guide should help maintain consistency across development sessions and provide clear patterns for implementing new features in the PM.Tool project.
