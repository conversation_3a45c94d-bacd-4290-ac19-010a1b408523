using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PM.Tool.Application.Services;
using PM.Tool.Core.Interfaces;
using System.Security.Claims;

namespace PM.Tool.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class MentionController : ControllerBase
    {
        private readonly IMentionService _mentionService;
        private readonly ILogger<MentionController> _logger;

        public MentionController(
            IMentionService mentionService,
            ILogger<MentionController> logger)
        {
            _mentionService = mentionService;
            _logger = logger;
        }

        /// <summary>
        /// Get users available for mentioning in a project context
        /// </summary>
        /// <param name="projectId">Optional project ID to filter users</param>
        /// <param name="query">Optional search query to filter users</param>
        /// <returns>List of mentionable users</returns>
        [HttpGet("users")]
        public async Task<IActionResult> GetMentionableUsers(int? projectId = null, string? query = null)
        {
            try
            {
                var users = await _mentionService.GetMentionableUsersAsync(projectId);

                // Filter by query if provided
                if (!string.IsNullOrWhiteSpace(query))
                {
                    var lowerQuery = query.ToLower();
                    users = users.Where(u =>
                    {
                        var user = u as dynamic;
                        return user.displayName.ToString().ToLower().Contains(lowerQuery) ||
                               user.username.ToString().ToLower().Contains(lowerQuery) ||
                               user.email.ToString().ToLower().Contains(lowerQuery);
                    }).ToList();
                }

                return Ok(users);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mentionable users for project {ProjectId}", projectId);
                return StatusCode(500, new { error = "Failed to get mentionable users" });
            }
        }

        /// <summary>
        /// Parse mentions from content
        /// </summary>
        /// <param name="request">Content to parse</param>
        /// <returns>List of mentioned user IDs</returns>
        [HttpPost("parse")]
        public async Task<IActionResult> ParseMentions([FromBody] ParseMentionsRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.Content))
                {
                    return Ok(new List<string>());
                }

                var mentionedUserIds = await _mentionService.ParseMentionsAsync(request.Content);
                return Ok(mentionedUserIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing mentions from content: {Content}", request.Content);
                return StatusCode(500, new { error = "Failed to parse mentions" });
            }
        }

        /// <summary>
        /// Process mentions and send notifications
        /// </summary>
        /// <param name="request">Mention processing request</param>
        /// <returns>Success status</returns>
        [HttpPost("process")]
        public async Task<IActionResult> ProcessMentions([FromBody] ProcessMentionsRequest request)
        {
            try
            {
                var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return Unauthorized();
                }

                await _mentionService.ProcessMentionsAsync(
                    request.Content,
                    currentUserId,
                    request.TaskId,
                    request.ProjectId,
                    request.Context);

                return Ok(new { success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing mentions: {Content}", request.Content);
                return StatusCode(500, new { error = "Failed to process mentions" });
            }
        }

        /// <summary>
        /// Format content with mention highlights
        /// </summary>
        /// <param name="request">Content to format</param>
        /// <returns>Formatted content with mention highlights</returns>
        [HttpPost("format")]
        public async Task<IActionResult> FormatMentions([FromBody] FormatMentionsRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.Content))
                {
                    return Ok(new { formattedContent = request.Content });
                }

                var formattedContent = await _mentionService.FormatMentionsAsync(request.Content);
                return Ok(new { formattedContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error formatting mentions: {Content}", request.Content);
                return StatusCode(500, new { error = "Failed to format mentions" });
            }
        }

        /// <summary>
        /// Check if a user can be mentioned in a given context
        /// </summary>
        /// <param name="userId">User ID to check</param>
        /// <param name="projectId">Optional project context</param>
        /// <returns>Whether the user can be mentioned</returns>
        [HttpGet("can-mention/{userId}")]
        public async Task<IActionResult> CanMentionUser(string userId, int? projectId = null)
        {
            try
            {
                var canMention = await _mentionService.CanMentionUserAsync(userId, projectId);
                return Ok(new { canMention });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {UserId} can be mentioned in project {ProjectId}", userId, projectId);
                return StatusCode(500, new { error = "Failed to check mention permission" });
            }
        }
    }

    // Request models
    public class ParseMentionsRequest
    {
        public string Content { get; set; } = string.Empty;
    }

    public class ProcessMentionsRequest
    {
        public string Content { get; set; } = string.Empty;
        public int? TaskId { get; set; }
        public int? ProjectId { get; set; }
        public string? Context { get; set; }
    }

    public class FormatMentionsRequest
    {
        public string Content { get; set; } = string.Empty;
    }
}
