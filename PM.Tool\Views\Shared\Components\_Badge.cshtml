@*
    Standardized Badge Component - Usage Examples:

    1. Status badge:
    @{
        ViewData["BadgeType"] = "status";
        ViewData["BadgeValue"] = "Active";
        ViewData["BadgeVariant"] = "success"; // success, warning, danger, info, neutral
    }
    <partial name="Components/_Badge" view-data="ViewData" />

    2. Priority badge:
    @{
        ViewData["BadgeType"] = "priority";
        ViewData["BadgeValue"] = "High";
        ViewData["BadgeVariant"] = "danger";
        ViewData["BadgeIcon"] = "fas fa-exclamation-triangle";
    }
    <partial name="Components/_Badge" view-data="ViewData" />

    3. Count badge:
    @{
        ViewData["BadgeType"] = "count";
        ViewData["BadgeValue"] = "5";
        ViewData["BadgeVariant"] = "primary";
        ViewData["BadgeSize"] = "sm"; // xs, sm, md, lg
    }
    <partial name="Components/_Badge" view-data="ViewData" />

    4. Custom badge:
    @{
        ViewData["BadgeType"] = "custom";
        ViewData["BadgeValue"] = "New Feature";
        ViewData["BadgeVariant"] = "info";
        ViewData["BadgeIcon"] = "fas fa-star";
        ViewData["BadgeTooltip"] = "This is a new feature";
    }
    <partial name="Components/_Badge" view-data="ViewData" />
*@

@model dynamic

@{
    var badgeType = ViewData["BadgeType"]?.ToString() ?? "status";
    var badgeValue = ViewData["BadgeValue"]?.ToString() ?? "";
    var badgeVariant = ViewData["BadgeVariant"]?.ToString() ?? "neutral";
    var badgeSize = ViewData["BadgeSize"]?.ToString() ?? "md";
    var badgeIcon = ViewData["BadgeIcon"]?.ToString();
    var badgeTooltip = ViewData["BadgeTooltip"]?.ToString();
    var badgeClickable = ViewData["BadgeClickable"] as bool? ?? false;
    var badgeOnClick = ViewData["BadgeOnClick"]?.ToString();
    var badgeHref = ViewData["BadgeHref"]?.ToString();
    var badgeId = ViewData["BadgeId"]?.ToString() ?? $"badge-{Guid.NewGuid().ToString("N")[..8]}";

    // Size classes
    var sizeClasses = badgeSize switch {
        "xs" => "text-xs px-2 py-0.5",
        "sm" => "text-xs px-2.5 py-1",
        "md" => "text-sm px-3 py-1.5",
        "lg" => "text-base px-4 py-2",
        _ => "text-sm px-3 py-1.5"
    };

    // Variant classes
    var (bgClass, textClass, borderClass) = badgeVariant switch {
        "success" => ("bg-success-100 dark:bg-success-900", "text-success-800 dark:text-success-200", "border-success-200 dark:border-success-700"),
        "warning" => ("bg-warning-100 dark:bg-warning-900", "text-warning-800 dark:text-warning-200", "border-warning-200 dark:border-warning-700"),
        "danger" => ("bg-danger-100 dark:bg-danger-900", "text-danger-800 dark:text-danger-200", "border-danger-200 dark:border-danger-700"),
        "info" => ("bg-primary-100 dark:bg-primary-900", "text-primary-800 dark:text-primary-200", "border-primary-200 dark:border-primary-700"),
        "primary" => ("bg-primary-600", "text-white", "border-primary-600"),
        "secondary" => ("bg-neutral-100 dark:bg-dark-700", "text-neutral-800 dark:text-dark-200", "border-neutral-200 dark:border-dark-600"),
        "neutral" => ("bg-neutral-100 dark:bg-dark-700", "text-neutral-800 dark:text-dark-200", "border-neutral-200 dark:border-dark-600"),
        _ => ("bg-neutral-100 dark:bg-dark-700", "text-neutral-800 dark:text-dark-200", "border-neutral-200 dark:border-dark-600")
    };

    // Type-specific styling
    var typeClasses = badgeType switch {
        "priority" => "font-bold rounded-full border shadow-sm",
        "status" => "font-medium rounded-lg border",
        "count" => "font-bold rounded-full",
        "custom" => "font-medium rounded-lg border",
        _ => "font-medium rounded-lg border"
    };

    // Interactive classes
    var interactiveClasses = badgeClickable || !string.IsNullOrEmpty(badgeOnClick) || !string.IsNullOrEmpty(badgeHref) 
        ? "cursor-pointer hover:opacity-80 transition-opacity duration-200" 
        : "";

    var finalClasses = $"inline-flex items-center {sizeClasses} {bgClass} {textClass} {borderClass} {typeClasses} {interactiveClasses}";

    // Tooltip attributes
    var tooltipAttrs = !string.IsNullOrEmpty(badgeTooltip) 
        ? $"data-tooltip=\"{badgeTooltip}\" title=\"{badgeTooltip}\"" 
        : "";
}

<!-- Standardized Badge -->
@if (!string.IsNullOrEmpty(badgeHref))
{
    <a id="@badgeId" 
       href="@badgeHref" 
       class="@finalClasses"
       @Html.Raw(tooltipAttrs)>
        @if (!string.IsNullOrEmpty(badgeIcon))
        {
            <i class="@badgeIcon @(!string.IsNullOrEmpty(badgeValue) ? "mr-1.5" : "") text-xs"></i>
        }
        @if (!string.IsNullOrEmpty(badgeValue))
        {
            <span>@badgeValue</span>
        }
    </a>
}
else if (!string.IsNullOrEmpty(badgeOnClick) || badgeClickable)
{
    <button id="@badgeId" 
            type="button"
            class="@finalClasses"
            @(!string.IsNullOrEmpty(badgeOnClick) ? $"onclick=\"{badgeOnClick}\"" : "")
            @Html.Raw(tooltipAttrs)>
        @if (!string.IsNullOrEmpty(badgeIcon))
        {
            <i class="@badgeIcon @(!string.IsNullOrEmpty(badgeValue) ? "mr-1.5" : "") text-xs"></i>
        }
        @if (!string.IsNullOrEmpty(badgeValue))
        {
            <span>@badgeValue</span>
        }
    </button>
}
else
{
    <span id="@badgeId" 
          class="@finalClasses"
          @Html.Raw(tooltipAttrs)>
        @if (!string.IsNullOrEmpty(badgeIcon))
        {
            <i class="@badgeIcon @(!string.IsNullOrEmpty(badgeValue) ? "mr-1.5" : "") text-xs"></i>
        }
        @if (!string.IsNullOrEmpty(badgeValue))
        {
            <span>@badgeValue</span>
        }
    </span>
}

<script>
    // Badge System for enhanced functionality
    window.BadgeSystem = window.BadgeSystem || {
        // Initialize tooltips for badges
        initializeTooltips: function() {
            document.querySelectorAll('[data-tooltip]').forEach(element => {
                if (!element.hasAttribute('data-tooltip-initialized')) {
                    element.setAttribute('data-tooltip-initialized', 'true');
                    
                    // Simple tooltip implementation
                    element.addEventListener('mouseenter', function(e) {
                        const tooltip = document.createElement('div');
                        tooltip.className = 'fixed z-50 px-2 py-1 text-xs text-white bg-black rounded shadow-lg pointer-events-none';
                        tooltip.textContent = e.target.getAttribute('data-tooltip');
                        tooltip.id = 'tooltip-' + Date.now();
                        
                        document.body.appendChild(tooltip);
                        
                        const rect = e.target.getBoundingClientRect();
                        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
                        
                        e.target.setAttribute('data-tooltip-id', tooltip.id);
                    });
                    
                    element.addEventListener('mouseleave', function(e) {
                        const tooltipId = e.target.getAttribute('data-tooltip-id');
                        if (tooltipId) {
                            const tooltip = document.getElementById(tooltipId);
                            if (tooltip) {
                                tooltip.remove();
                            }
                            e.target.removeAttribute('data-tooltip-id');
                        }
                    });
                }
            });
        },
        
        // Create badge programmatically
        create: function(options) {
            const {
                type = 'status',
                value = '',
                variant = 'neutral',
                size = 'md',
                icon = '',
                tooltip = '',
                clickable = false,
                onClick = null,
                href = null
            } = options;
            
            const badgeId = 'badge-' + Date.now();
            const badge = document.createElement(href ? 'a' : (onClick || clickable ? 'button' : 'span'));
            
            badge.id = badgeId;
            badge.className = this.getBadgeClasses(type, variant, size, clickable || onClick || href);
            
            if (href) {
                badge.href = href;
            }
            
            if (onClick) {
                badge.onclick = onClick;
            }
            
            if (tooltip) {
                badge.setAttribute('data-tooltip', tooltip);
                badge.title = tooltip;
            }
            
            let content = '';
            if (icon) {
                content += `<i class="${icon} ${value ? 'mr-1.5' : ''} text-xs"></i>`;
            }
            if (value) {
                content += `<span>${value}</span>`;
            }
            
            badge.innerHTML = content;
            
            return badge;
        },
        
        // Get badge classes based on configuration
        getBadgeClasses: function(type, variant, size, interactive) {
            const sizeClasses = {
                'xs': 'text-xs px-2 py-0.5',
                'sm': 'text-xs px-2.5 py-1',
                'md': 'text-sm px-3 py-1.5',
                'lg': 'text-base px-4 py-2'
            };
            
            const variantClasses = {
                'success': 'bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200 border-success-200 dark:border-success-700',
                'warning': 'bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200 border-warning-200 dark:border-warning-700',
                'danger': 'bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200 border-danger-200 dark:border-danger-700',
                'info': 'bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 border-primary-200 dark:border-primary-700',
                'primary': 'bg-primary-600 text-white border-primary-600',
                'neutral': 'bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200 border-neutral-200 dark:border-dark-600'
            };
            
            const typeClasses = {
                'priority': 'font-bold rounded-full border shadow-sm',
                'status': 'font-medium rounded-lg border',
                'count': 'font-bold rounded-full',
                'custom': 'font-medium rounded-lg border'
            };
            
            const interactiveClass = interactive ? 'cursor-pointer hover:opacity-80 transition-opacity duration-200' : '';
            
            return `inline-flex items-center ${sizeClasses[size] || sizeClasses.md} ${variantClasses[variant] || variantClasses.neutral} ${typeClasses[type] || typeClasses.status} ${interactiveClass}`;
        }
    };

    // Auto-initialize tooltips when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        window.BadgeSystem.initializeTooltips();
    });

    // Re-initialize tooltips when new content is added
    document.addEventListener('DOMNodeInserted', function() {
        setTimeout(() => window.BadgeSystem.initializeTooltips(), 100);
    });
</script>
