@model IEnumerable<PM.Tool.Core.Entities.Stakeholder>
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "Stakeholder Management";
    ViewData["PageTitle"] = "Stakeholder Management";
    ViewData["PageDescription"] = "Manage project stakeholders, track influence and interest levels, and maintain stakeholder communications.";
}

<!-- Standardized Page Header -->
@{
    ViewData["Title"] = "Stakeholder Management";
    ViewData["Description"] = "Manage project stakeholders, track influence and interest levels, and maintain stakeholder communications";
    ViewData["Icon"] = "fas fa-users";
    ViewData["IconColor"] = "text-primary-600 dark:text-primary-400";
    ViewData["Actions"] = new object[] {
        new { Text = "Add Stakeholder", Variant = "primary", Icon = "fas fa-plus", Href = Url.Action("Create"), OnClick = (string)null }
    };

    var statsArray = new List<object>
    {
        new { Label = "Total", Value = Model.Count().ToString(), Icon = "fas fa-users", Color = "blue" },
        new { Label = "Active", Value = Model.Count(s => s.IsActive).ToString(), Icon = "fas fa-check-circle", Color = "green" },
        new { Label = "High Influence", Value = Model.Count(s => s.Influence == PM.Tool.Core.Entities.InfluenceLevel.High || s.Influence == PM.Tool.Core.Entities.InfluenceLevel.VeryHigh).ToString(), Icon = "fas fa-star", Color = "amber" },
        new { Label = "Critical", Value = Model.Count(s => s.Priority == PM.Tool.Core.Entities.StakeholderPriority.Critical).ToString(), Icon = "fas fa-exclamation-triangle", Color = "red" }
    };

    ViewData["Stats"] = statsArray.ToArray();
}
<partial name="Components/_PageHeader" view-data="ViewData" />



<!-- Standardized Filter Bar -->
<div class="card-custom mb-8">
    <form id="filterForm" method="get" class="card-body-custom">
        <!-- Single Row Layout -->
        <div class="flex flex-wrap items-center gap-6">
            <!-- Quick Filters Section -->
            <div class="flex items-center gap-3">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-3">Quick:</span>
                <div class="flex items-center gap-2">
                    @{
                        ViewData["Text"] = "High Influence";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-star";
                        ViewData["OnClick"] = "filterStakeholders('highinfluence')";
                        ViewData["AdditionalClasses"] = "quick-filter-btn";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "Key Players";
                        ViewData["Icon"] = "fas fa-crown";
                        ViewData["OnClick"] = "filterStakeholders('keyplayers')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "Internal";
                        ViewData["Icon"] = "fas fa-building";
                        ViewData["OnClick"] = "filterStakeholders('internal')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "External";
                        ViewData["Icon"] = "fas fa-users";
                        ViewData["OnClick"] = "filterStakeholders('external')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>

            <!-- Main Filters Section -->
            <div class="flex items-center gap-4 flex-1">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text"
                           name="search"
                           placeholder="Search stakeholders..."
                           class="w-64 pl-11 pr-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 transition-all duration-200">
                </div>

                <!-- Filter Controls -->
                <div class="flex items-center gap-3">
                    <!-- Type Filter -->
                    <select name="type" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Types</option>
                        <option value="internal">Internal</option>
                        <option value="external">External</option>
                        <option value="customer">Customer</option>
                        <option value="vendor">Vendor</option>
                        <option value="regulatory">Regulatory</option>
                    </select>

                    <!-- Influence Filter -->
                    <select name="influence" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Influence</option>
                        <option value="veryhigh">Very High</option>
                        <option value="high">High</option>
                        <option value="medium">Medium</option>
                        <option value="low">Low</option>
                        <option value="verylow">Very Low</option>
                    </select>

                    <!-- Interest Filter -->
                    <select name="interest" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Interest</option>
                        <option value="veryhigh">Very High</option>
                        <option value="high">High</option>
                        <option value="medium">Medium</option>
                        <option value="low">Low</option>
                        <option value="verylow">Very Low</option>
                    </select>

                    <!-- Clear Filters Button -->
                    @{
                        ViewData["Text"] = "Clear";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-times";
                        ViewData["OnClick"] = "clearAllFilters()";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>
        </div>
    </form>
</div>



<!-- Stakeholder Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" id="stakeholderContainer">
    @if (Model != null && Model.Any())
    {
        @foreach (var stakeholder in Model)
        {
            <div class="stakeholder-card transition-all duration-200 hover:shadow-lg"
                 data-type="@stakeholder.Type.ToString().ToLower()"
                 data-influence="@stakeholder.Influence.ToString().ToLower()"
                 data-status="@(stakeholder.IsActive ? "active" : "inactive")"
                 data-search="@($"{stakeholder.Name} {stakeholder.Email} {stakeholder.Organization}".ToLower())">

                <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                    <!-- Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 @GetStakeholderTypeIconBg(stakeholder.Type) rounded-lg flex items-center justify-center">
                                <i class="@GetStakeholderTypeIcon(stakeholder.Type) text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-neutral-900 dark:text-white">@stakeholder.Name</h3>
                                <p class="text-sm text-neutral-600 dark:text-dark-400">@stakeholder.Title</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            @if (stakeholder.IsActive)
                            {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200">
                                    Active
                                </span>
                            }
                            else
                            {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200">
                                    Inactive
                                </span>
                            }
                        </div>
                    </div>

                    <!-- Details -->
                    <div class="space-y-3 mb-4">
                        <div class="flex items-center text-sm text-neutral-600 dark:text-dark-400">
                            <i class="fas fa-building w-4 mr-2"></i>
                            <span>@(stakeholder.Organization ?? "No organization")</span>
                        </div>
                        <div class="flex items-center text-sm text-neutral-600 dark:text-dark-400">
                            <i class="fas fa-envelope w-4 mr-2"></i>
                            <span>@stakeholder.Email</span>
                        </div>
                        @if (!string.IsNullOrEmpty(stakeholder.Phone))
                        {
                            <div class="flex items-center text-sm text-neutral-600 dark:text-dark-400">
                                <i class="fas fa-phone w-4 mr-2"></i>
                                <span>@stakeholder.Phone</span>
                            </div>
                        }
                    </div>

                    <!-- Influence & Interest Matrix -->
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <div class="text-center">
                            <div class="text-xs text-neutral-500 dark:text-dark-500 mb-1">Influence</div>
                            <span class="influence-badge @GetInfluenceBadgeClass(stakeholder.Influence) rounded-full">
                                @stakeholder.Influence
                            </span>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-neutral-500 dark:text-dark-500 mb-1">Interest</div>
                            <span class="influence-badge @GetInterestBadgeClass(stakeholder.Interest) rounded-full">
                                @stakeholder.Interest
                            </span>
                        </div>
                    </div>

                    <!-- Priority Badge -->
                    <div class="text-center mb-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @GetPriorityBadgeClass(stakeholder.Priority)">
                            @stakeholder.Priority Priority
                        </span>
                    </div>

                    <!-- Actions -->
                    <div class="flex justify-between items-center pt-4 border-t border-neutral-200 dark:border-dark-600">
                        <div class="flex space-x-2">
                            <a href="@Url.Action("Details", new { id = stakeholder.Id })"
                               class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="@Url.Action("Edit", new { id = stakeholder.Id })"
                               class="text-warning-600 hover:text-warning-700 dark:text-warning-400 dark:hover:text-warning-300">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                        <div class="text-xs text-neutral-500 dark:text-dark-500">
                            @stakeholder.Role
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-span-full">
            <div class="card-custom">
                <div class="card-body-custom">
                    <div class="text-center py-12">
                        <div class="w-24 h-24 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-users text-4xl text-neutral-400 dark:text-dark-500"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-2">No stakeholders found</h3>
                        <p class="text-neutral-600 dark:text-dark-400 mb-8 max-w-md mx-auto">
                            Get started by adding your first stakeholder to track influence, interest levels, and maintain stakeholder communications.
                        </p>
                        @{
                            ViewData["Text"] = "Add First Stakeholder";
                            ViewData["Variant"] = "primary";
                            ViewData["Icon"] = "fas fa-plus";
                            ViewData["Href"] = Url.Action("Create");
                            ViewData["Size"] = "lg";
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            initializeStakeholderFilters();
        });

        function initializeStakeholderFilters() {
            const searchInput = $('input[name="searchInput"]');
            const typeFilter = $('select[name="typeFilter"]');
            const influenceFilter = $('select[name="influenceFilter"]');
            const statusFilter = $('select[name="statusFilter"]');

            function filterStakeholders() {
                const searchTerm = searchInput.val().toLowerCase();
                const typeValue = typeFilter.val();
                const influenceValue = influenceFilter.val();
                const statusValue = statusFilter.val();

                $('.stakeholder-card').each(function() {
                    const card = $(this);
                    const searchData = card.data('search') || '';
                    const typeData = card.data('type') || '';
                    const influenceData = card.data('influence') || '';
                    const statusData = card.data('status') || '';

                    const matchesSearch = searchData.includes(searchTerm);
                    const matchesType = !typeValue || typeData === typeValue;
                    const matchesInfluence = !influenceValue || influenceData === influenceValue;
                    const matchesStatus = !statusValue || statusData === statusValue;

                    if (matchesSearch && matchesType && matchesInfluence && matchesStatus) {
                        card.show();
                    } else {
                        card.hide();
                    }
                });

                // Show/hide "no results" message
                const visibleCards = $('.stakeholder-card:visible').length;
                let noResultsMessage = $('#noResultsMessage');

                if (visibleCards === 0 && $('.stakeholder-card').length > 0) {
                    if (noResultsMessage.length === 0) {
                        noResultsMessage = $(`
                            <div id="noResultsMessage" class="col-span-full">
                                <div class="card-custom">
                                    <div class="card-body-custom">
                                        <div class="text-center py-8">
                                            <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                                <i class="fas fa-search text-2xl text-neutral-400 dark:text-dark-500"></i>
                                            </div>
                                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No stakeholders match your filters</h3>
                                            <p class="text-neutral-600 dark:text-dark-400 mb-4">Try adjusting your search criteria or clearing some filters.</p>
                                            <button onclick="clearAllFilters()" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                                                <i class="fas fa-times mr-1"></i>Clear all filters
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `);
                        $('#stakeholderContainer').append(noResultsMessage);
                    }
                    noResultsMessage.show();
                } else {
                    noResultsMessage.hide();
                }
            }

            window.clearAllFilters = function() {
                searchInput.val('');
                typeFilter.val('');
                influenceFilter.val('');
                statusFilter.val('');
                filterStakeholders();
            }

            searchInput.on('input', filterStakeholders);
            typeFilter.on('change', filterStakeholders);
            influenceFilter.on('change', filterStakeholders);
            statusFilter.on('change', filterStakeholders);
        }
    </script>
}

@functions {
    private string GetStakeholderTypeIcon(StakeholderType type)
    {
        return type switch
        {
            StakeholderType.Internal => "fas fa-user-tie",
            StakeholderType.External => "fas fa-user",
            StakeholderType.Customer => "fas fa-handshake",
            StakeholderType.Vendor => "fas fa-truck",
            StakeholderType.Regulatory => "fas fa-gavel",
            StakeholderType.Partner => "fas fa-users",
            _ => "fas fa-user"
        };
    }

    private string GetStakeholderTypeIconBg(StakeholderType type)
    {
        return type switch
        {
            StakeholderType.Internal => "bg-blue-500",
            StakeholderType.External => "bg-green-500",
            StakeholderType.Customer => "bg-purple-500",
            StakeholderType.Vendor => "bg-orange-500",
            StakeholderType.Regulatory => "bg-red-500",
            StakeholderType.Partner => "bg-indigo-500",
            _ => "bg-neutral-500"
        };
    }

    private string GetInfluenceBadgeClass(InfluenceLevel influence)
    {
        return influence switch
        {
            InfluenceLevel.VeryLow => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200",
            InfluenceLevel.Low => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            InfluenceLevel.Medium => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            InfluenceLevel.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            InfluenceLevel.VeryHigh => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }

    private string GetInterestBadgeClass(InterestLevel interest)
    {
        return interest switch
        {
            InterestLevel.VeryLow => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200",
            InterestLevel.Low => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            InterestLevel.Medium => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            InterestLevel.High => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            InterestLevel.VeryHigh => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }

    private string GetPriorityBadgeClass(StakeholderPriority priority)
    {
        return priority switch
        {
            StakeholderPriority.Low => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200",
            StakeholderPriority.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            StakeholderPriority.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            StakeholderPriority.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }
}
