@*
    Standardized Alert Component - Usage Examples:

    1. Basic alert:
    @{
        ViewData["AlertType"] = "success"; // success, warning, danger, info
        ViewData["AlertTitle"] = "Success!";
        ViewData["AlertMessage"] = "Your changes have been saved successfully.";
        ViewData["Dismissible"] = true;
    }
    <partial name="Components/_Alert" view-data="ViewData" />

    2. Alert with custom action:
    @{
        ViewData["AlertType"] = "warning";
        ViewData["AlertTitle"] = "Warning";
        ViewData["AlertMessage"] = "This action cannot be undone.";
        ViewData["ActionText"] = "Proceed Anyway";
        ViewData["ActionOnClick"] = "confirmAction()";
        ViewData["Dismissible"] = true;
    }
    <partial name="Components/_Alert" view-data="ViewData" />

    3. Inline alert (no background):
    @{
        ViewData["AlertType"] = "info";
        ViewData["AlertMessage"] = "Additional information about this field.";
        ViewData["Inline"] = true;
    }
    <partial name="Components/_Alert" view-data="ViewData" />
*@

@model dynamic

@{
    var alertType = ViewData["AlertType"]?.ToString() ?? "info";
    var title = ViewData["AlertTitle"]?.ToString();
    var message = ViewData["AlertMessage"]?.ToString() ?? "";
    var dismissible = ViewData["Dismissible"] as bool? ?? false;
    var inline = ViewData["Inline"] as bool? ?? false;
    var actionText = ViewData["ActionText"]?.ToString();
    var actionOnClick = ViewData["ActionOnClick"]?.ToString();
    var actionHref = ViewData["ActionHref"]?.ToString();
    var alertId = ViewData["AlertId"]?.ToString() ?? $"alert-{Guid.NewGuid().ToString("N")[..8]}";

    var (bgClass, borderClass, textClass, iconClass, icon) = alertType switch {
        "success" => ("bg-success-50 dark:bg-success-900", "border-success-200 dark:border-success-700", "text-success-800 dark:text-success-200", "text-success-600 dark:text-success-400", "fas fa-check-circle"),
        "warning" => ("bg-warning-50 dark:bg-warning-900", "border-warning-200 dark:border-warning-700", "text-warning-800 dark:text-warning-200", "text-warning-600 dark:text-warning-400", "fas fa-exclamation-triangle"),
        "danger" => ("bg-danger-50 dark:bg-danger-900", "border-danger-200 dark:border-danger-700", "text-danger-800 dark:text-danger-200", "text-danger-600 dark:text-danger-400", "fas fa-exclamation-circle"),
        "info" => ("bg-primary-50 dark:bg-primary-900", "border-primary-200 dark:border-primary-700", "text-primary-800 dark:text-primary-200", "text-primary-600 dark:text-primary-400", "fas fa-info-circle"),
        _ => ("bg-neutral-50 dark:bg-neutral-900", "border-neutral-200 dark:border-neutral-700", "text-neutral-800 dark:text-neutral-200", "text-neutral-600 dark:text-neutral-400", "fas fa-info-circle")
    };

    var containerClasses = inline 
        ? $"flex items-start space-x-3 {textClass}" 
        : $"rounded-lg border {bgClass} {borderClass} p-4";
}

<!-- Standardized Alert -->
<div id="@alertId" class="@containerClasses" role="alert">
    @if (!inline)
    {
        <div class="flex items-start">
            <!-- Icon -->
            <div class="flex-shrink-0">
                <i class="@icon @iconClass text-lg"></i>
            </div>
            
            <!-- Content -->
            <div class="ml-3 flex-1">
                @if (!string.IsNullOrEmpty(title))
                {
                    <h3 class="text-sm font-medium @textClass mb-1">
                        @title
                    </h3>
                }
                
                @if (!string.IsNullOrEmpty(message))
                {
                    <div class="text-sm @textClass @(!string.IsNullOrEmpty(title) ? "mt-1" : "")">
                        @Html.Raw(message)
                    </div>
                }
                
                @if (!string.IsNullOrEmpty(actionText))
                {
                    <div class="mt-3">
                        @if (!string.IsNullOrEmpty(actionHref))
                        {
                            <a href="@actionHref" 
                               class="text-sm font-medium @textClass underline hover:no-underline">
                                @actionText
                            </a>
                        }
                        else if (!string.IsNullOrEmpty(actionOnClick))
                        {
                            <button type="button" 
                                    onclick="@actionOnClick"
                                    class="text-sm font-medium @textClass underline hover:no-underline bg-transparent border-none p-0 cursor-pointer">
                                @actionText
                            </button>
                        }
                    </div>
                }
            </div>
            
            <!-- Dismiss Button -->
            @if (dismissible)
            {
                <div class="ml-auto pl-3">
                    <button type="button" 
                            onclick="dismissAlert('@alertId')"
                            class="inline-flex rounded-md @textClass hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-current"
                            aria-label="Dismiss alert">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            }
        </div>
    }
    else
    {
        <!-- Inline Alert (simplified) -->
        <div class="flex-shrink-0">
            <i class="@icon @iconClass text-sm"></i>
        </div>
        <div class="flex-1">
            @if (!string.IsNullOrEmpty(title))
            {
                <span class="font-medium">@title</span>
            }
            @if (!string.IsNullOrEmpty(message))
            {
                <span class="@(!string.IsNullOrEmpty(title) ? "ml-1" : "")">@Html.Raw(message)</span>
            }
        </div>
        @if (dismissible)
        {
            <button type="button" 
                    onclick="dismissAlert('@alertId')"
                    class="ml-2 @textClass hover:opacity-75"
                    aria-label="Dismiss alert">
                <i class="fas fa-times text-xs"></i>
            </button>
        }
    }
</div>

<script>
    // Standardized Alert System
    window.AlertSystem = window.AlertSystem || {
        dismiss: function(alertId) {
            const alert = document.getElementById(alertId);
            if (alert) {
                // Add fade out animation
                alert.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-10px)';
                
                setTimeout(() => {
                    alert.remove();
                }, 300);
                
                // Dispatch custom event
                alert.dispatchEvent(new CustomEvent('alert:dismissed', { detail: { alertId } }));
            }
        },
        
        show: function(options) {
            const {
                type = 'info',
                title = '',
                message = '',
                dismissible = true,
                duration = 0, // 0 = no auto-dismiss
                container = document.body
            } = options;
            
            const alertId = `alert-${Date.now()}`;
            
            // Create alert HTML
            const alertHtml = `
                <div id="${alertId}" class="rounded-lg border bg-${type}-50 dark:bg-${type}-900 border-${type}-200 dark:border-${type}-700 p-4 mb-4" role="alert" style="opacity: 0; transform: translateY(-10px);">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} text-${type}-600 dark:text-${type}-400 text-lg"></i>
                        </div>
                        <div class="ml-3 flex-1">
                            ${title ? `<h3 class="text-sm font-medium text-${type}-800 dark:text-${type}-200 mb-1">${title}</h3>` : ''}
                            ${message ? `<div class="text-sm text-${type}-800 dark:text-${type}-200">${message}</div>` : ''}
                        </div>
                        ${dismissible ? `
                            <div class="ml-auto pl-3">
                                <button type="button" onclick="dismissAlert('${alertId}')" class="inline-flex rounded-md text-${type}-800 dark:text-${type}-200 hover:opacity-75" aria-label="Dismiss alert">
                                    <i class="fas fa-times text-sm"></i>
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            
            // Insert alert
            container.insertAdjacentHTML('afterbegin', alertHtml);
            
            // Animate in
            const alertElement = document.getElementById(alertId);
            if (alertElement) {
                requestAnimationFrame(() => {
                    alertElement.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                    alertElement.style.opacity = '1';
                    alertElement.style.transform = 'translateY(0)';
                });
                
                // Auto-dismiss if duration is set
                if (duration > 0) {
                    setTimeout(() => this.dismiss(alertId), duration);
                }
            }
            
            return alertId;
        }
    };

    // Global function for backward compatibility
    function dismissAlert(alertId) {
        window.AlertSystem.dismiss(alertId);
    }

    // Helper functions for common alert types
    function showSuccessAlert(message, title = 'Success!', duration = 5000) {
        return window.AlertSystem.show({ type: 'success', title, message, duration });
    }

    function showWarningAlert(message, title = 'Warning', duration = 0) {
        return window.AlertSystem.show({ type: 'warning', title, message, duration });
    }

    function showErrorAlert(message, title = 'Error', duration = 0) {
        return window.AlertSystem.show({ type: 'danger', title, message, duration });
    }

    function showInfoAlert(message, title = 'Information', duration = 3000) {
        return window.AlertSystem.show({ type: 'info', title, message, duration });
    }
</script>
