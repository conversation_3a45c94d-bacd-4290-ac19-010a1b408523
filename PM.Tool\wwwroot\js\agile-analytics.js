// PM.Tool - Advanced Agile Analytics JavaScript
// Comprehensive analytics charts and data visualization

class AgileAnalytics {
    constructor() {
        this.charts = {};
        this.projectId = null;
        this.isDarkMode = document.documentElement.classList.contains('dark');
        this.colors = this.getColorScheme();
    }

    getColorScheme() {
        return {
            primary: this.isDarkMode ? '#60A5FA' : '#3B82F6',
            secondary: this.isDarkMode ? '#A78BFA' : '#8B5CF6',
            success: this.isDarkMode ? '#34D399' : '#10B981',
            warning: this.isDarkMode ? '#FBBF24' : '#F59E0B',
            danger: this.isDarkMode ? '#F87171' : '#EF4444',
            info: this.isDarkMode ? '#38BDF8' : '#0EA5E9',
            background: this.isDarkMode ? '#1F2937' : '#FFFFFF',
            text: this.isDarkMode ? '#F9FAFB' : '#111827',
            grid: this.isDarkMode ? '#374151' : '#E5E7EB'
        };
    }

    // Initialize analytics for a project
    init(projectId) {
        this.projectId = projectId;
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.analytics-tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Refresh button
        const refreshBtn = document.querySelector('[onclick="refreshAnalytics()"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshAllData());
        }
    }

    switchTab(tabName) {
        // Update active tab
        document.querySelectorAll('.analytics-tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Show/hide content
        document.querySelectorAll('.analytics-tab-content').forEach(content => {
            content.classList.add('hidden');
        });
        document.getElementById(`${tabName}-tab`).classList.remove('hidden');

        // Load tab-specific data
        this.loadTabData(tabName);
    }

    async loadTabData(tabName) {
        try {
            switch(tabName) {
                case 'overview':
                    await this.loadOverviewData();
                    break;
                case 'velocity':
                    await this.loadVelocityData();
                    break;
                case 'burndown':
                    await this.loadBurndownData();
                    break;
                case 'team':
                    await this.loadTeamData();
                    break;
                case 'quality':
                    await this.loadQualityData();
                    break;
            }
        } catch (error) {
            console.error(`Error loading ${tabName} data:`, error);
            this.showError(`Failed to load ${tabName} data`);
        }
    }

    async loadOverviewData() {
        // Epic progress chart is already loaded from server data
        // Additional real-time updates can be added here
    }

    async loadVelocityData() {
        try {
            const response = await fetch(`/Agile/GetVelocityData?projectId=${this.projectId}`);
            const velocityData = await response.json();
            
            this.createVelocityChart(velocityData);
            this.populateVelocityStats(velocityData);
            this.populateVelocityTable(velocityData);
        } catch (error) {
            console.error('Error loading velocity data:', error);
        }
    }

    async loadBurndownData() {
        // Load current sprint burndown data
        try {
            const response = await fetch(`/Agile/GetCurrentSprintBurndown?projectId=${this.projectId}`);
            const burndownData = await response.json();
            
            this.createBurndownChart(burndownData);
            this.createBurnupChart(burndownData);
        } catch (error) {
            console.error('Error loading burndown data:', error);
        }
    }

    async loadTeamData() {
        try {
            const response = await fetch(`/Agile/GetTeamPerformanceData?projectId=${this.projectId}`);
            const teamData = await response.json();
            
            this.createTeamProductivityChart(teamData);
            this.populateIndividualPerformance(teamData);
        } catch (error) {
            console.error('Error loading team data:', error);
        }
    }

    async loadQualityData() {
        try {
            const response = await fetch(`/Agile/GetQualityData?projectId=${this.projectId}`);
            const qualityData = await response.json();
            
            this.createDefectChart(qualityData);
            this.populateQualityMetrics(qualityData);
        } catch (error) {
            console.error('Error loading quality data:', error);
        }
    }

    createVelocityChart(data) {
        const ctx = document.getElementById('velocity-chart');
        if (!ctx) return;

        if (this.charts.velocity) {
            this.charts.velocity.destroy();
        }

        this.charts.velocity = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(d => d.SprintName || `Sprint ${d.SprintNumber}`),
                datasets: [{
                    label: 'Completed Story Points',
                    data: data.map(d => d.CompletedStoryPoints),
                    borderColor: this.colors.primary,
                    backgroundColor: this.colors.primary + '20',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Planned Story Points',
                    data: data.map(d => d.PlannedStoryPoints),
                    borderColor: this.colors.secondary,
                    backgroundColor: this.colors.secondary + '20',
                    tension: 0.4,
                    borderDash: [5, 5]
                }]
            },
            options: this.getChartOptions('Velocity Trend', 'Story Points')
        });
    }

    createTeamProductivityChart(data) {
        const ctx = document.getElementById('team-productivity-chart');
        if (!ctx) return;

        if (this.charts.teamProductivity) {
            this.charts.teamProductivity.destroy();
        }

        const memberData = data.MemberPerformance || [];
        
        this.charts.teamProductivity = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: memberData.map(m => m.MemberName),
                datasets: [{
                    label: 'Completed Tasks',
                    data: memberData.map(m => m.CompletedTasks),
                    backgroundColor: this.colors.success,
                }, {
                    label: 'Total Tasks',
                    data: memberData.map(m => m.TotalTasks),
                    backgroundColor: this.colors.info,
                }]
            },
            options: this.getChartOptions('Team Productivity', 'Tasks')
        });
    }

    createDefectChart(data) {
        const ctx = document.getElementById('defect-chart');
        if (!ctx) return;

        if (this.charts.defect) {
            this.charts.defect.destroy();
        }

        this.charts.defect = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Quality Stories', 'Stories with Defects'],
                datasets: [{
                    data: [
                        data.TotalStories - data.StoriesWithDefects,
                        data.StoriesWithDefects
                    ],
                    backgroundColor: [this.colors.success, this.colors.danger]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { color: this.colors.text }
                    },
                    title: {
                        display: true,
                        text: 'Quality Distribution',
                        color: this.colors.text
                    }
                }
            }
        });
    }

    populateVelocityStats(data) {
        const statsContainer = document.getElementById('velocity-stats');
        if (!statsContainer || !data.length) return;

        const velocities = data.map(d => d.CompletedStoryPoints);
        const average = velocities.reduce((a, b) => a + b, 0) / velocities.length;
        const max = Math.max(...velocities);
        const min = Math.min(...velocities);

        statsContainer.innerHTML = `
            <div class="stat-item">
                <span class="stat-label">Average Velocity:</span>
                <span class="stat-value">${average.toFixed(1)} points</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Highest Velocity:</span>
                <span class="stat-value">${max} points</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Lowest Velocity:</span>
                <span class="stat-value">${min} points</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Velocity Trend:</span>
                <span class="stat-value ${this.getTrendClass(velocities)}">${this.getTrendText(velocities)}</span>
            </div>
        `;
    }

    populateVelocityTable(data) {
        const tableBody = document.getElementById('velocity-table-body');
        if (!tableBody) return;

        tableBody.innerHTML = data.map(sprint => `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-900 dark:text-dark-100">
                    ${sprint.SprintName || `Sprint ${sprint.SprintNumber}`}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-900 dark:text-dark-100">
                    ${sprint.PlannedStoryPoints}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-900 dark:text-dark-100">
                    ${sprint.CompletedStoryPoints}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-900 dark:text-dark-100">
                    ${sprint.CompletedStoryPoints}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                    <span class="px-2 py-1 text-xs rounded-full ${this.getAccuracyClass(sprint.PlannedStoryPoints, sprint.CompletedStoryPoints)}">
                        ${this.calculateAccuracy(sprint.PlannedStoryPoints, sprint.CompletedStoryPoints)}%
                    </span>
                </td>
            </tr>
        `).join('');
    }

    getChartOptions(title, yAxisLabel) {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: { color: this.colors.text }
                },
                title: {
                    display: true,
                    text: title,
                    color: this.colors.text
                }
            },
            scales: {
                x: {
                    ticks: { color: this.colors.text },
                    grid: { color: this.colors.grid }
                },
                y: {
                    ticks: { color: this.colors.text },
                    grid: { color: this.colors.grid },
                    title: {
                        display: true,
                        text: yAxisLabel,
                        color: this.colors.text
                    }
                }
            }
        };
    }

    getTrendClass(velocities) {
        if (velocities.length < 2) return 'text-neutral-500';
        const trend = velocities[velocities.length - 1] - velocities[0];
        return trend > 0 ? 'text-green-600' : trend < 0 ? 'text-red-600' : 'text-neutral-500';
    }

    getTrendText(velocities) {
        if (velocities.length < 2) return 'Insufficient data';
        const trend = velocities[velocities.length - 1] - velocities[0];
        return trend > 0 ? '↗ Improving' : trend < 0 ? '↘ Declining' : '→ Stable';
    }

    calculateAccuracy(planned, completed) {
        if (planned === 0) return 100;
        return Math.round((completed / planned) * 100);
    }

    getAccuracyClass(planned, completed) {
        const accuracy = this.calculateAccuracy(planned, completed);
        if (accuracy >= 90) return 'bg-green-100 text-green-800';
        if (accuracy >= 70) return 'bg-yellow-100 text-yellow-800';
        return 'bg-red-100 text-red-800';
    }

    showError(message) {
        // Show error notification
        console.error(message);
    }

    async refreshAllData() {
        const activeTab = document.querySelector('.analytics-tab-btn.active')?.dataset.tab || 'overview';
        await this.loadTabData(activeTab);
    }
}

// Initialize analytics when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (window.agileAnalytics) {
        const projectId = document.querySelector('[data-project-id]')?.dataset.projectId;
        if (projectId) {
            window.agileAnalytics.init(parseInt(projectId));
        }
    }
});

// Global instance
window.agileAnalytics = new AgileAnalytics();
