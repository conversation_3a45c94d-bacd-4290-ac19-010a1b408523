@*
    Standardized DataTable Component - Usage Examples:

    1. Basic table:
    @{
        ViewData["Title"] = "Projects";
        ViewData["Icon"] = "fas fa-project-diagram";
        ViewData["Headers"] = new[] { "Name", "Status", "Progress", "Actions" };
        ViewData["Data"] = Model; // IEnumerable of data
        ViewData["EmptyMessage"] = "No projects found";
        ViewData["EmptyIcon"] = "fas fa-project-diagram";
    }
    <partial name="Components/_DataTable" view-data="ViewData" />

    2. Table with search, filters, and export:
    @{
        ViewData["Title"] = "Projects";
        ViewData["ShowSearch"] = true;
        ViewData["ShowFilters"] = true;
        ViewData["ShowExport"] = true;
        ViewData["FilterOptions"] = new[] { "Active", "Completed", "On Hold" };
        ViewData["ExportFormats"] = new[] { "Excel", "PDF", "CSV" };
    }
    <partial name="Components/_DataTable" view-data="ViewData" />

    3. Sortable table with pagination:
    @{
        ViewData["Title"] = "Tasks";
        ViewData["Sortable"] = true;
        ViewData["ShowPagination"] = true;
        ViewData["PageSize"] = 25;
        ViewData["SortableColumns"] = new[] { "Name", "Status", "Priority", "DueDate" };
    }
    <partial name="Components/_DataTable" view-data="ViewData" />

    4. Responsive table with custom row template:
    @{
        ViewData["Title"] = "Resources";
        ViewData["Responsive"] = true;
        ViewData["RowTemplate"] = "_ResourceTableRow"; // Partial view name
        ViewData["ShowBulkActions"] = true;
        ViewData["BulkActions"] = new[] { "Delete", "Archive", "Export" };
    }
    <partial name="Components/_DataTable" view-data="ViewData" />
*@

@{
    var title = ViewData["Title"]?.ToString() ?? "Data Table";
    var icon = ViewData["Icon"]?.ToString() ?? "fas fa-table";
    var subtitle = ViewData["Subtitle"]?.ToString();
    var showSearch = ViewData["ShowSearch"] as bool? ?? false;
    var showFilters = ViewData["ShowFilters"] as bool? ?? false;
    var showExport = ViewData["ShowExport"] as bool? ?? false;
    var showPagination = ViewData["ShowPagination"] as bool? ?? true;
    var headers = ViewData["Headers"] as string[] ?? new string[0];
    var data = ViewData["Data"] as IEnumerable<object>;
    var emptyMessage = ViewData["EmptyMessage"]?.ToString() ?? "No data available";
    var emptyIcon = ViewData["EmptyIcon"]?.ToString() ?? "fas fa-inbox";
    var emptyDescription = ViewData["EmptyDescription"]?.ToString() ?? "Get started by adding your first item.";
    var createButtonText = ViewData["CreateButtonText"]?.ToString();
    var createButtonHref = ViewData["CreateButtonHref"]?.ToString();
    var filterOptions = ViewData["FilterOptions"] as string[] ?? new string[0];
    var tableId = ViewData["TableId"]?.ToString() ?? "dataTable";
    var rowTemplate = ViewData["RowTemplate"]?.ToString(); // Custom row template
    var hasData = data?.Any() == true;
}

<div class="card-custom">
    <!-- Table Header -->
    <div class="card-header-custom">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- Title Section -->
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="@icon text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@title</h3>
                    @if (!string.IsNullOrEmpty(subtitle))
                    {
                        <p class="text-sm text-neutral-500 dark:text-dark-400">@subtitle</p>
                    }
                </div>
            </div>

            <!-- Controls Section -->
            <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                <!-- Search -->
                @if (showSearch)
                {
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-neutral-400 dark:text-dark-500"></i>
                        </div>
                        <input type="text"
                               id="@(tableId)Search"
                               class="form-input-custom pl-10 w-full sm:w-64"
                               placeholder="Search...">
                    </div>
                }

                <!-- Filters -->
                @if (showFilters && filterOptions.Any())
                {
                    <div class="relative">
                        <select id="@(tableId)Filter" class="form-select-custom w-full sm:w-auto">
                            <option value="">All Items</option>
                            @foreach (var option in filterOptions)
                            {
                                <option value="@option">@option</option>
                            }
                        </select>
                    </div>
                }

                <!-- Export Button -->
                @if (showExport)
                {
                    ViewData["Text"] = "Export";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-download";
                    ViewData["Size"] = "sm";
                    ViewData["OnClick"] = $"exportTable('{tableId}')";
                    <partial name="Components/_Button" view-data="ViewData" />
                }
            </div>
        </div>
    </div>

    <!-- Table Content -->
    <div class="card-body-custom p-0">
        @if (hasData)
        {
            <div class="overflow-x-auto">
                <table class="table-custom" id="@tableId">
                    @if (headers.Any())
                    {
                        <thead class="table-header-custom">
                            <tr>
                                @foreach (var header in headers)
                                {
                                    <th class="table-header-cell-custom">
                                        <div class="flex items-center space-x-2">
                                            <span>@header</span>
                                            <div class="flex flex-col">
                                                <i class="fas fa-caret-up text-xs text-neutral-300 dark:text-dark-600 hover:text-neutral-500 dark:hover:text-dark-400 cursor-pointer sort-asc" data-column="@header.ToLower()"></i>
                                                <i class="fas fa-caret-down text-xs text-neutral-300 dark:text-dark-600 hover:text-neutral-500 dark:hover:text-dark-400 cursor-pointer sort-desc" data-column="@header.ToLower()"></i>
                                            </div>
                                        </div>
                                    </th>
                                }
                            </tr>
                        </thead>
                    }
                    <tbody>
                        @if (!string.IsNullOrEmpty(rowTemplate))
                        {
                            @Html.Raw(rowTemplate)
                        }
                        else
                        {
                            <!-- Default row rendering - will be customized per implementation -->
                            @if (data != null)
                            {
                                @foreach (var item in data)
                                {
                                    <tr class="hover:bg-neutral-50 dark:hover:bg-dark-800 transition-colors">
                                        <td class="table-cell-custom">
                                            <!-- Custom content will be injected here -->
                                            @item?.ToString()
                                        </td>
                                    </tr>
                                }
                            }
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if (showPagination)
            {
                <div class="px-6 py-4 border-t border-neutral-200 dark:border-dark-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-neutral-500 dark:text-dark-400">
                            Showing <span class="font-medium">1</span> to <span class="font-medium">10</span> of <span class="font-medium">@(data?.Count() ?? 0)</span> results
                        </div>
                        <div class="flex space-x-2">
                            <button class="btn-secondary-custom px-3 py-1 text-sm" disabled>
                                <i class="fas fa-chevron-left mr-1"></i>
                                Previous
                            </button>
                            <button class="btn-primary-custom px-3 py-1 text-sm">
                                Next
                                <i class="fas fa-chevron-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="@emptyIcon text-4xl text-neutral-400 dark:text-dark-500"></i>
                </div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">@emptyMessage</h3>
                <p class="text-neutral-500 dark:text-dark-400 mb-8 max-w-md mx-auto">@emptyDescription</p>

                @if (!string.IsNullOrEmpty(createButtonText) && !string.IsNullOrEmpty(createButtonHref))
                {
                    ViewData["Text"] = createButtonText;
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Href"] = createButtonHref;
                    <partial name="Components/_Button" view-data="ViewData" />
                }
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            var tableId = '@tableId';

            // Search functionality
            $('#' + tableId + 'Search').on('keyup', function() {
                var value = $(this).val().toLowerCase();
                $('#' + tableId + ' tbody tr').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });

            // Filter functionality
            $('#' + tableId + 'Filter').on('change', function() {
                var value = $(this).val().toLowerCase();
                $('#' + tableId + ' tbody tr').filter(function() {
                    if (value === '') {
                        $(this).show();
                    } else {
                        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                    }
                });
            });

            // Sort functionality
            $('.sort-asc, .sort-desc').on('click', function() {
                var column = $(this).data('column');
                var direction = $(this).hasClass('sort-asc') ? 'asc' : 'desc';
                sortTable(tableId, column, direction);
            });
        });

        function sortTable(tableId, column, direction) {
            // Basic sorting implementation
            var table = document.getElementById(tableId);
            var tbody = table.querySelector('tbody');
            var rows = Array.from(tbody.querySelectorAll('tr'));

            rows.sort(function(a, b) {
                var aText = a.textContent.trim();
                var bText = b.textContent.trim();

                if (direction === 'asc') {
                    return aText.localeCompare(bText);
                } else {
                    return bText.localeCompare(aText);
                }
            });

            rows.forEach(function(row) {
                tbody.appendChild(row);
            });
        }

        function exportTable(tableId) {
            // Basic CSV export functionality
            var table = document.getElementById(tableId);
            var csv = [];
            var rows = table.querySelectorAll('tr');

            for (var i = 0; i < rows.length; i++) {
                var row = [], cols = rows[i].querySelectorAll('td, th');
                for (var j = 0; j < cols.length; j++) {
                    row.push(cols[j].innerText);
                }
                csv.push(row.join(','));
            }

            var csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
            var downloadLink = document.createElement('a');
            downloadLink.download = tableId + '_export.csv';
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    </script>
}
