@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus
@model IEnumerable<PM.Tool.Core.Entities.TaskEntity>
@{
    ViewData["Title"] = "Work Breakdown Structure";
    var projectId = ViewBag.ProjectId;
    var projectName = ViewBag.ProjectName ?? "Unknown Project";
    var tasks = Model?.ToList() ?? new List<PM.Tool.Core.Entities.TaskEntity>();

    // Standardized Page Header Configuration
    ViewData["Description"] = $"{projectName} - Organize and manage your project tasks in a hierarchical structure";
    ViewData["Icon"] = "fas fa-sitemap";
    ViewData["IconColor"] = "text-primary-600 dark:text-primary-400";
    ViewData["Actions"] = new object[] {
        new { Text = "Add Task", Variant = "primary", Icon = "fas fa-plus", Href = Url.Action("Create", "Tasks", new { projectId = projectId }) },
        new { Text = "Generate Codes", Variant = "outline-secondary", Icon = "fas fa-code", OnClick = "showGenerateCodesModal()", Href = "" },
        new { Text = "Export", Variant = "outline-primary", Icon = "fas fa-download", OnClick = "showExportModal()", Href = "" },
        new { Text = "Back to Project", Variant = "secondary", Icon = "fas fa-arrow-left", Href = Url.Action("Details", "Projects", new { id = projectId }) }
    };
    ViewData["Stats"] = new object[] {
        new { Label = "Total Tasks", Value = tasks.Count.ToString(), Icon = "fas fa-tasks", Color = "blue" },
        new { Label = "Completed", Value = tasks.Count(t => t.Status == TaskStatus.Done).ToString(), Icon = "fas fa-check-circle", Color = "green" },
        new { Label = "In Progress", Value = tasks.Count(t => t.Status == TaskStatus.InProgress).ToString(), Icon = "fas fa-play-circle", Color = "amber" },
        new { Label = "To Do", Value = tasks.Count(t => t.Status == TaskStatus.ToDo).ToString(), Icon = "fas fa-circle", Color = "gray" }
    };
}

<!-- Standardized Page Header -->
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Filter and Search Controls -->
<div class="card-custom mb-6">
    <div class="card-body-custom">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <!-- Search and Filters -->
            <div class="flex flex-col sm:flex-row sm:items-center gap-4 flex-1">
                <!-- Search -->
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text"
                           id="taskSearch"
                           placeholder="Search tasks..."
                           class="w-full pl-10 pr-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 transition-all duration-200">
                </div>

                <!-- Filters -->
                <div class="flex items-center gap-3">
                    <!-- Status Filter -->
                    <select id="statusFilter" class="px-3 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Status</option>
                        <option value="ToDo">To Do</option>
                        <option value="InProgress">In Progress</option>
                        <option value="InReview">In Review</option>
                        <option value="Done">Done</option>
                    </select>

                    <!-- Priority Filter -->
                    <select id="priorityFilter" class="px-3 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Priority</option>
                        <option value="Critical">Critical</option>
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                    </select>

                    <!-- Clear Filters Button -->
                    <button type="button"
                            id="clearFilters"
                            class="px-3 py-2.5 text-sm text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100 border border-neutral-300 dark:border-neutral-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-all duration-200"
                            title="Clear all filters">
                        <i class="fas fa-times mr-1"></i>
                        Clear
                    </button>
                </div>
            </div>

            <!-- Task Count and View Controls -->
            <div class="flex items-center gap-4">
                <!-- Task Count -->
                <div class="text-sm text-neutral-600 dark:text-neutral-400">
                    <span id="visibleTasksCount">@tasks.Count</span> tasks
                </div>

                <!-- View Controls -->
                <div class="flex items-center gap-2">
                    @{
                        ViewData["Text"] = "Expand All";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-expand-arrows-alt";
                        ViewData["OnClick"] = "expandAllTasks()";
                        ViewData["Href"] = "";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Collapse All";
                    ViewData["Variant"] = "outline";
                    ViewData["Size"] = "sm";
                    ViewData["Icon"] = "fas fa-compress-arrows-alt";
                    ViewData["OnClick"] = "collapseAllTasks()";
                    ViewData["Href"] = "";
                }
                <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Task Hierarchy Content -->
@if (tasks.Any())
{
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-sitemap text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Task Hierarchy</h3>
                        <p class="text-sm text-neutral-600 dark:text-dark-400">Hierarchical view of project tasks</p>
                    </div>
                </div>
                <div class="text-sm text-neutral-500 dark:text-dark-400">
                    <span id="visibleTasksCount">@tasks.Count</span> of @tasks.Count tasks
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            @{
                var rootTasks = tasks.Where(t => !t.ParentTaskId.HasValue).OrderBy(t => t.SortOrder).ThenBy(t => t.CreatedAt);
            }

            @if (rootTasks.Any())
            {
                <div class="space-y-1" id="taskHierarchy">
                    @foreach (var task in rootTasks)
                    {
                        ViewData["Task"] = task;
                        ViewData["AllTasks"] = tasks;
                        ViewData["Level"] = 0;
                        @await Html.PartialAsync("_TaskNode", ViewData)
                    }
                </div>
            }
            else
            {
                <div class="text-center py-12">
                    <div class="mx-auto h-24 w-24 text-neutral-400 mb-4">
                        <i class="fas fa-sitemap text-6xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No root tasks found</h3>
                    <p class="text-neutral-600 dark:text-dark-400 mb-6">All tasks appear to be subtasks. Create a parent task to organize the hierarchy.</p>
                    @{
                        ViewData["Text"] = "Create Parent Task";
                        ViewData["Variant"] = "primary";
                        ViewData["Icon"] = "fas fa-plus";
                        ViewData["Href"] = Url.Action("Create", "Tasks", new { projectId = projectId });
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            }
        </div>
    </div>
}
else
{
    <!-- Empty State -->
    <div class="card-custom">
        <div class="card-body-custom text-center py-16">
            <div class="mx-auto h-32 w-32 text-neutral-300 dark:text-neutral-600 mb-6">
                <i class="fas fa-sitemap text-8xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-3">No tasks found</h3>
            <p class="text-neutral-600 dark:text-dark-400 mb-8 max-w-md mx-auto">
                Get started by creating your first task. You can organize tasks in a hierarchical structure to break down your project work.
            </p>
            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                @{
                    ViewData["Text"] = "Create First Task";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Href"] = Url.Action("Create", "Tasks", new { projectId = projectId });
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Import Tasks";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-upload";
                    ViewData["OnClick"] = "showImportModal()";
                    ViewData["Href"] = "";
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
    </div>
}
<!-- Standardized Modals -->
@{
    ViewData["ModalId"] = "generateCodesModal";
    ViewData["ModalTitle"] = "Generate WBS Codes";
    ViewData["ModalSize"] = "md";
    ViewData["ShowFooter"] = true;
    ViewData["BodyContent"] = @"
        <div class='text-center py-6'>
            <div class='mx-auto h-16 w-16 bg-warning-100 dark:bg-warning-900 rounded-full flex items-center justify-center mb-4'>
                <i class='fas fa-code text-warning-600 dark:text-warning-400 text-2xl'></i>
            </div>
            <h3 class='text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2'>Generate WBS Codes</h3>
            <p class='text-neutral-600 dark:text-dark-400 mb-4'>
                This will automatically generate WBS codes for all tasks in this project. Existing codes will be overwritten.
            </p>
            <div class='bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800 rounded-lg p-4'>
                <div class='flex items-start'>
                    <i class='fas fa-exclamation-triangle text-warning-600 dark:text-warning-400 mt-0.5 mr-3'></i>
                    <div class='text-sm text-warning-800 dark:text-warning-200'>
                        <strong>Warning:</strong> This action cannot be undone. Make sure to backup your data if needed.
                    </div>
                </div>
            </div>
        </div>
    ";
    ViewData["FooterContent"] = @"
        <div class='flex justify-end space-x-3'>
            <button type='button' class='btn-secondary' onclick='if(window.ModalSystem) ModalSystem.hide(""generateCodesModal"")'>Cancel</button>
            <button type='button' class='btn-warning' onclick='executeGenerateCodes()'>Generate Codes</button>
        </div>
    ";
}
<partial name="Components/_Modal" view-data="ViewData" />

@{
    ViewData["ModalId"] = "exportModal";
    ViewData["ModalTitle"] = "Export WBS";
    ViewData["ModalSize"] = "md";
    ViewData["ShowFooter"] = true;
    ViewData["BodyContent"] = @"
        <div class='space-y-6'>
            <div>
                <h3 class='text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4'>Export Options</h3>
                <div class='grid grid-cols-2 gap-4'>
                    <button type='button' class='export-option-btn' onclick='exportWbs(""excel"")'>
                        <i class='fas fa-file-excel text-success-600 text-2xl mb-2'></i>
                        <div class='font-medium'>Excel</div>
                        <div class='text-sm text-neutral-500'>Spreadsheet format</div>
                    </button>
                    <button type='button' class='export-option-btn' onclick='exportWbs(""pdf"")'>
                        <i class='fas fa-file-pdf text-danger-600 text-2xl mb-2'></i>
                        <div class='font-medium'>PDF</div>
                        <div class='text-sm text-neutral-500'>Document format</div>
                    </button>
                    <button type='button' class='export-option-btn' onclick='exportWbs(""csv"")'>
                        <i class='fas fa-file-csv text-primary-600 text-2xl mb-2'></i>
                        <div class='font-medium'>CSV</div>
                        <div class='text-sm text-neutral-500'>Data format</div>
                    </button>
                    <button type='button' class='export-option-btn' onclick='exportWbs(""json"")'>
                        <i class='fas fa-file-code text-purple-600 text-2xl mb-2'></i>
                        <div class='font-medium'>JSON</div>
                        <div class='text-sm text-neutral-500'>API format</div>
                    </button>
                </div>
            </div>
        </div>
    ";
    ViewData["FooterContent"] = @"
        <div class='flex justify-end'>
            <button type='button' class='btn-secondary' onclick='if(window.ModalSystem) ModalSystem.hide(""exportModal"")'>Close</button>
        </div>
    ";
}
<partial name="Components/_Modal" view-data="ViewData" />

@section Scripts {
    <script>
        // Standardized WBS functionality using modern patterns
        const WbsManager = {
            projectId: @projectId,

            // Initialize the WBS page
            init() {
                this.initializeSearch();
                this.initializeFilters();
                this.initializeTaskControls();
                this.initializeHierarchy();
                this.updateTaskCount();
                this.updateFilterStatus();
            },

            // Initialize search functionality
            initializeSearch() {
                const searchInput = document.getElementById('taskSearch');
                if (searchInput) {
                    let searchTimeout;
                    searchInput.addEventListener('input', (e) => {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            this.filterTasks();
                        }, 300); // Debounce search
                    });
                }
            },

            // Initialize filter functionality
            initializeFilters() {
                const statusFilter = document.getElementById('statusFilter');
                const priorityFilter = document.getElementById('priorityFilter');
                const clearFilters = document.getElementById('clearFilters');

                if (statusFilter) {
                    statusFilter.addEventListener('change', () => this.filterTasks());
                }

                if (priorityFilter) {
                    priorityFilter.addEventListener('change', () => this.filterTasks());
                }

                if (clearFilters) {
                    clearFilters.addEventListener('click', () => this.clearAllFilters());
                }
            },

            // Initialize task control functionality
            initializeTaskControls() {
                // Add event listeners for expand/collapse functionality
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.task-toggle')) {
                        this.toggleTaskNode(e.target.closest('.task-toggle'));
                    }
                });
            },

            // Initialize hierarchy controls
            initializeHierarchy() {
                try {
                    // Ensure all toggle buttons have proper event handlers
                    const toggleButtons = document.querySelectorAll('.task-node button[onclick*="toggleChildren"]');

                    toggleButtons.forEach(button => {
                        // Ensure proper accessibility attributes
                        if (!button.hasAttribute('aria-expanded')) {
                            const childrenContainer = button.closest('.task-node')?.querySelector('.children-container');
                            const isExpanded = childrenContainer &&
                                             childrenContainer.style.display !== 'none' &&
                                             !childrenContainer.classList.contains('hidden');
                            button.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');
                        }

                        // Add keyboard support
                        button.addEventListener('keydown', (e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault();
                                toggleChildren(button);
                            }
                        });
                    });

                    console.log('Hierarchy controls initialized');
                } catch (error) {
                    console.error('Error initializing hierarchy:', error);
                }
            },

            // Filter tasks based on search and filter criteria
            filterTasks() {
                try {
                    const searchTerm = document.getElementById('taskSearch')?.value.toLowerCase() || '';
                    const statusFilter = document.getElementById('statusFilter')?.value || '';
                    const priorityFilter = document.getElementById('priorityFilter')?.value || '';

                    const taskNodes = document.querySelectorAll('.task-node');
                    let visibleCount = 0;
                    const visibleTasks = new Set();

                    taskNodes.forEach(node => {
                        const titleElement = node.querySelector('.task-title');
                        const title = titleElement?.textContent.toLowerCase() || '';
                        const status = node.dataset.status || '';
                        const priority = node.dataset.priority || '';
                        const taskId = node.dataset.taskId || '';

                        // Extract WBS code and task title separately for better search
                        const wbsCodeMatch = title.match(/^[\d.]+\s*/);
                        const wbsCode = wbsCodeMatch ? wbsCodeMatch[0].trim() : '';
                        const taskTitle = title.replace(/^[\d.]+\s*/, '');

                        // Search in title, WBS code, and assigned user
                        const assignedUserElement = node.querySelector('[class*="fa-user"]')?.parentElement;
                        const assignedUser = assignedUserElement?.textContent.toLowerCase() || '';

                        const matchesSearch = !searchTerm ||
                                            title.includes(searchTerm) ||
                                            wbsCode.includes(searchTerm) ||
                                            taskTitle.includes(searchTerm) ||
                                            assignedUser.includes(searchTerm);

                        const matchesStatus = !statusFilter || status === statusFilter;
                        const matchesPriority = !priorityFilter || priority === priorityFilter;

                        const isVisible = matchesSearch && matchesStatus && matchesPriority;

                        if (isVisible) {
                            visibleTasks.add(taskId);
                            this.showTaskAndParents(node, visibleTasks);
                            visibleCount++;
                        }
                    });

                    // Hide tasks that don't match and aren't parents of matching tasks
                    taskNodes.forEach(node => {
                        const taskId = node.dataset.taskId || '';
                        if (!visibleTasks.has(taskId)) {
                            node.style.display = 'none';
                        } else {
                            node.style.display = '';
                        }
                    });

                    this.updateTaskCount(visibleCount);
                    this.updateFilterStatus();
                } catch (error) {
                    console.error('Error filtering tasks:', error);
                }
            },

            // Show task and all its parents to maintain hierarchy
            showTaskAndParents(taskNode, visibleTasks) {
                let currentNode = taskNode;
                while (currentNode) {
                    const taskId = currentNode.dataset.taskId;
                    if (taskId) {
                        visibleTasks.add(taskId);
                    }

                    // Find parent task node
                    const parentContainer = currentNode.parentElement?.closest('.children-container');
                    if (parentContainer) {
                        currentNode = parentContainer.parentElement?.closest('.task-node');
                    } else {
                        break;
                    }
                }
            },

            // Clear all filters and search
            clearAllFilters() {
                try {
                    // Clear search input
                    const searchInput = document.getElementById('taskSearch');
                    if (searchInput) {
                        searchInput.value = '';
                    }

                    // Reset filter dropdowns
                    const statusFilter = document.getElementById('statusFilter');
                    if (statusFilter) {
                        statusFilter.value = '';
                    }

                    const priorityFilter = document.getElementById('priorityFilter');
                    if (priorityFilter) {
                        priorityFilter.value = '';
                    }

                    // Show all tasks
                    const taskNodes = document.querySelectorAll('.task-node');
                    taskNodes.forEach(node => {
                        node.style.display = '';
                    });

                    // Update task count
                    this.updateTaskCount();
                    this.updateFilterStatus();

                    // Show success message
                    if (window.ModernUI) {
                        ModernUI.showToast('Filters cleared', 'info', 2000);
                    }
                } catch (error) {
                    console.error('Error clearing filters:', error);
                }
            },

            // Update filter status indicators
            updateFilterStatus() {
                try {
                    const searchTerm = document.getElementById('taskSearch')?.value || '';
                    const statusFilter = document.getElementById('statusFilter')?.value || '';
                    const priorityFilter = document.getElementById('priorityFilter')?.value || '';
                    const clearButton = document.getElementById('clearFilters');

                    const hasActiveFilters = searchTerm || statusFilter || priorityFilter;

                    if (clearButton) {
                        if (hasActiveFilters) {
                            clearButton.classList.remove('text-neutral-600', 'dark:text-neutral-400');
                            clearButton.classList.add('text-primary-600', 'dark:text-primary-400');
                            clearButton.style.display = '';
                        } else {
                            clearButton.classList.remove('text-primary-600', 'dark:text-primary-400');
                            clearButton.classList.add('text-neutral-600', 'dark:text-neutral-400');
                        }
                    }
                } catch (error) {
                    console.error('Error updating filter status:', error);
                }
            },

            // Update visible task count
            updateTaskCount(visibleCount = null) {
                const countElement = document.getElementById('visibleTasksCount');
                if (countElement) {
                    if (visibleCount !== null) {
                        countElement.textContent = visibleCount;
                    } else {
                        const taskNodes = document.querySelectorAll('.task-node:not([style*="display: none"])');
                        countElement.textContent = taskNodes.length;
                    }
                }
            },

            // Toggle task node expansion
            toggleTaskNode(toggleButton) {
                const taskNode = toggleButton.closest('.task-node');
                const childrenContainer = taskNode?.querySelector('.children-container');
                const toggleIcon = toggleButton.querySelector('.toggle-icon');

                if (childrenContainer && toggleIcon) {
                    const isExpanded = childrenContainer.style.display !== 'none';

                    if (isExpanded) {
                        childrenContainer.style.display = 'none';
                        toggleIcon.classList.remove('fa-chevron-down');
                        toggleIcon.classList.add('fa-chevron-right');
                    } else {
                        childrenContainer.style.display = '';
                        toggleIcon.classList.remove('fa-chevron-right');
                        toggleIcon.classList.add('fa-chevron-down');
                    }
                }
            }
        };

        // Modal functions for standardized UI
        function showGenerateCodesModal() {
            if (window.ModalSystem) {
                ModalSystem.show('generateCodesModal');
            }
        }

        function showExportModal() {
            if (window.ModalSystem) {
                ModalSystem.show('exportModal');
            }
        }

        function showImportModal() {
            if (window.ModernUI) {
                ModernUI.showToast('Import functionality will be available in the next update.', 'info');
            } else {
                alert('Import functionality will be available in the next update.');
            }
        }

        // Execute WBS code generation with proper error handling
        function executeGenerateCodes() {
            if (window.ModalSystem) {
                ModalSystem.hide('generateCodesModal');
            }

            // Show loading notification
            if (window.ModernUI) {
                ModernUI.showToast('Please wait while WBS codes are being generated...', 'info', 10000);
            }

            fetch('@Url.Action("GenerateCodes", "Wbs")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                },
                body: JSON.stringify({ projectId: WbsManager.projectId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (window.ModernUI) {
                        ModernUI.showToast('WBS codes generated successfully.', 'success');
                    }
                    setTimeout(() => location.reload(), 1500);
                } else {
                    if (window.ModernUI) {
                        ModernUI.showToast(data.message || 'Failed to generate WBS codes.', 'danger');
                    } else {
                        alert(data.message || 'Failed to generate WBS codes.');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (window.ModernUI) {
                    ModernUI.showToast('An unexpected error occurred while generating WBS codes.', 'danger');
                } else {
                    alert('An unexpected error occurred while generating WBS codes.');
                }
            });
        }

        // Export functionality
        function exportWbs(format) {
            if (window.ModalSystem) {
                ModalSystem.hide('exportModal');
            }

            const exportUrl = '@Url.Action("Export", "Wbs")' + `?projectId=${WbsManager.projectId}&format=${format}`;

            // Create temporary link for download
            const link = document.createElement('a');
            link.href = exportUrl;
            link.download = `wbs-export.${format}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            if (window.ModernUI) {
                ModernUI.showToast(`Your WBS export in ${format.toUpperCase()} format is being prepared.`, 'success');
            }
        }

        // Task hierarchy management
        const TaskHierarchy = {
            // Toggle individual task children
            toggleChildren(button) {
                try {
                    const taskNode = button.closest('.task-node');
                    const childrenContainer = taskNode?.querySelector('.children-container');
                    const toggleIcon = button?.querySelector('.toggle-icon');

                    if (!childrenContainer || !toggleIcon) return;

                    const isCollapsed = childrenContainer.classList.contains('hidden') ||
                                      childrenContainer.style.display === 'none';

                    if (isCollapsed) {
                        // Expand
                        childrenContainer.style.display = '';
                        childrenContainer.classList.remove('hidden');
                        button.setAttribute('aria-expanded', 'true');

                        // Force reflow for smooth animation
                        childrenContainer.offsetHeight;
                    } else {
                        // Collapse
                        childrenContainer.classList.add('hidden');
                        button.setAttribute('aria-expanded', 'false');

                        // Set display none after animation completes
                        setTimeout(() => {
                            if (childrenContainer.classList.contains('hidden')) {
                                childrenContainer.style.display = 'none';
                            }
                        }, 300);
                    }
                } catch (error) {
                    console.error('Error toggling task children:', error);
                }
            },

            // Expand all tasks
            expandAll() {
                try {
                    const childrenContainers = document.querySelectorAll('.children-container');
                    const toggleButtons = document.querySelectorAll('.task-node button[onclick*="toggleChildren"]');

                    // Add loading state
                    document.getElementById('taskHierarchy')?.classList.add('task-hierarchy-loading');

                    childrenContainers.forEach((container, index) => {
                        setTimeout(() => {
                            container.style.display = '';
                            container.classList.remove('hidden');
                        }, index * 50); // Stagger animations
                    });

                    toggleButtons.forEach((button, index) => {
                        setTimeout(() => {
                            button.setAttribute('aria-expanded', 'true');
                        }, index * 50);
                    });

                    // Remove loading state after animations complete
                    setTimeout(() => {
                        document.getElementById('taskHierarchy')?.classList.remove('task-hierarchy-loading');
                        if (window.ModernUI) {
                            ModernUI.showToast('All task nodes have been expanded.', 'info');
                        }
                    }, childrenContainers.length * 50 + 300);

                } catch (error) {
                    console.error('Error expanding all tasks:', error);
                    document.getElementById('taskHierarchy')?.classList.remove('task-hierarchy-loading');
                    if (window.ModernUI) {
                        ModernUI.showToast('Failed to expand all tasks.', 'danger');
                    }
                }
            },

            // Collapse all tasks
            collapseAll() {
                try {
                    const childrenContainers = document.querySelectorAll('.children-container');
                    const toggleButtons = document.querySelectorAll('.task-node button[onclick*="toggleChildren"]');

                    // Add loading state
                    document.getElementById('taskHierarchy')?.classList.add('task-hierarchy-loading');

                    childrenContainers.forEach((container, index) => {
                        setTimeout(() => {
                            container.classList.add('hidden');
                            // Set display none after animation
                            setTimeout(() => {
                                if (container.classList.contains('hidden')) {
                                    container.style.display = 'none';
                                }
                            }, 300);
                        }, index * 50); // Stagger animations
                    });

                    toggleButtons.forEach((button, index) => {
                        setTimeout(() => {
                            button.setAttribute('aria-expanded', 'false');
                        }, index * 50);
                    });

                    // Remove loading state after animations complete
                    setTimeout(() => {
                        document.getElementById('taskHierarchy')?.classList.remove('task-hierarchy-loading');
                        if (window.ModernUI) {
                            ModernUI.showToast('All task nodes have been collapsed.', 'info');
                        }
                    }, childrenContainers.length * 50 + 600);

                } catch (error) {
                    console.error('Error collapsing all tasks:', error);
                    document.getElementById('taskHierarchy')?.classList.remove('task-hierarchy-loading');
                    if (window.ModernUI) {
                        ModernUI.showToast('Failed to collapse all tasks.', 'danger');
                    }
                }
            }
        };

        // Global functions for backward compatibility
        function expandAllTasks() {
            TaskHierarchy.expandAll();
        }

        function collapseAllTasks() {
            TaskHierarchy.collapseAll();
        }

        function toggleChildren(button) {
            TaskHierarchy.toggleChildren(button);
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            WbsManager.init();
        });
    </script>
}

@section Styles {
    <style>
        .export-option-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
        }

        .dark .export-option-btn {
            border-color: #4b5563;
        }

        .dark .export-option-btn:hover {
            border-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.1);
        }

        /* Task hierarchy animations and styles */
        .children-container {
            transition: all 0.2s ease-in-out;
            overflow: hidden;
        }

        .children-container.hidden {
            max-height: 0;
            opacity: 0;
            margin-top: 0;
            padding-top: 0;
            padding-bottom: 0;
        }

        .task-node {
            transition: all 0.15s ease-in-out;
            min-height: auto;
        }

        .task-node:hover {
            background-color: rgba(0, 0, 0, 0.025);
            border-color: rgba(0, 0, 0, 0.1);
        }

        .dark .task-node:hover {
            background-color: rgba(255, 255, 255, 0.025);
            border-color: rgba(255, 255, 255, 0.1);
        }

        /* Compact task styling */
        .task-node {
            font-size: 0.875rem;
            line-height: 1.25;
        }

        .task-title {
            font-weight: 500;
            font-size: 0.875rem;
        }

        /* Compact badges */
        .task-node .inline-flex {
            font-size: 0.75rem;
            padding: 0.125rem 0.375rem;
        }

        /* Subtle WBS codes */
        .task-title span {
            font-weight: 400;
            opacity: 0.7;
        }

        .toggle-icon {
            transition: transform 0.2s ease-in-out;
        }

        button[aria-expanded="false"] .toggle-icon {
            transform: rotate(-90deg);
        }

        button[aria-expanded="true"] .toggle-icon {
            transform: rotate(0deg);
        }

        /* Focus styles for accessibility */
        .task-node button:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* Loading state */
        .task-hierarchy-loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .export-option-btn:hover {
            border-color: #3b82f6;
            background-color: #eff6ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .task-node {
            transition: all 0.2s ease-in-out;
        }

        .task-node:hover {
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .children-container {
            border-left: 2px solid #e5e7eb;
            margin-left: 1rem;
            padding-left: 1rem;
        }

        .dark .children-container {
            border-left-color: #4b5563;
        }
    </style>
}



