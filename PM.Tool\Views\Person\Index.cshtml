@model IEnumerable<PM.Tool.Core.Entities.Person>
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "People Management";
    ViewData["PageTitle"] = "People Management";
    ViewData["PageDescription"] = "Unified management of all people in the organization - employees, contractors, stakeholders, vendors, and partners.";
}

<!-- Standardized Page Header -->
@{
    ViewData["Title"] = "People Management";
    ViewData["Description"] = "Unified management of all people in the organization - employees, contractors, stakeholders, vendors, and partners";
    ViewData["Icon"] = "fas fa-users";
    ViewData["IconColor"] = "text-primary-600 dark:text-primary-400";
    ViewData["Actions"] = new object[] {
        new { Text = "Add Person", Variant = "primary", Icon = "fas fa-plus", Href = Url.Action("Create"), OnClick = (string)null },
        new { Text = "Analytics", Variant = "secondary", Icon = "fas fa-chart-bar", Href = Url.Action("Analytics"), OnClick = (string)null }
    };

    var statsArray = new List<object>
    {
        new { Label = "Total People", Value = Model.Count().ToString(), Icon = "fas fa-users", Color = "blue" },
        new { Label = "Active", Value = Model.Count(p => p.Status == PersonStatus.Active).ToString(), Icon = "fas fa-user-check", Color = "green" },
        new { Label = "Employees", Value = Model.Count(p => p.Type == PersonType.Internal).ToString(), Icon = "fas fa-id-badge", Color = "amber" },
        new { Label = "System Users", Value = Model.Count(p => p.HasSystemAccess).ToString(), Icon = "fas fa-user-cog", Color = "indigo" }
    };

    ViewData["Stats"] = statsArray.ToArray();
}
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Standardized Filter Bar -->
<div class="card-custom mb-8">
    <form id="filterForm" method="get" class="card-body-custom">
        <!-- Single Row Layout -->
        <div class="flex flex-wrap items-center gap-6">
            <!-- Quick Filters Section -->
            <div class="flex items-center gap-3">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-3">Quick:</span>
                <div class="flex items-center gap-2">
                    @{
                        ViewData["Text"] = "Active";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-user-check";
                        ViewData["OnClick"] = "filterPeople('active')";
                        ViewData["AdditionalClasses"] = "quick-filter-btn";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "Employees";
                        ViewData["Icon"] = "fas fa-id-badge";
                        ViewData["OnClick"] = "filterPeople('employees')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "Contractors";
                        ViewData["Icon"] = "fas fa-user-tie";
                        ViewData["OnClick"] = "filterPeople('contractors')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "System Users";
                        ViewData["Icon"] = "fas fa-user-cog";
                        ViewData["OnClick"] = "filterPeople('systemusers')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>

            <!-- Main Filters Section -->
            <div class="flex items-center gap-4 flex-1">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text"
                           name="search"
                           placeholder="Search people..."
                           class="w-64 pl-11 pr-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 transition-all duration-200">
                </div>

                <!-- Filter Controls -->
                <div class="flex items-center gap-3">
                    <!-- Type Filter -->
                    <select name="type" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Types</option>
                        <option value="Internal">Employees</option>
                        <option value="Contractor">Contractors</option>
                        <option value="External">Stakeholders</option>
                        <option value="Vendor">Vendors</option>
                    </select>

                    <!-- Status Filter -->
                    <select name="status" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Status</option>
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                        <option value="Pending">Pending</option>
                    </select>

                    <!-- Department Filter -->
                    <select name="department" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Departments</option>
                        <option value="Engineering">Engineering</option>
                        <option value="Design">Design</option>
                        <option value="Marketing">Marketing</option>
                        <option value="Operations">Operations</option>
                    </select>

                    <!-- Clear Filters Button -->
                    @{
                        ViewData["Text"] = "Clear";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-times";
                        ViewData["OnClick"] = "clearAllFilters()";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>
        </div>
    </form>
</div>


<!-- Standardized People Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" id="peopleGrid">
    @foreach (var person in Model)
    {
        <div class="card-custom hover:shadow-medium transition-all duration-300"
             data-type="@person.Type" data-status="@person.Status" data-search="@person.FullName.ToLower() @person.Email.ToLower() @person.PersonCode.ToLower()">

            <!-- Person Header -->
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 @GetPersonTypeIconBg(person.Type) rounded-lg flex items-center justify-center">
                        <i class="@GetPersonTypeIcon(person.Type) text-white text-lg"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@person.FullName</h3>
                        <p class="text-sm text-neutral-600 dark:text-dark-400">@person.PersonCode</p>
                    </div>
                </div>
                <div class="flex flex-col items-end space-y-2">
                    @{
                        ViewData["Label"] = person.Type.ToString();
                        ViewData["Color"] = GetPersonTypeBadgeColor(person.Type);
                        ViewData["Size"] = "sm";
                    }
                    <partial name="Components/_StatsBadge" view-data="ViewData" />

                    @{
                        ViewData["Label"] = person.Status.ToString();
                        ViewData["Color"] = person.Status == PersonStatus.Active ? "green" : "red";
                        ViewData["Size"] = "sm";
                    }
                    <partial name="Components/_StatsBadge" view-data="ViewData" />
                </div>
            </div>

            <!-- Person Details -->
            <div class="card-body-custom">
                <div class="space-y-3">
                    @if (!string.IsNullOrEmpty(person.Title))
                    {
                        <div class="flex items-center text-sm text-neutral-600 dark:text-dark-400">
                            <i class="fas fa-briefcase w-4 h-4 mr-3 text-neutral-500 dark:text-dark-500"></i>
                            <span>@person.Title</span>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(person.Department))
                    {
                        <div class="flex items-center text-sm text-neutral-600 dark:text-dark-400">
                            <i class="fas fa-sitemap w-4 h-4 mr-3 text-neutral-500 dark:text-dark-500"></i>
                            <span>@person.Department</span>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(person.Organization))
                    {
                        <div class="flex items-center text-sm text-neutral-600 dark:text-dark-400">
                            <i class="fas fa-building w-4 h-4 mr-3 text-neutral-500 dark:text-dark-500"></i>
                            <span>@person.Organization</span>
                        </div>
                    }
                    <div class="flex items-center text-sm text-neutral-600 dark:text-dark-400">
                        <i class="fas fa-envelope w-4 h-4 mr-3 text-neutral-500 dark:text-dark-500"></i>
                        <span class="truncate">@person.Email</span>
                    </div>
                </div>

                <!-- Person Roles -->
                @if (person.Roles.Any())
                {
                    <div class="mt-4">
                        <div class="flex flex-wrap gap-2">
                            @foreach (var role in person.Roles.Take(3))
                            {
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    @role.RoleCode
                                </span>
                            }
                            @if (person.Roles.Count() > 3)
                            {
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                    +@(person.Roles.Count() - 3) more
                                </span>
                            }
                        </div>
                    </div>
                }
            </div>

            <!-- Action Buttons -->
            <div class="card-footer-custom">
                <div class="flex space-x-2 w-full">
                    @{
                        ViewData["Text"] = "View";
                        ViewData["Variant"] = "secondary";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-eye";
                        ViewData["Href"] = Url.Action("Details", new { id = person.Id });
                        ViewData["OnClick"] = null;
                        ViewData["ContainerClasses"] = "flex-1";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "Edit";
                        ViewData["Variant"] = "primary";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-edit";
                        ViewData["Href"] = Url.Action("Edit", new { id = person.Id });
                        ViewData["OnClick"] = null;
                        ViewData["ContainerClasses"] = "flex-1";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @if (person.HasSystemAccess)
                    {
                        <div class="flex items-center justify-center w-10 h-8 bg-success-100 dark:bg-success-900 rounded-md" title="Has System Access">
                            <i class="fas fa-user-cog text-success-600 dark:text-success-400"></i>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
</div>

<!-- Enhanced Empty State -->
@if (!Model.Any())
{
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-users"></i>
        </div>
        <h3 class="empty-state-title">No People Found</h3>
        <p class="empty-state-description">Get started by adding your first person to the system. You can manage employees, contractors, stakeholders, vendors, and partners all in one place.</p>
        <a href="@Url.Action("Create")" class="btn-enterprise btn-primary-enterprise">
            <i class="fas fa-plus mr-2"></i>
            Add First Person
        </a>
    </div>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Search functionality
            $('#searchInput').on('input', function() {
                filterPeople();
            });

            // Type filter functionality
            $('.filter-chip').on('click', function() {
                $('.filter-chip').removeClass('active');
                $(this).addClass('active');
                filterPeople();
            });

            // Status filter functionality
            $('#statusFilter').on('change', function() {
                filterPeople();
            });

            function filterPeople() {
                const searchTerm = $('#searchInput').val().toLowerCase();
                const selectedType = $('.filter-chip.active').data('filter');
                const selectedStatus = $('#statusFilter').val();

                $('.person-card').each(function() {
                    const $card = $(this);
                    const $column = $card.closest('.col-xl-3, .col-lg-4, .col-md-6, .col-sm-12');
                    const cardType = $card.data('type');
                    const cardStatus = $card.data('status');
                    const cardSearch = $card.data('search');

                    let showCard = true;

                    // Search filter
                    if (searchTerm && !cardSearch.includes(searchTerm)) {
                        showCard = false;
                    }

                    // Type filter
                    if (selectedType && selectedType !== 'all' && cardType !== selectedType) {
                        showCard = false;
                    }

                    // Status filter
                    if (selectedStatus && cardStatus !== selectedStatus) {
                        showCard = false;
                    }

                    if (showCard) {
                        $column.fadeIn(300);
                    } else {
                        $column.fadeOut(300);
                    }
                });
            }

            // Initialize first filter as active
            $('.filter-chip[data-filter="all"]').addClass('active');

            // Add smooth animations for card filtering
            $('.person-card').closest('.col-xl-3, .col-lg-4, .col-md-6, .col-sm-12').hide().fadeIn(300);

            // Add keyboard support for filter chips
            $('.filter-chip').on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    $(this).click();
                }
            });
        });
    </script>
}

@functions {
    private string GetPersonTypeIcon(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "fas fa-user-tie",
            PersonType.External => "fas fa-user-friends",
            PersonType.Contractor => "fas fa-user-hard-hat",
            PersonType.Vendor => "fas fa-user-tag",
            PersonType.Customer => "fas fa-handshake",
            PersonType.Partner => "fas fa-users",
            PersonType.Regulatory => "fas fa-gavel",
            _ => "fas fa-user"
        };
    }

    private string GetPersonTypeColor(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "#2563eb",
            PersonType.External => "#22c55e",
            PersonType.Contractor => "#f59e0b",
            PersonType.Vendor => "#8b5cf6",
            PersonType.Customer => "#ec4899",
            PersonType.Partner => "#6366f1",
            PersonType.Regulatory => "#ef4444",
            _ => "#6b7280"
        };
    }

    private string GetPersonTypeStatusPill(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "status-pill status-in-progress",
            PersonType.External => "status-pill status-completed",
            PersonType.Contractor => "status-pill status-on-hold",
            PersonType.Vendor => "status-pill status-blocked",
            PersonType.Customer => "status-pill status-todo",
            PersonType.Partner => "status-pill status-in-progress",
            PersonType.Regulatory => "status-pill status-blocked",
            _ => "status-pill status-todo"
        };
    }

    private string GetPersonTypeIconBg(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "bg-blue-500",
            PersonType.External => "bg-green-500",
            PersonType.Contractor => "bg-orange-500",
            PersonType.Vendor => "bg-purple-500",
            PersonType.Customer => "bg-pink-500",
            PersonType.Partner => "bg-indigo-500",
            PersonType.Regulatory => "bg-red-500",
            _ => "bg-neutral-500"
        };
    }

    private string GetPersonTypeBadgeClass(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            PersonType.External => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            PersonType.Contractor => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            PersonType.Vendor => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
            PersonType.Customer => "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",
            PersonType.Partner => "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
            PersonType.Regulatory => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }

    private string GetPersonTypeBadgeColor(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "blue",
            PersonType.External => "green",
            PersonType.Contractor => "amber",
            PersonType.Vendor => "purple",
            PersonType.Customer => "pink",
            PersonType.Partner => "indigo",
            PersonType.Regulatory => "red",
            _ => "gray"
        };
    }
}
