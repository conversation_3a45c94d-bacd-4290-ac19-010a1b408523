@{
    /*
    Standardized Stats Badge Component
    
    Usage:
    @{
        ViewData["Label"] = "Total";
        ViewData["Value"] = "100";
        ViewData["Icon"] = "fas fa-chart";
        ViewData["Color"] = "blue"; // blue, green, yellow, red, purple, indigo, emerald, amber
        ViewData["Size"] = "md"; // sm, md, lg
        ViewData["Href"] = "/link"; // Optional - makes badge clickable
        ViewData["Animated"] = true; // Optional - adds hover animation
    }
    <partial name="Components/_StatsBadge" view-data="ViewData" />
    */

    var label = ViewData["Label"]?.ToString() ?? "Label";
    var value = ViewData["Value"]?.ToString() ?? "0";
    var icon = ViewData["Icon"]?.ToString();
    var color = ViewData["Color"]?.ToString() ?? "blue";
    var size = ViewData["Size"]?.ToString() ?? "md";
    var href = ViewData["Href"]?.ToString();
    var animated = ViewData["Animated"] as bool? ?? true;

    // Color classes mapping
    var colorClasses = color switch {
        "blue" => "bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200",
        "green" => "bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200",
        "yellow" => "bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-200",
        "red" => "bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200",
        "purple" => "bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200",
        "indigo" => "bg-indigo-100 dark:bg-indigo-900/50 text-indigo-800 dark:text-indigo-200",
        "emerald" => "bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200",
        "amber" => "bg-amber-100 dark:bg-amber-900/50 text-amber-800 dark:text-amber-200",
        "neutral" => "bg-neutral-100 dark:bg-neutral-900/50 text-neutral-800 dark:text-neutral-200",
        "primary" => "bg-primary-100 dark:bg-primary-900/50 text-primary-800 dark:text-primary-200",
        "secondary" => "bg-secondary-100 dark:bg-secondary-900/50 text-secondary-800 dark:text-secondary-200",
        "success" => "bg-success-100 dark:bg-success-900/50 text-success-800 dark:text-success-200",
        "warning" => "bg-warning-100 dark:bg-warning-900/50 text-warning-800 dark:text-warning-200",
        "danger" => "bg-danger-100 dark:bg-danger-900/50 text-danger-800 dark:text-danger-200",
        "info" => "bg-info-100 dark:bg-info-900/50 text-info-800 dark:text-info-200",
        _ => "bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200"
    };

    // Icon color classes
    var iconColorClasses = color switch {
        "blue" => "text-blue-600 dark:text-blue-400",
        "green" => "text-green-600 dark:text-green-400",
        "yellow" => "text-yellow-600 dark:text-yellow-400",
        "red" => "text-red-600 dark:text-red-400",
        "purple" => "text-purple-600 dark:text-purple-400",
        "indigo" => "text-indigo-600 dark:text-indigo-400",
        "emerald" => "text-emerald-600 dark:text-emerald-400",
        "amber" => "text-amber-600 dark:text-amber-400",
        "neutral" => "text-neutral-600 dark:text-neutral-400",
        "primary" => "text-primary-600 dark:text-primary-400",
        "secondary" => "text-secondary-600 dark:text-secondary-400",
        "success" => "text-success-600 dark:text-success-400",
        "warning" => "text-warning-600 dark:text-warning-400",
        "danger" => "text-danger-600 dark:text-danger-400",
        "info" => "text-info-600 dark:text-info-400",
        _ => "text-blue-600 dark:text-blue-400"
    };

    // Size classes
    var sizeClasses = size switch {
        "sm" => "px-2 py-1 text-xs",
        "md" => "px-3 py-1.5 text-xs",
        "lg" => "px-4 py-2 text-sm",
        _ => "px-3 py-1.5 text-xs"
    };

    // Base classes
    var baseClasses = $"inline-flex items-center {sizeClasses} {colorClasses} rounded-full font-medium";
    
    // Animation classes
    var animationClasses = animated ? "transition-all duration-200 hover:scale-105 hover:shadow-sm" : "";
    
    // Clickable classes
    var clickableClasses = !string.IsNullOrEmpty(href) ? "cursor-pointer hover:opacity-80" : "";

    var finalClasses = $"{baseClasses} {animationClasses} {clickableClasses}".Trim();
}

@if (!string.IsNullOrEmpty(href))
{
    <a href="@href" class="@finalClasses">
        @if (!string.IsNullOrEmpty(icon))
        {
            <i class="@icon mr-2 @iconColorClasses"></i>
        }
        <span class="font-semibold">@value</span>
        @if (!string.IsNullOrEmpty(label))
        {
            <span class="ml-1">@label</span>
        }
    </a>
}
else
{
    <span class="@finalClasses">
        @if (!string.IsNullOrEmpty(icon))
        {
            <i class="@icon mr-2 @iconColorClasses"></i>
        }
        <span class="font-semibold">@value</span>
        @if (!string.IsNullOrEmpty(label))
        {
            <span class="ml-1">@label</span>
        }
    </span>
}
