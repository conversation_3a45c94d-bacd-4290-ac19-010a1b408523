using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Models.ViewModels;
using System.Security.Claims;

namespace PM.Tool.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class NotificationsController : ControllerBase
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<NotificationsController> _logger;

        public NotificationsController(
            INotificationService notificationService,
            ILogger<NotificationsController> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
        }

        /// <summary>
        /// Get user notifications
        /// </summary>
        /// <param name="unreadOnly">Show only unread notifications</param>
        /// <param name="limit">Maximum number of notifications to return</param>
        /// <returns>List of notifications</returns>
        [HttpGet]
        public async Task<IActionResult> GetNotifications([FromQuery] bool unreadOnly = false, [FromQuery] int limit = 50)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var notifications = await _notificationService.GetUserNotificationsAsync(userId, unreadOnly);
                var limitedNotifications = notifications.Take(limit);

                var result = limitedNotifications.Select(n => new
                {
                    Id = n.Id,
                    Title = n.Title,
                    Message = n.Message,
                    Type = n.Type.ToString(),
                    IsRead = n.IsRead,
                    CreatedAt = n.CreatedAt,
                    ReadAt = n.ReadAt,
                    ProjectId = n.RelatedProjectId,
                    TaskId = n.RelatedTaskId,
                    ProjectName = n.RelatedProject?.Name,
                    TaskTitle = n.RelatedTask?.Title,
                    Url = GetNotificationUrl(n)
                });

                return Ok(new { notifications = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notifications for user");
                return StatusCode(500, new { error = "Failed to get notifications" });
            }
        }

        /// <summary>
        /// Get unread notification count
        /// </summary>
        /// <returns>Number of unread notifications</returns>
        [HttpGet("count")]
        public async Task<IActionResult> GetUnreadCount()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var count = await _notificationService.GetUnreadCountAsync(userId);
                return Ok(new { count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread notification count");
                return StatusCode(500, new { error = "Failed to get notification count" });
            }
        }

        /// <summary>
        /// Mark a notification as read
        /// </summary>
        /// <param name="id">Notification ID</param>
        /// <returns>Success status</returns>
        [HttpPost("{id}/read")]
        public async Task<IActionResult> MarkAsRead(int id)
        {
            try
            {
                var success = await _notificationService.MarkAsReadAsync(id);
                if (!success)
                {
                    return NotFound(new { error = "Notification not found" });
                }

                return Ok(new { success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification {NotificationId} as read", id);
                return StatusCode(500, new { error = "Failed to mark notification as read" });
            }
        }

        /// <summary>
        /// Mark all notifications as read for the current user
        /// </summary>
        /// <returns>Success status</returns>
        [HttpPost("mark-all-read")]
        public async Task<IActionResult> MarkAllAsRead()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var success = await _notificationService.MarkAllAsReadAsync(userId);
                return Ok(new { success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking all notifications as read for user");
                return StatusCode(500, new { error = "Failed to mark all notifications as read" });
            }
        }

        /// <summary>
        /// Create a test notification (for development/testing)
        /// </summary>
        /// <param name="request">Test notification request</param>
        /// <returns>Created notification</returns>
        [HttpPost("test")]
        public async Task<IActionResult> CreateTestNotification([FromBody] CreateTestNotificationRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var notification = await _notificationService.CreateNotificationAsync(
                    userId,
                    request.Title ?? "Test Notification",
                    request.Message ?? "This is a test notification",
                    request.Type ?? NotificationType.TaskAssigned,
                    request.ProjectId,
                    request.TaskId);

                return Ok(new { 
                    success = true, 
                    notification = new {
                        Id = notification.Id,
                        Title = notification.Title,
                        Message = notification.Message,
                        Type = notification.Type.ToString()
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating test notification");
                return StatusCode(500, new { error = "Failed to create test notification" });
            }
        }

        private string? GetNotificationUrl(Notification notification)
        {
            if (notification.RelatedTaskId.HasValue)
            {
                return Url.Action("Details", "Tasks", new { id = notification.RelatedTaskId.Value });
            }

            if (notification.RelatedProjectId.HasValue)
            {
                return Url.Action("Details", "Projects", new { id = notification.RelatedProjectId.Value });
            }

            return null;
        }

        // POST: api/notifications/create-test
        [HttpPost("create-test")]
        public async Task<IActionResult> CreateTestNotifications()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                // Create some test notifications using the service
                var testNotifications = new List<Notification>();

                var notification1 = await _notificationService.CreateNotificationAsync(
                    userId,
                    "Welcome to PM.Tool",
                    "Your account has been set up successfully. Start by creating your first project!",
                    NotificationType.TaskAssigned
                );
                testNotifications.Add(notification1);

                var notification2 = await _notificationService.CreateNotificationAsync(
                    userId,
                    "Task Assignment",
                    "You have been assigned a new task: 'Review project requirements'",
                    NotificationType.TaskAssigned
                );
                testNotifications.Add(notification2);

                var notification3 = await _notificationService.CreateNotificationAsync(
                    userId,
                    "Project Update",
                    "The 'Website Redesign' project has been updated with new requirements",
                    NotificationType.ProjectUpdated
                );
                testNotifications.Add(notification3);

                return Ok(new { message = "Test notifications created successfully", count = testNotifications.Count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating test notifications");
                return StatusCode(500, new { error = "Failed to create test notifications" });
            }
        }
    }

    public class CreateTestNotificationRequest
    {
        public string? Title { get; set; }
        public string? Message { get; set; }
        public NotificationType? Type { get; set; }
        public int? ProjectId { get; set; }
        public int? TaskId { get; set; }
    }
}
