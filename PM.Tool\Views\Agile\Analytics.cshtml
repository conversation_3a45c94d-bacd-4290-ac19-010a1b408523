@{
    ViewData["Title"] = "Sprint Analytics & Reporting";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Agile", Href = Url.Action("Index", "Agile"), Icon = "fas fa-tasks" },
        new { Text = "Analytics", Href = (string?)null, Icon = "fas fa-chart-line" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Standardized Page Header -->
@{
    ViewData["Title"] = "Sprint Analytics & Reporting";
    ViewData["Description"] = $"Comprehensive analytics and insights for {ViewBag.Project?.Name ?? "All Projects"}";
    ViewData["Icon"] = "fas fa-chart-line";

    ViewData["Actions"] = new[] {
        new { Text = "Export Analytics", Href = (string?)null, Icon = "fas fa-download", Variant = "primary", OnClick = (string?)"exportAnalytics('pdf')" },
        new { Text = "Schedule Report", Href = (string?)null, Icon = "fas fa-clock", Variant = "outline", OnClick = (string?)"scheduleReport()" },
        new { Text = "Back to Agile", Href = Url.Action("Index", "Agile"), Icon = "fas fa-arrow-left", Variant = "secondary", OnClick = (string?)null }
    };

    ViewData["Stats"] = new[] {
        new { Label = "Sprint Velocity", Value = "12.5", Icon = "fas fa-rocket", Color = "blue" },
        new { Label = "Completion Rate", Value = "85%", Icon = "fas fa-check-circle", Color = "green" },
        new { Label = "Story Points", Value = "156", Icon = "fas fa-tasks", Color = "purple" },
        new { Label = "Team Efficiency", Value = "85%", Icon = "fas fa-users", Color = "amber" }
    };
}
<partial name="Components/_PageHeader" view-data="ViewData" />

<div class="container mx-auto" data-project-id="@ViewBag.ProjectId">

    <!-- Analytics Navigation -->
    <div class="mb-6">
        <div class="border-b border-neutral-200 dark:border-dark-300">
            <nav class="-mb-px flex space-x-8">
                <button class="analytics-tab-btn active" data-tab="overview">
                    <i class="fas fa-tachometer-alt mr-2"></i>Overview
                </button>
                <button class="analytics-tab-btn" data-tab="velocity">
                    <i class="fas fa-rocket mr-2"></i>Velocity
                </button>
                <button class="analytics-tab-btn" data-tab="burndown">
                    <i class="fas fa-chart-area mr-2"></i>Burndown
                </button>
                <button class="analytics-tab-btn" data-tab="team">
                    <i class="fas fa-users mr-2"></i>Team Performance
                </button>
                <button class="analytics-tab-btn" data-tab="quality">
                    <i class="fas fa-shield-alt mr-2"></i>Quality Metrics
                </button>
            </nav>
        </div>
    </div>

    <!-- Overview Tab -->
    <div id="overview-tab" class="analytics-tab-content">
        <!-- Sprint Overview Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Sprint Progress Chart -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-chart-pie text-primary-600 mr-2"></i>
                    Sprint Progress
                </h3>
                <div class="h-64">
                    <canvas id="sprint-progress-chart"></canvas>
                </div>
            </div>

            <!-- Story Points Burndown -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-chart-line text-primary-600 mr-2"></i>
                    Story Points Burndown
                </h3>
                <div class="h-64">
                    <canvas id="story-points-burndown-chart"></canvas>
                </div>
            </div>
        </div>

        <!-- Epic Progress Chart -->
        <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6 mb-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                <i class="fas fa-layer-group text-primary-600 mr-2"></i>
                Epic Progress Overview
            </h3>
            <div class="h-64">
                <canvas id="epic-progress-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- Velocity Tab -->
    <div id="velocity-tab" class="analytics-tab-content hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Velocity Trend Chart -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-rocket text-primary-600 mr-2"></i>
                    Velocity Trend
                </h3>
                <div class="h-64">
                    <canvas id="velocity-chart"></canvas>
                </div>
            </div>

            <!-- Velocity Statistics -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-calculator text-primary-600 mr-2"></i>
                    Velocity Statistics
                </h3>
                <div class="space-y-4" id="velocity-stats">
                    <!-- Velocity stats will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Velocity Data Table -->
        <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6 mt-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                <i class="fas fa-table text-primary-600 mr-2"></i>
                Sprint Velocity Details
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-neutral-200 dark:divide-dark-300">
                    <thead class="bg-neutral-50 dark:bg-dark-400">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-200 uppercase tracking-wider">Sprint</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-200 uppercase tracking-wider">Planned Points</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-200 uppercase tracking-wider">Completed Points</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-200 uppercase tracking-wider">Velocity</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-200 uppercase tracking-wider">Commitment Accuracy</th>
                        </tr>
                    </thead>
                    <tbody id="velocity-table-body" class="bg-white dark:bg-surface-dark divide-y divide-neutral-200 dark:divide-dark-300">
                        <!-- Table rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Burndown Tab -->
    <div id="burndown-tab" class="analytics-tab-content hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Sprint Burndown -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-chart-area text-primary-600 mr-2"></i>
                    Sprint Burndown
                </h3>
                <div class="h-64">
                    <canvas id="burndown-chart"></canvas>
                </div>
            </div>

            <!-- Burnup Chart -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-chart-line text-primary-600 mr-2"></i>
                    Sprint Burnup
                </h3>
                <div class="h-64">
                    <canvas id="burnup-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Team Performance Tab -->
    <div id="team-tab" class="analytics-tab-content hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Team Productivity -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-users text-primary-600 mr-2"></i>
                    Team Productivity
                </h3>
                <div class="h-64">
                    <canvas id="team-productivity-chart"></canvas>
                </div>
            </div>

            <!-- Individual Performance -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-user text-primary-600 mr-2"></i>
                    Individual Performance
                </h3>
                <div class="space-y-4" id="individual-performance">
                    <!-- Individual performance data will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Quality Metrics Tab -->
    <div id="quality-tab" class="analytics-tab-content hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Defect Trend -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-bug text-primary-600 mr-2"></i>
                    Defect Trend
                </h3>
                <div class="h-64">
                    <canvas id="defect-chart"></canvas>
                </div>
            </div>

            <!-- Quality Metrics -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-shield-alt text-primary-600 mr-2"></i>
                    Quality Metrics
                </h3>
                <div class="space-y-4" id="quality-metrics">
                    <!-- Quality metrics will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Export Actions -->
    <div class="mt-8 flex justify-end space-x-4">
        <button class="btn-secondary-custom" onclick="exportAnalytics('pdf')">
            <i class="fas fa-file-pdf mr-2"></i>
            Export PDF
        </button>
        <button class="btn-secondary-custom" onclick="exportAnalytics('excel')">
            <i class="fas fa-file-excel mr-2"></i>
            Export Excel
        </button>
        <button class="btn-primary-custom" onclick="refreshAnalytics()">
            <i class="fas fa-sync-alt mr-2"></i>
            Refresh Data
        </button>
    </div>
</div>

<style>
.analytics-tab-btn {
    padding: 0.5rem 0.25rem;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    font-size: 0.875rem;
    color: #6B7280;
    transition: all 0.2s;
}

.dark .analytics-tab-btn {
    color: #9CA3AF;
}

.analytics-tab-btn:hover {
    color: #374151;
    border-bottom-color: #D1D5DB;
}

.dark .analytics-tab-btn:hover {
    color: #F9FAFB;
    border-bottom-color: #4B5563;
}

.analytics-tab-btn.active {
    border-bottom-color: #3B82F6;
    color: #3B82F6;
}

.dark .analytics-tab-btn.active {
    color: #60A5FA;
}

.analytics-tab-content {
    transition: opacity 0.2s;
}
</style>

<script>
// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.analytics-tab-btn');
    const tabContents = document.querySelectorAll('.analytics-tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            
            // Update button states
            tabButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Update content visibility
            tabContents.forEach(content => {
                if (content.id === `${tabName}-tab`) {
                    content.classList.remove('hidden');
                } else {
                    content.classList.add('hidden');
                }
            });
            
            // Load tab-specific data
            loadTabData(tabName);
        });
    });

    // Load initial data
    loadTabData('overview');
});

// Data loading functions
function loadTabData(tabName) {
    switch(tabName) {
        case 'overview':
            loadOverviewData();
            break;
        case 'velocity':
            loadVelocityData();
            break;
        case 'burndown':
            loadBurndownData();
            break;
        case 'team':
            loadTeamData();
            break;
        case 'quality':
            loadQualityData();
            break;
    }
}

function loadOverviewData() {
    // Load epic progress chart
    const epicData = @Html.Raw(Json.Serialize(ViewBag.EpicProgress ?? new {}));
    createEpicProgressChart(epicData);
}

function loadVelocityData() {
    const velocityData = @Html.Raw(Json.Serialize(ViewBag.VelocityData ?? new {}));
    createVelocityChart(velocityData);
    populateVelocityStats(velocityData);
    populateVelocityTable(velocityData);
}

function loadBurndownData() {
    // Implementation for burndown charts
    createBurndownChart();
    createBurnupChart();
}

function loadTeamData() {
    // Implementation for team performance
    createTeamProductivityChart();
    populateIndividualPerformance();
}

function loadQualityData() {
    // Implementation for quality metrics
    createDefectChart();
    populateQualityMetrics();
}

// Chart creation functions will be implemented next
function createEpicProgressChart(data) {
    // Epic progress chart implementation
}

function createVelocityChart(data) {
    // Velocity chart implementation
}

// Export and refresh functions
function exportAnalytics(format) {
    try {
        const projectId = @ViewBag.ProjectId;
        window.open(`/Agile/ExportAnalytics/${projectId}?format=${format}`, '_blank');
    } catch (error) {
        console.error('Error in exportAnalytics:', error);
    }
}

function refreshAnalytics() {
    try {
        if (window.agileAnalytics) {
            window.agileAnalytics.refreshAllData();
        } else {
            location.reload();
        }
    } catch (error) {
        console.error('Error in refreshAnalytics:', error);
    }
}

function scheduleReport() {
    try {
        console.log('Schedule report requested');
        alert('Schedule report functionality will be implemented');
    } catch (error) {
        console.error('Error in scheduleReport:', error);
    }
}
</script>

<script src="~/js/agile-analytics.js"></script>
