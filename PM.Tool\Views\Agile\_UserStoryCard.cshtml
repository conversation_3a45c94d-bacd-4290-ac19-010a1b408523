@model PM.Tool.Core.Entities.Agile.UserStory

<div class="card user-story-card mb-2" data-story-id="@Model.Id" data-status="@Model.Status.ToString().ToLower()">
    <div class="card-body p-3">
        <div class="d-flex justify-content-between align-items-start mb-2">
            <div class="d-flex align-items-center">
                <span class="badge bg-@GetPriorityBadgeClass(Model.Priority) me-2">@Model.Priority</span>
                @if (Model.StoryPoints > 0)
                {
                    <span class="badge bg-info">@Model.StoryPoints SP</span>
                }
            </div>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="editUserStory(@Model.Id)">
                        <i class="fas fa-edit me-2"></i>Edit
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="viewUserStory(@Model.Id)">
                        <i class="fas fa-eye me-2"></i>View Details
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="moveToSprint(@Model.Id)">
                        <i class="fas fa-arrow-right me-2"></i>Move to Sprint
                    </a></li>
                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteUserStory(@Model.Id)">
                        <i class="fas fa-trash me-2"></i>Delete
                    </a></li>
                </ul>
            </div>
        </div>

        <h6 class="card-title">@Model.Title</h6>
        <p class="card-text text-muted small">@Model.Description</p>

        <div class="user-story-meta">
            <div class="row">
                <div class="col-6">
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>
                        @if (Model.AssignedTo != null)
                        {
                            <span>@Model.AssignedTo.UserName</span>
                        }
                        else
                        {
                            <span class="text-muted">Unassigned</span>
                        }
                    </small>
                </div>
                <div class="col-6 text-end">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        @Model.CreatedAt.ToString("MMM dd")
                    </small>
                </div>
            </div>
        </div>

        @if (Model.AcceptanceCriteria != null && Model.AcceptanceCriteria.Any())
        {
            <div class="mt-2">
                <small class="text-muted">Acceptance Criteria:</small>
                <ul class="list-unstyled small mt-1">
                    @foreach (var criteria in Model.AcceptanceCriteria.Take(2))
                    {
                        <li><i class="fas fa-check text-success me-1"></i>@criteria</li>
                    }
                    @if (Model.AcceptanceCriteria.Count() > 2)
                    {
                        <li><small class="text-muted">+@(Model.AcceptanceCriteria.Count() - 2) more...</small></li>
                    }
                </ul>
            </div>
        }

        @if (Model.Tasks != null && Model.Tasks.Any())
        {
            <div class="mt-2">
                <div class="progress" style="height: 6px;">
                    @{
                        var completedTasks = Model.Tasks.Count(t => t.Status == PM.Tool.Core.Enums.TaskStatus.Done);
                        var totalTasks = Model.Tasks.Count();
                        var progressPercentage = totalTasks > 0 ? (completedTasks * 100 / totalTasks) : 0;
                    }
                    <div class="progress-bar" role="progressbar" style="width: @progressPercentage%"></div>
                </div>
                <small class="text-muted">@completedTasks/@totalTasks tasks completed</small>
            </div>
        }
    </div>
</div>

@functions {
    string GetPriorityBadgeClass(PM.Tool.Core.Entities.Agile.UserStoryPriority priority)
    {
        return priority switch
        {
            PM.Tool.Core.Entities.Agile.UserStoryPriority.Critical => "danger",
            PM.Tool.Core.Entities.Agile.UserStoryPriority.High => "warning",
            PM.Tool.Core.Entities.Agile.UserStoryPriority.Medium => "info",
            PM.Tool.Core.Entities.Agile.UserStoryPriority.Low => "success",
            _ => "secondary"
        };
    }
}
