/**
 * PM.Tool Mentions System Styles
 * Professional styling for @mention functionality
 */

/* Mention Suggestions Dropdown */
.mention-suggestions {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    max-height: 240px;
    overflow-y: auto;
    z-index: 1000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.mention-suggestions::-webkit-scrollbar {
    width: 6px;
}

.mention-suggestions::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.mention-suggestions::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.mention-suggestions::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Dark mode support */
.dark .mention-suggestions {
    background: #1f2937;
    border-color: #374151;
    color: white;
}

.dark .mention-suggestions::-webkit-scrollbar-track {
    background: #374151;
}

.dark .mention-suggestions::-webkit-scrollbar-thumb {
    background: #4b5563;
}

.dark .mention-suggestions::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Suggestion Items */
.mention-suggestion-item {
    padding: 10px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.15s ease;
}

.mention-suggestion-item:last-child {
    border-bottom: none;
}

.mention-suggestion-item:hover,
.mention-suggestion-item.selected {
    background-color: #f8fafc;
}

.dark .mention-suggestion-item {
    border-bottom-color: #374151;
}

.dark .mention-suggestion-item:hover,
.dark .mention-suggestion-item.selected {
    background-color: #374151;
}

/* User Avatar */
.mention-suggestion-item img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
    flex-shrink: 0;
}

.dark .mention-suggestion-item img {
    border-color: #4b5563;
}

/* User Info */
.mention-suggestion-item .user-info {
    flex: 1;
    min-width: 0;
}

.mention-suggestion-item .user-name {
    font-weight: 500;
    color: #111827;
    font-size: 14px;
    line-height: 1.4;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mention-suggestion-item .user-username {
    color: #6b7280;
    font-size: 12px;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dark .mention-suggestion-item .user-name {
    color: #f9fafb;
}

.dark .mention-suggestion-item .user-username {
    color: #9ca3af;
}

/* Mention Highlights in Content */
.mention {
    background-color: #dbeafe;
    color: #1e40af;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.15s ease;
}

.mention:hover {
    background-color: #bfdbfe;
    color: #1d4ed8;
}

.dark .mention {
    background-color: #1e3a8a;
    color: #93c5fd;
}

.dark .mention:hover {
    background-color: #1e40af;
    color: #bfdbfe;
}

/* Input Field Enhancements */
.mention-enabled {
    position: relative;
}

.mention-enabled textarea,
.mention-enabled input[type="text"] {
    resize: vertical;
}

/* Loading State */
.mention-suggestions.loading {
    padding: 16px;
    text-align: center;
    color: #6b7280;
    font-size: 14px;
}

.mention-suggestions.loading::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Empty State */
.mention-suggestions.empty {
    padding: 16px;
    text-align: center;
    color: #9ca3af;
    font-size: 14px;
}

.dark .mention-suggestions.empty {
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 640px) {
    .mention-suggestions {
        max-height: 180px;
        border-radius: 0.375rem;
    }
    
    .mention-suggestion-item {
        padding: 8px 10px;
        gap: 8px;
    }
    
    .mention-suggestion-item img {
        width: 28px;
        height: 28px;
    }
    
    .mention-suggestion-item .user-name {
        font-size: 13px;
    }
    
    .mention-suggestion-item .user-username {
        font-size: 11px;
    }
}

/* Accessibility */
.mention-suggestion-item:focus {
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
}

.mention-suggestions[aria-hidden="true"] {
    display: none;
}

/* Animation */
.mention-suggestions {
    animation: fadeInUp 0.15s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(4px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .mention-suggestions {
        border-width: 2px;
    }
    
    .mention-suggestion-item {
        border-bottom-width: 2px;
    }
    
    .mention {
        border: 1px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .mention-suggestions {
        animation: none;
    }
    
    .mention-suggestion-item {
        transition: none;
    }
    
    .mention {
        transition: none;
    }
    
    .mention-suggestions.loading::before {
        animation: none;
    }
}
