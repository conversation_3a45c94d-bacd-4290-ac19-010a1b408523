@*
    Standardized Tooltip Component - Usage Examples:

    1. Basic tooltip:
    @{
        ViewData["TooltipText"] = "This is a helpful tooltip";
        ViewData["TooltipPosition"] = "top"; // top, bottom, left, right
    }
    <span data-tooltip="@ViewData["TooltipText"]" data-tooltip-position="@ViewData["TooltipPosition"]">
        Hover me
    </span>

    2. Rich tooltip with HTML:
    @{
        ViewData["TooltipContent"] = "<strong>Rich Content</strong><br>With HTML formatting";
        ViewData["TooltipPosition"] = "bottom";
        ViewData["TooltipTheme"] = "dark"; // light, dark
    }
    <button data-tooltip-html="@ViewData["TooltipContent"]" data-tooltip-position="@ViewData["TooltipPosition"]" data-tooltip-theme="@ViewData["TooltipTheme"]">
        Rich Tooltip
    </button>

    3. Delayed tooltip:
    @{
        ViewData["TooltipText"] = "This tooltip appears after a delay";
        ViewData["TooltipDelay"] = "500"; // milliseconds
    }
    <span data-tooltip="@ViewData["TooltipText"]" data-tooltip-delay="@ViewData["TooltipDelay"]">
        Delayed tooltip
    </span>
*@

<script>
    // Standardized Tooltip System
    window.TooltipSystem = window.TooltipSystem || {
        tooltips: new Map(),
        
        init: function() {
            this.initializeTooltips();
            this.setupEventListeners();
        },
        
        initializeTooltips: function() {
            // Initialize simple text tooltips
            document.querySelectorAll('[data-tooltip]:not([data-tooltip-initialized])').forEach(element => {
                this.attachTooltip(element);
            });
            
            // Initialize rich HTML tooltips
            document.querySelectorAll('[data-tooltip-html]:not([data-tooltip-initialized])').forEach(element => {
                this.attachTooltip(element, true);
            });
        },
        
        attachTooltip: function(element, isHtml = false) {
            element.setAttribute('data-tooltip-initialized', 'true');
            
            const tooltipText = isHtml ? element.getAttribute('data-tooltip-html') : element.getAttribute('data-tooltip');
            const position = element.getAttribute('data-tooltip-position') || 'top';
            const theme = element.getAttribute('data-tooltip-theme') || 'dark';
            const delay = parseInt(element.getAttribute('data-tooltip-delay')) || 0;
            
            let showTimeout, hideTimeout;
            
            element.addEventListener('mouseenter', (e) => {
                clearTimeout(hideTimeout);
                showTimeout = setTimeout(() => {
                    this.showTooltip(e.target, tooltipText, position, theme, isHtml);
                }, delay);
            });
            
            element.addEventListener('mouseleave', (e) => {
                clearTimeout(showTimeout);
                hideTimeout = setTimeout(() => {
                    this.hideTooltip(e.target);
                }, 100);
            });
            
            // Handle focus for accessibility
            element.addEventListener('focus', (e) => {
                this.showTooltip(e.target, tooltipText, position, theme, isHtml);
            });
            
            element.addEventListener('blur', (e) => {
                this.hideTooltip(e.target);
            });
        },
        
        showTooltip: function(element, content, position, theme, isHtml) {
            // Remove existing tooltip for this element
            this.hideTooltip(element);
            
            const tooltip = document.createElement('div');
            const tooltipId = 'tooltip-' + Date.now();
            tooltip.id = tooltipId;
            
            // Base classes
            tooltip.className = `fixed z-50 px-3 py-2 text-sm rounded-lg shadow-lg pointer-events-none transition-opacity duration-200 opacity-0`;
            
            // Theme classes
            if (theme === 'light') {
                tooltip.className += ' bg-white text-neutral-900 border border-neutral-200 dark:border-dark-600';
            } else {
                tooltip.className += ' bg-neutral-900 text-white dark:bg-dark-800';
            }
            
            // Set content
            if (isHtml) {
                tooltip.innerHTML = content;
            } else {
                tooltip.textContent = content;
            }
            
            // Add arrow
            const arrow = document.createElement('div');
            arrow.className = 'absolute w-2 h-2 transform rotate-45';
            
            if (theme === 'light') {
                arrow.className += ' bg-white border border-neutral-200 dark:border-dark-600';
            } else {
                arrow.className += ' bg-neutral-900 dark:bg-dark-800';
            }
            
            tooltip.appendChild(arrow);
            document.body.appendChild(tooltip);
            
            // Position tooltip
            this.positionTooltip(element, tooltip, arrow, position);
            
            // Show tooltip
            requestAnimationFrame(() => {
                tooltip.classList.remove('opacity-0');
                tooltip.classList.add('opacity-100');
            });
            
            // Store tooltip reference
            this.tooltips.set(element, tooltipId);
        },
        
        hideTooltip: function(element) {
            const tooltipId = this.tooltips.get(element);
            if (tooltipId) {
                const tooltip = document.getElementById(tooltipId);
                if (tooltip) {
                    tooltip.classList.remove('opacity-100');
                    tooltip.classList.add('opacity-0');
                    setTimeout(() => {
                        if (tooltip.parentNode) {
                            tooltip.parentNode.removeChild(tooltip);
                        }
                    }, 200);
                }
                this.tooltips.delete(element);
            }
        },
        
        positionTooltip: function(element, tooltip, arrow, position) {
            const rect = element.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            const arrowSize = 8;
            
            let top, left, arrowTop, arrowLeft;
            
            switch (position) {
                case 'top':
                    top = rect.top - tooltipRect.height - arrowSize;
                    left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
                    arrowTop = tooltipRect.height - 4;
                    arrowLeft = (tooltipRect.width / 2) - 4;
                    arrow.style.borderTop = theme === 'light' ? 'none' : 'none';
                    arrow.style.borderLeft = theme === 'light' ? 'none' : 'none';
                    break;
                    
                case 'bottom':
                    top = rect.bottom + arrowSize;
                    left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
                    arrowTop = -4;
                    arrowLeft = (tooltipRect.width / 2) - 4;
                    arrow.style.borderBottom = theme === 'light' ? 'none' : 'none';
                    arrow.style.borderRight = theme === 'light' ? 'none' : 'none';
                    break;
                    
                case 'left':
                    top = rect.top + (rect.height / 2) - (tooltipRect.height / 2);
                    left = rect.left - tooltipRect.width - arrowSize;
                    arrowTop = (tooltipRect.height / 2) - 4;
                    arrowLeft = tooltipRect.width - 4;
                    arrow.style.borderTop = theme === 'light' ? 'none' : 'none';
                    arrow.style.borderRight = theme === 'light' ? 'none' : 'none';
                    break;
                    
                case 'right':
                    top = rect.top + (rect.height / 2) - (tooltipRect.height / 2);
                    left = rect.right + arrowSize;
                    arrowTop = (tooltipRect.height / 2) - 4;
                    arrowLeft = -4;
                    arrow.style.borderBottom = theme === 'light' ? 'none' : 'none';
                    arrow.style.borderLeft = theme === 'light' ? 'none' : 'none';
                    break;
                    
                default:
                    top = rect.top - tooltipRect.height - arrowSize;
                    left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
                    arrowTop = tooltipRect.height - 4;
                    arrowLeft = (tooltipRect.width / 2) - 4;
            }
            
            // Ensure tooltip stays within viewport
            const padding = 10;
            if (left < padding) {
                left = padding;
            } else if (left + tooltipRect.width > window.innerWidth - padding) {
                left = window.innerWidth - tooltipRect.width - padding;
            }
            
            if (top < padding) {
                top = padding;
            } else if (top + tooltipRect.height > window.innerHeight - padding) {
                top = window.innerHeight - tooltipRect.height - padding;
            }
            
            tooltip.style.top = top + window.scrollY + 'px';
            tooltip.style.left = left + window.scrollX + 'px';
            arrow.style.top = arrowTop + 'px';
            arrow.style.left = arrowLeft + 'px';
        },
        
        setupEventListeners: function() {
            // Hide tooltips on scroll
            window.addEventListener('scroll', () => {
                this.tooltips.forEach((tooltipId, element) => {
                    this.hideTooltip(element);
                });
            });
            
            // Hide tooltips on resize
            window.addEventListener('resize', () => {
                this.tooltips.forEach((tooltipId, element) => {
                    this.hideTooltip(element);
                });
            });
            
            // Hide tooltips on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.tooltips.forEach((tooltipId, element) => {
                        this.hideTooltip(element);
                    });
                }
            });
        },
        
        // Public API for creating tooltips programmatically
        create: function(element, options) {
            const {
                text = '',
                html = '',
                position = 'top',
                theme = 'dark',
                delay = 0
            } = options;
            
            if (html) {
                element.setAttribute('data-tooltip-html', html);
            } else {
                element.setAttribute('data-tooltip', text);
            }
            
            element.setAttribute('data-tooltip-position', position);
            element.setAttribute('data-tooltip-theme', theme);
            
            if (delay > 0) {
                element.setAttribute('data-tooltip-delay', delay.toString());
            }
            
            this.attachTooltip(element, !!html);
        },
        
        // Remove tooltip from element
        destroy: function(element) {
            this.hideTooltip(element);
            element.removeAttribute('data-tooltip');
            element.removeAttribute('data-tooltip-html');
            element.removeAttribute('data-tooltip-position');
            element.removeAttribute('data-tooltip-theme');
            element.removeAttribute('data-tooltip-delay');
            element.removeAttribute('data-tooltip-initialized');
        }
    };

    // Auto-initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        window.TooltipSystem.init();
    });

    // Re-initialize when new content is added
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        setTimeout(() => window.TooltipSystem.initializeTooltips(), 100);
                    }
                });
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Global helper functions
    function addTooltip(element, text, position = 'top', theme = 'dark') {
        window.TooltipSystem.create(element, { text, position, theme });
    }

    function addRichTooltip(element, html, position = 'top', theme = 'dark') {
        window.TooltipSystem.create(element, { html, position, theme });
    }

    function removeTooltip(element) {
        window.TooltipSystem.destroy(element);
    }
</script>
