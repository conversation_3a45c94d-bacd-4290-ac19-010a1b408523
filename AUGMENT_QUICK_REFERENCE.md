# PM.Tool - Augment Quick Reference

## Essential Commands

### Database Operations
```bash
# Add migration
dotnet ef migrations add Migration<PERSON>ame

# Update database
dotnet ef database update

# Remove last migration
dotnet ef migrations remove

# Generate SQL script
dotnet ef migrations script
```

### Running the Application
```bash
# Run in development
dotnet run

# Run tests
dotnet test

# Build CSS (TailwindCSS)
npm run build-css

# Build for production
dotnet build --configuration Release
```

## Common Code Patterns

### 1. Creating a New Entity
```csharp
// 1. Entity in Core/Entities/
public class MyEntity : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [Encrypted(SearchableHash = true)]  // For sensitive data
    public string? SensitiveData { get; set; }
}

// 2. Add to ApplicationDbContext
public DbSet<MyEntity> MyEntities { get; set; }
```

### 2. Repository Pattern
```csharp
// Interface in Core/Interfaces/
public interface IMyEntityRepository : IRepository<MyEntity>
{
    Task<IEnumerable<MyEntity>> GetByProjectIdAsync(int projectId);
}

// Implementation in Infrastructure/Repositories/
public class MyEntityRepository : Repository<MyEntity>, IMyEntityRepository
{
    public MyEntityRepository(ApplicationDbContext context) : base(context) { }
    
    public async Task<IEnumerable<MyEntity>> GetByProjectIdAsync(int projectId)
    {
        return await _dbSet.Where(e => e.ProjectId == projectId && !e.IsDeleted)
                          .ToListAsync();
    }
}
```

### 3. Service Pattern
```csharp
// Interface in Core/Interfaces/
public interface IMyEntityService
{
    Task<MyEntity?> GetByIdAsync(int id);
    Task<MyEntity> CreateAsync(MyEntity entity);
    Task UpdateAsync(MyEntity entity);
    Task DeleteAsync(int id);
}

// Implementation in Application/Services/
public class MyEntityService : IMyEntityService
{
    private readonly IMyEntityRepository _repository;
    private readonly IAuditService _auditService;
    
    public MyEntityService(IMyEntityRepository repository, IAuditService auditService)
    {
        _repository = repository;
        _auditService = auditService;
    }
    
    public async Task<MyEntity> CreateAsync(MyEntity entity)
    {
        var result = await _repository.AddAsync(entity);
        await _repository.SaveChangesAsync();
        
        // Log audit
        await _auditService.LogAsync(AuditAction.Create, "MyEntity", result.Id);
        
        return result;
    }
}
```

### 4. Controller Pattern
```csharp
[Authorize]
public class MyEntityController : SecureBaseController
{
    private readonly IMyEntityService _service;
    private readonly IAuthorizationService _authorizationService;
    
    public MyEntityController(IMyEntityService service, IAuthorizationService authorizationService)
    {
        _service = service;
        _authorizationService = authorizationService;
    }
    
    public async Task<IActionResult> Index()
    {
        var entities = await _service.GetAllAsync();
        return View(entities);
    }
    
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(MyEntityCreateViewModel model)
    {
        if (ModelState.IsValid)
        {
            var entity = model.ToEntity();
            await _service.CreateAsync(entity);
            
            TempData["SuccessMessage"] = "Entity created successfully!";
            return RedirectToAction(nameof(Index));
        }
        
        return View(model);
    }
}
```

### 5. ViewModel Pattern
```csharp
public class MyEntityCreateViewModel
{
    [Required]
    [Display(Name = "Entity Name")]
    public string Name { get; set; } = string.Empty;
    
    public int ProjectId { get; set; }
    
    public MyEntity ToEntity()
    {
        return new MyEntity
        {
            Name = Name,
            ProjectId = ProjectId
        };
    }
}
```

### 6. Service Registration in Program.cs
```csharp
// Repository
builder.Services.AddScoped<IMyEntityRepository, MyEntityRepository>();

// Service
builder.Services.AddScoped<IMyEntityService, MyEntityService>();
```

## View Patterns

### 1. Standard Page Structure
```html
@{
    ViewData["Title"] = "My Entity";
    ViewData["PageTitle"] = "My Entity Management";
    ViewData["PageDescription"] = "Manage your entities";
    ViewData["PageIcon"] = "fas fa-cube";
}

<partial name="Components/_PageHeader" view-data="ViewData" />

<div class="container-fluid">
    <!-- Page content -->
</div>

@section Scripts {
    <partial name="MyEntity/Partials/_MyEntityScripts" />
}
```

### 2. Using Shared Components
```html
<!-- Page Header -->
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Data Table -->
<partial name="Components/_DataTable" model="Model.Items" />

<!-- Modal -->
<partial name="Components/_Modal" model="@(new { Id = "myModal", Title = "Create Entity" })" />

<!-- Alert -->
<partial name="Components/_Alert" model="@(new { Type = "success", Message = "Success!" })" />
```

## Authorization Patterns

### 1. Policy-Based Authorization
```csharp
// In controller
[Authorize(Policy = "ProjectMember")]
public async Task<IActionResult> Details(int id)
{
    var entity = await _service.GetByIdAsync(id);
    
    // Resource-based authorization
    var authResult = await _authorizationService.AuthorizeAsync(User, entity, "CanView");
    if (!authResult.Succeeded)
    {
        return Forbid();
    }
    
    return View(entity);
}
```

### 2. Custom Authorization Handler
```csharp
public class MyEntityAuthorizationHandler : AuthorizationHandler<OperationAuthorizationRequirement, MyEntity>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context,
        OperationAuthorizationRequirement requirement, MyEntity resource)
    {
        // Authorization logic here
        if (CanUserAccessResource(context.User, resource, requirement))
        {
            context.Succeed(requirement);
        }
        
        return Task.CompletedTask;
    }
}
```

## Real-time Updates with SignalR

### 1. Broadcasting Updates
```csharp
// In service or controller
await _realTimeService.BroadcastToProjectAsync(
    projectId,
    "EntityUpdated",
    new { EntityId = entity.Id, Title = entity.Name });
```

### 2. JavaScript Client
```javascript
// Connect to hub
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/collaborationHub")
    .build();

// Listen for updates
connection.on("EntityUpdated", function (data) {
    // Update UI
    updateEntityInList(data.EntityId, data.Title);
});
```

## Common Validation Patterns

### 1. FluentValidation
```csharp
public class MyEntityValidator : AbstractValidator<MyEntityCreateViewModel>
{
    public MyEntityValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Name is required")
            .MaximumLength(200).WithMessage("Name cannot exceed 200 characters");
            
        RuleFor(x => x.ProjectId)
            .GreaterThan(0).WithMessage("Please select a project");
    }
}
```

## Testing Patterns

### 1. Unit Test Structure
```csharp
[Test]
public async Task CreateAsync_ValidEntity_ReturnsCreatedEntity()
{
    // Arrange
    var entity = new MyEntity { Name = "Test Entity" };
    _mockRepository.Setup(r => r.AddAsync(It.IsAny<MyEntity>()))
                   .ReturnsAsync(entity);
    
    // Act
    var result = await _service.CreateAsync(entity);
    
    // Assert
    Assert.That(result.Name, Is.EqualTo("Test Entity"));
    _mockAuditService.Verify(a => a.LogAsync(AuditAction.Create, "MyEntity", It.IsAny<int>()), Times.Once);
}
```

## Key Configuration Settings

### 1. Connection String (appsettings.json)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=PMToolDB;Username=user;Password=****"
  }
}
```

### 2. Common AppSettings
```json
{
  "DataEncryption": {
    "EnableEncryption": true,
    "Key": "32-byte-key-here"
  },
  "FileUpload": {
    "MaxFileSize": 10485760,
    "AllowedExtensions": [".jpg", ".pdf", ".docx"]
  }
}
```

## Troubleshooting Common Issues

1. **Migration Issues**: Check entity configurations and relationships
2. **Authorization Failures**: Verify policy registration and user roles
3. **SignalR Connection Issues**: Check hub registration and client connection
4. **Validation Errors**: Ensure FluentValidation is properly configured
5. **Encryption Issues**: Verify encryption key configuration

## Useful Debugging Commands

```bash
# Check EF migrations
dotnet ef migrations list

# View database schema
dotnet ef dbcontext info

# Generate model from database
dotnet ef dbcontext scaffold "connection-string" Npgsql.EntityFrameworkCore.PostgreSQL
```

This quick reference should help you quickly implement common patterns and resolve typical issues in the PM.Tool project.
