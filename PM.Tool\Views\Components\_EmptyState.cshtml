@{
    var icon = ViewData["Icon"]?.ToString() ?? "fas fa-inbox";
    var title = ViewData["Title"]?.ToString() ?? "No Items";
    var description = ViewData["Description"]?.ToString() ?? "";
    var actionText = ViewData["ActionText"]?.ToString() ?? "";
    var actionIcon = ViewData["ActionIcon"]?.ToString() ?? "";
    var actionHref = ViewData["ActionHref"]?.ToString() ?? "";
    var actionVariant = ViewData["ActionVariant"]?.ToString() ?? "primary";
}

<div class="text-center py-12">
    <div class="w-16 h-16 bg-neutral-100 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="@icon text-2xl text-neutral-400 dark:text-neutral-500"></i>
    </div>
    
    <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">@title</h3>
    
    @if (!string.IsNullOrEmpty(description))
    {
        <p class="text-neutral-500 dark:text-neutral-400 mb-6 max-w-sm mx-auto">@description</p>
    }
    
    @if (!string.IsNullOrEmpty(actionText) && !string.IsNullOrEmpty(actionHref))
    {
        ViewData["Text"] = actionText;
        ViewData["Variant"] = actionVariant;
        ViewData["Icon"] = actionIcon;
        ViewData["Href"] = actionHref;
        <partial name="Components/_Button" view-data="ViewData" />
    }
</div>
