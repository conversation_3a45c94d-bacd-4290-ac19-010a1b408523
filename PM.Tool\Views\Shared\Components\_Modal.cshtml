@*
    Standardized Modal Component - Usage Examples:

    1. Basic modal:
    @{
        ViewData["ModalId"] = "exampleModal";
        ViewData["ModalTitle"] = "Example Modal";
        ViewData["ModalSize"] = "md"; // sm, md, lg, xl, full
        ViewData["BodyContent"] = "<p>Modal content goes here</p>";
        ViewData["ShowFooter"] = true;
        ViewData["FooterContent"] = "<button onclick='closeModal(\"exampleModal\")' class='btn-secondary'>Cancel</button>";
    }
    <partial name="Components/_Modal" view-data="ViewData" />

    2. Form modal:
    @{
        ViewData["ModalId"] = "formModal";
        ViewData["ModalTitle"] = "Create New Item";
        ViewData["ModalSize"] = "lg";
        ViewData["BodyContent"] = Html.Partial("_FormContent").ToString();
        ViewData["ShowFooter"] = true;
        ViewData["FooterContent"] = "<button type='submit' class='btn-primary'>Save</button><button onclick='closeModal(\"formModal\")' class='btn-secondary'>Cancel</button>";
    }
    <partial name="Components/_Modal" view-data="ViewData" />
*@

@model dynamic

@{
    var modalId = ViewData["ModalId"]?.ToString() ?? "modal";
    var title = ViewData["ModalTitle"]?.ToString() ?? "Modal";
    var size = ViewData["ModalSize"]?.ToString() ?? "md"; // sm, md, lg, xl, full
    var showFooter = ViewData["ShowFooter"] as bool? ?? true;
    var footerContent = ViewData["FooterContent"]?.ToString();
    var bodyContent = ViewData["BodyContent"]?.ToString() ?? "";
    var showCloseButton = ViewData["ShowCloseButton"] as bool? ?? true;
    var preventBackdropClose = ViewData["PreventBackdropClose"] as bool? ?? false;

    var sizeClasses = size switch {
        "sm" => "max-w-md",
        "md" => "max-w-lg",
        "lg" => "max-w-2xl",
        "xl" => "max-w-4xl",
        "full" => "max-w-7xl",
        _ => "max-w-lg"
    };
}

<!-- Standardized Modal -->
<div id="@modalId" class="fixed inset-0 z-50 overflow-y-auto hidden" role="dialog" aria-labelledby="@(modalId)-title" aria-hidden="true">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @(preventBackdropClose ? "" : $"onclick=\"closeModal('{modalId}')\"")></div>

    <!-- Modal Content -->
    <div class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="@sizeClasses w-full bg-white dark:bg-surface-dark rounded-xl shadow-strong border border-neutral-200 dark:border-dark-200 transform transition-all duration-300 scale-95 opacity-0" id="@(modalId)-content">

                <!-- Header -->
                @if (showCloseButton || !string.IsNullOrEmpty(title))
                {
                    <div class="flex items-center justify-between p-6 border-b border-neutral-200 dark:border-dark-600">
                        @if (!string.IsNullOrEmpty(title))
                        {
                            <h3 id="@(modalId)-title" class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                                @title
                            </h3>
                        }
                        @if (showCloseButton)
                        {
                            <button type="button"
                                    class="text-neutral-400 dark:text-dark-500 hover:text-neutral-600 dark:hover:text-dark-300 transition-colors p-1 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700"
                                    onclick="closeModal('@modalId')"
                                    aria-label="Close modal">
                                <i class="fas fa-times text-sm"></i>
                            </button>
                        }
                    </div>
                }

                <!-- Body -->
                <div class="p-6 @(showFooter ? "pb-4" : "")">
                    @if (!string.IsNullOrEmpty(bodyContent))
                    {
                        @Html.Raw(bodyContent)
                    }
                </div>

                <!-- Footer -->
                @if (showFooter)
                {
                    <div class="flex items-center justify-end space-x-3 px-6 py-4 border-t border-neutral-200 dark:border-dark-600 bg-neutral-50 dark:bg-dark-800 rounded-b-xl">
                        @if (!string.IsNullOrEmpty(footerContent))
                        {
                            @Html.Raw(footerContent)
                        }
                        else
                        {
                            <button type="button" class="btn-secondary-custom" onclick="closeModal('@modalId')">
                                Cancel
                            </button>
                            <button type="button" class="btn-primary-custom">
                                Confirm
                            </button>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<script>
    // Standardized Modal System
    window.ModalSystem = window.ModalSystem || {
        openModals: new Set(),

        open: function(modalId) {
            const modal = document.getElementById(modalId);
            const content = document.getElementById(modalId + '-content');

            if (modal && content) {
                // Add to open modals set
                this.openModals.add(modalId);

                modal.classList.remove('hidden');
                modal.setAttribute('aria-hidden', 'false');

                // Trigger animation
                requestAnimationFrame(() => {
                    content.classList.remove('scale-95', 'opacity-0');
                    content.classList.add('scale-100', 'opacity-100');
                });

                // Focus management
                const firstFocusable = content.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                if (firstFocusable) {
                    setTimeout(() => firstFocusable.focus(), 100);
                }

                // Prevent body scroll
                document.body.style.overflow = 'hidden';

                // Dispatch custom event
                modal.dispatchEvent(new CustomEvent('modal:opened', { detail: { modalId } }));
            }
        },

        close: function(modalId) {
            const modal = document.getElementById(modalId);
            const content = document.getElementById(modalId + '-content');

            if (modal && content) {
                // Remove from open modals set
                this.openModals.delete(modalId);

                // Trigger animation
                content.classList.add('scale-95', 'opacity-0');
                content.classList.remove('scale-100', 'opacity-100');

                setTimeout(() => {
                    modal.classList.add('hidden');
                    modal.setAttribute('aria-hidden', 'true');

                    // Restore body scroll only if no other modals are open
                    if (this.openModals.size === 0) {
                        document.body.style.overflow = '';
                    }
                }, 300);

                // Dispatch custom event
                modal.dispatchEvent(new CustomEvent('modal:closed', { detail: { modalId } }));
            }
        },

        closeAll: function() {
            this.openModals.forEach(modalId => this.close(modalId));
        }
    };

    // Global functions for backward compatibility
    function openModal(modalId) {
        window.ModalSystem.open(modalId);
    }

    function closeModal(modalId) {
        window.ModalSystem.close(modalId);
    }

    // Enhanced keyboard handling
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && window.ModalSystem.openModals.size > 0) {
            // Close the most recently opened modal
            const lastModal = Array.from(window.ModalSystem.openModals).pop();
            if (lastModal) {
                window.ModalSystem.close(lastModal);
            }
        }
    });
</script>
