using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Logging;
using MockQueryable.Moq;
using Moq;
using PM.Tool.Controllers;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Localization;
using PM.Tool.Models.ViewModels;
using PM.Tool.Services;
using PM.Tool.Tests.Helpers;
using PM.Tool.Data;
using System.Security.Claims;
using Xunit;
using FluentAssertions;
using System.Linq;

namespace PM.Tool.Tests.Unit.Controllers
{
    public class AgileControllerTests
    {
        private readonly Mock<IAgileService> _mockAgileService;
        private readonly Mock<IProjectService> _mockProjectService;
        private readonly Mock<ILocalizationService> _mockLocalizationService;
        private readonly Mock<UserManager<ApplicationUser>> _mockUserManager;
        private readonly Mock<ILogger<AgileController>> _mockLogger;
        private readonly Mock<IFormHelperService> _mockFormHelper;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly Mock<ApplicationDbContext> _mockContext;
        private readonly AgileController _controller;
        private readonly ApplicationUser _testUser;

        public AgileControllerTests()
        {
            _mockAgileService = new Mock<IAgileService>();
            _mockProjectService = new Mock<IProjectService>();
            _mockLocalizationService = new Mock<ILocalizationService>();
            _mockUserManager = MockHelper.CreateMockUserManager();
            _mockLogger = MockHelper.CreateMockLogger<AgileController>();
            _mockFormHelper = new Mock<IFormHelperService>();
            _mockAuditService = MockHelper.CreateMockAuditService();
            _mockContext = new Mock<ApplicationDbContext>();

            _controller = new AgileController(
                _mockAgileService.Object,
                _mockProjectService.Object,
                _mockLocalizationService.Object,
                _mockUserManager.Object,
                _mockLogger.Object,
                _mockFormHelper.Object,
                _mockContext.Object,
                _mockAuditService.Object);

            _testUser = new ApplicationUser
            {
                Id = "test-user-1",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                IsActive = true
            };

            MockHelper.SetupControllerContext(_controller);

            // Setup TempData
            _controller.TempData = new TempDataDictionary(_controller.ControllerContext.HttpContext, Mock.Of<ITempDataProvider>());

            // Setup common localization strings
            _mockLocalizationService.Setup(x => x.GetString("Message.ErrorOccurred"))
                .Returns("An error occurred");
            _mockLocalizationService.Setup(x => x.GetString("Message.SaveSuccess"))
                .Returns("Saved successfully");
            _mockLocalizationService.Setup(x => x.GetString("Message.DeleteSuccess"))
                .Returns("Deleted successfully");
        }

        [Fact]
        public async Task Index_WithValidUser_ReturnsViewWithEpics()
        {
            // Arrange
            var projects = new List<Project>
            {
                new Project { Id = 1, Name = "Project 1" },
                new Project { Id = 2, Name = "Project 2" }
            };

            var epics = new List<Epic>
            {
                new Epic
                {
                    Id = 1,
                    Title = "Epic 1",
                    Description = "Epic 1 Description",
                    ProjectId = 1,
                    Status = EpicStatus.InProgress,
                    CreatedAt = DateTime.UtcNow.AddDays(-5)
                },
                new Epic
                {
                    Id = 2,
                    Title = "Epic 2",
                    Description = "Epic 2 Description",
                    ProjectId = 2,
                    Status = EpicStatus.Draft,
                    CreatedAt = DateTime.UtcNow.AddDays(-3)
                }
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetUserProjectsAsync(_testUser.Id))
                .ReturnsAsync(projects);
            _mockAgileService.Setup(x => x.GetProjectEpicsAsync(1))
                .ReturnsAsync(new List<Epic> { epics[0] });
            _mockAgileService.Setup(x => x.GetProjectEpicsAsync(2))
                .ReturnsAsync(new List<Epic> { epics[1] });

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeAssignableTo<IEnumerable<Epic>>();
            var model = (viewResult.Model as IEnumerable<Epic>)!.ToList();
            model.Should().HaveCount(2);
            model[0].Title.Should().Be("Epic 2"); // Ordered by CreatedAt descending (Epic 2 is newer)
            model[1].Title.Should().Be("Epic 1");
            viewResult.ViewData["HasProjects"].Should().Be(true);
            viewResult.ViewData["ProjectCount"].Should().Be(2);
            viewResult.ViewData["UserProjects"].Should().Be(projects);
        }

        [Fact]
        public async Task Index_WithNoProjects_ReturnsEmptyView()
        {
            // Arrange
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetUserProjectsAsync(_testUser.Id))
                .ReturnsAsync(new List<Project>());

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeAssignableTo<IEnumerable<Epic>>();
            var model = (viewResult.Model as IEnumerable<Epic>)!.ToList();
            model.Should().BeEmpty();
            viewResult.ViewData["HasProjects"].Should().Be(false);
        }

        [Fact]
        public async Task Index_WithNullUser_RedirectsToLogin()
        {
            // Arrange
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<ClaimsPrincipal>()))
                .ReturnsAsync((ApplicationUser?)null);

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Login");
            redirectResult.ControllerName.Should().Be("Account");
        }

        [Fact]
        public async Task Index_WithException_ReturnsEmptyViewWithError()
        {
            // Arrange
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetUserProjectsAsync(_testUser.Id))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeAssignableTo<IEnumerable<Epic>>();
            var model = (viewResult.Model as IEnumerable<Epic>)!.ToList();
            model.Should().BeEmpty();
            _controller.TempData["Error"].Should().Be("An error occurred");
        }

        [Fact]
        public async Task Backlog_WithValidProject_ReturnsViewWithUserStories()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };
            var epics = new List<Epic>
            {
                new Epic { Id = 1, Title = "Epic 1", ProjectId = projectId }
            };
            var userStories = new List<UserStory>
            {
                new UserStory
                {
                    Id = 1,
                    Title = "User Story 1",
                    ProjectId = projectId,
                    Status = UserStoryStatus.Backlog
                }
            };
            var sprints = new List<Sprint>
            {
                new Sprint { Id = 1, Name = "Sprint 1", ProjectId = projectId }
            };

            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockAgileService.Setup(x => x.GetProjectEpicsAsync(projectId))
                .ReturnsAsync(epics);
            _mockAgileService.Setup(x => x.GetBacklogUserStoriesAsync(projectId))
                .ReturnsAsync(userStories);
            _mockAgileService.Setup(x => x.GetProjectSprintsAsync(projectId))
                .ReturnsAsync(sprints);

            // Act
            var result = await _controller.Backlog(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<UserStory>>();
            var model = viewResult.Model as List<UserStory>;
            model!.Should().HaveCount(1);
            model[0].Title.Should().Be("User Story 1");
            viewResult.ViewData["Project"].Should().Be(project);
            viewResult.ViewData["Epics"].Should().Be(epics);
            viewResult.ViewData["Sprints"].Should().Be(sprints);
            viewResult.ViewData["ProjectId"].Should().Be(projectId);
        }

        [Fact]
        public async Task Backlog_WithNonExistentProject_ReturnsNotFound()
        {
            // Arrange
            var projectId = 999;
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync((Project?)null);

            // Act
            var result = await _controller.Backlog(projectId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task Backlog_WithException_RedirectsToProjectDetails()
        {
            // Arrange
            var projectId = 1;
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Backlog(projectId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Details");
            redirectResult.ControllerName.Should().Be("Projects");
            redirectResult.RouteValues!["id"].Should().Be(projectId);
            _controller.TempData["Error"].Should().Be("An error occurred");
        }

        [Fact]
        public async Task Kanban_WithValidProject_ReturnsViewWithKanbanBoard()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };
            var kanbanBoard = new Dictionary<int, IEnumerable<UserStory>>
            {
                { 1, new List<UserStory>() },
                { 2, new List<UserStory>() },
                { 3, new List<UserStory>() }
            };
            var sprints = new List<Sprint>
            {
                new Sprint { Id = 1, Name = "Sprint 1", ProjectId = projectId }
            };
            var activeSprint = new Sprint { Id = 1, Name = "Active Sprint", ProjectId = projectId, Status = SprintStatus.Active };

            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockAgileService.Setup(x => x.GetKanbanBoardAsync(projectId))
                .ReturnsAsync(kanbanBoard);
            _mockAgileService.Setup(x => x.GetProjectSprintsAsync(projectId))
                .ReturnsAsync(sprints);
            _mockAgileService.Setup(x => x.GetActiveSprintAsync(projectId))
                .ReturnsAsync(activeSprint);

            // Act
            var result = await _controller.Kanban(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().Be(kanbanBoard);
            viewResult.ViewData["Project"].Should().Be(project);
            viewResult.ViewData["Sprints"].Should().Be(sprints);
            viewResult.ViewData["ActiveSprint"].Should().Be(activeSprint);
            viewResult.ViewData["ProjectId"].Should().Be(projectId);
        }

        [Fact]
        public async Task Kanban_WithNonExistentProject_ReturnsNotFound()
        {
            // Arrange
            var projectId = 999;
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync((Project?)null);

            // Act
            var result = await _controller.Kanban(projectId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task Epics_WithValidProject_ReturnsViewWithEpics()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };
            var epics = new List<Epic>
            {
                new Epic
                {
                    Id = 1,
                    Title = "Epic 1",
                    ProjectId = projectId,
                    Status = EpicStatus.InProgress
                }
            };
            var analytics = new Dictionary<string, object>
            {
                { "totalEpics", 1 },
                { "completedEpics", 0 }
            };

            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockAgileService.Setup(x => x.GetProjectEpicsAsync(projectId))
                .ReturnsAsync(epics);
            _mockAgileService.Setup(x => x.GetAgileAnalyticsAsync(projectId))
                .ReturnsAsync(analytics);

            // Act
            var result = await _controller.Epics(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<Epic>>();
            var model = viewResult.Model as List<Epic>;
            model!.Should().HaveCount(1);
            model[0].Title.Should().Be("Epic 1");
            viewResult.ViewData["Project"].Should().Be(project);
            viewResult.ViewData["Analytics"].Should().Be(analytics);
            viewResult.ViewData["ProjectId"].Should().Be(projectId);
        }

        [Fact]
        public async Task Epics_WithNonExistentProject_ReturnsNotFound()
        {
            // Arrange
            var projectId = 999;
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync((Project?)null);

            // Act
            var result = await _controller.Epics(projectId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task EpicDetails_WithValidEpic_ReturnsViewWithDetails()
        {
            // Arrange
            var epicId = 1;
            var epic = new Epic
            {
                Id = epicId,
                Title = "Test Epic",
                Description = "Test Description",
                ProjectId = 1,
                Project = new Project { Id = 1, Name = "Test Project" }
            };
            var userStories = new List<UserStory>
            {
                new UserStory
                {
                    Id = 1,
                    Title = "User Story 1",
                    StoryPoints = 5,
                    Status = UserStoryStatus.Done
                },
                new UserStory
                {
                    Id = 2,
                    Title = "User Story 2",
                    StoryPoints = 3,
                    Status = UserStoryStatus.InProgress
                }
            };

            _mockAgileService.Setup(x => x.GetEpicByIdAsync(epicId))
                .ReturnsAsync(epic);
            _mockAgileService.Setup(x => x.GetEpicUserStoriesAsync(epicId))
                .ReturnsAsync(userStories);

            // Act
            var result = await _controller.EpicDetails(epicId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<Epic>();
            var model = viewResult.Model as Epic;
            model!.Id.Should().Be(epicId);
            model.Title.Should().Be("Test Epic");
            viewResult.ViewData["UserStories"].Should().Be(userStories);
            viewResult.ViewData["Project"].Should().Be(epic.Project);
            viewResult.ViewData["TotalStoryPoints"].Should().Be(8m);
            viewResult.ViewData["CompletedStoryPoints"].Should().Be(5m);
            viewResult.ViewData["ProgressPercentage"].Should().Be(62.5);
        }

        [Fact]
        public async Task EpicDetails_WithNonExistentEpic_ReturnsNotFound()
        {
            // Arrange
            var epicId = 999;
            _mockAgileService.Setup(x => x.GetEpicByIdAsync(epicId))
                .ReturnsAsync((Epic?)null);

            // Act
            var result = await _controller.EpicDetails(epicId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task EpicDetails_WithException_RedirectsToIndex()
        {
            // Arrange
            var epicId = 1;
            _mockAgileService.Setup(x => x.GetEpicByIdAsync(epicId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.EpicDetails(epicId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Index");
            _controller.TempData["Error"].Should().Be("An error occurred");
        }

        [Fact]
        public async Task CreateEpic_Get_WithValidProject_ReturnsViewWithNewEpic()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };

            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);

            // Setup enum display names for dropdowns
            _mockLocalizationService.Setup(x => x.GetEnumDisplayNames<EpicStatus>())
                .Returns(new Dictionary<EpicStatus, string>
                {
                    { EpicStatus.Draft, "Draft" },
                    { EpicStatus.InProgress, "In Progress" }
                });
            _mockLocalizationService.Setup(x => x.GetEnumDisplayNames<EpicPriority>())
                .Returns(new Dictionary<EpicPriority, string>
                {
                    { EpicPriority.Low, "Low" },
                    { EpicPriority.Medium, "Medium" }
                });

            // Act
            var result = await _controller.CreateEpic(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<EpicCreateViewModel>();
            var model = viewResult.Model as EpicCreateViewModel;
            model!.ProjectId.Should().Be(projectId);
            viewResult.ViewData["Project"].Should().Be(project);
            viewResult.ViewData.Should().ContainKeys("EpicStatuses", "EpicPriorities");
        }

        [Fact]
        public async Task CreateEpic_Get_WithNonExistentProject_ReturnsNotFound()
        {
            // Arrange
            var projectId = 999;
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync((Project?)null);

            // Act
            var result = await _controller.CreateEpic(projectId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task CreateEpic_Post_WithValidModel_CreatesEpicAndRedirects()
        {
            // Arrange
            var epic = new Epic
            {
                Title = "New Epic",
                Description = "New Description",
                ProjectId = 1
            };

            var existingEpics = new List<Epic>
            {
                new Epic { Id = 1, EpicKey = "EP-001" }
            };

            var createdEpic = new Epic
            {
                Id = 2,
                Title = epic.Title,
                Description = epic.Description,
                ProjectId = epic.ProjectId,
                EpicKey = "EP-002"
            };

            // Setup User.FindFirstValue for owner assignment
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockAgileService.Setup(x => x.GetProjectEpicsAsync(epic.ProjectId))
                .ReturnsAsync(existingEpics);
            _mockAgileService.Setup(x => x.CreateEpicAsync(It.IsAny<Epic>()))
                .ReturnsAsync(createdEpic);

            // Create ViewModel from Epic
            var viewModel = new EpicCreateViewModel
            {
                Title = epic.Title,
                Description = epic.Description,
                ProjectId = epic.ProjectId,
                Priority = epic.Priority
            };

            // Act
            var result = await _controller.CreateEpic(viewModel);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("EpicDetails");
            redirectResult.RouteValues!["id"].Should().Be(2);
            _controller.TempData["Success"].Should().Be("Saved successfully");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Create,
                "Epic",
                2,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task CreateEpic_Post_WithInvalidModel_ReturnsViewWithModel()
        {
            // Arrange
            var viewModel = new EpicCreateViewModel
            {
                // Missing Title - this will make ModelState invalid
                Description = "Test Description",
                ProjectId = 1
            };
            _controller.ModelState.AddModelError("Title", "Title is required");

            var project = new Project { Id = 1, Name = "Test Project" };
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(viewModel.ProjectId))
                .ReturnsAsync(project);

            // Setup enum display names for dropdowns
            _mockLocalizationService.Setup(x => x.GetEnumDisplayNames<EpicStatus>())
                .Returns(new Dictionary<EpicStatus, string>());
            _mockLocalizationService.Setup(x => x.GetEnumDisplayNames<EpicPriority>())
                .Returns(new Dictionary<EpicPriority, string>());

            // Act
            var result = await _controller.CreateEpic(viewModel);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().Be(viewModel);
            viewResult.ViewData["Project"].Should().Be(project);
        }

        [Fact]
        public async Task EditEpic_Get_WithValidEpic_ReturnsViewWithModel()
        {
            // Arrange
            var epicId = 1;
            var epic = new Epic
            {
                Id = epicId,
                Title = "Test Epic",
                Description = "Test Description",
                ProjectId = 1,
                Project = new Project { Id = 1, Name = "Test Project" }
            };

            _mockAgileService.Setup(x => x.GetEpicByIdAsync(epicId))
                .ReturnsAsync(epic);

            // Setup enum display names for dropdowns
            _mockLocalizationService.Setup(x => x.GetEnumDisplayNames<EpicStatus>())
                .Returns(new Dictionary<EpicStatus, string>());
            _mockLocalizationService.Setup(x => x.GetEnumDisplayNames<EpicPriority>())
                .Returns(new Dictionary<EpicPriority, string>());

            // Act
            var result = await _controller.EditEpic(epicId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<Epic>();
            var model = viewResult.Model as Epic;
            model!.Id.Should().Be(epicId);
            model.Title.Should().Be("Test Epic");
            viewResult.ViewData["Project"].Should().Be(epic.Project);
        }

        [Fact]
        public async Task EditEpic_Get_WithNonExistentEpic_ReturnsNotFound()
        {
            // Arrange
            var epicId = 999;
            _mockAgileService.Setup(x => x.GetEpicByIdAsync(epicId))
                .ReturnsAsync((Epic?)null);

            // Act
            var result = await _controller.EditEpic(epicId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task EditEpic_Post_WithValidModel_UpdatesEpicAndRedirects()
        {
            // Arrange
            var epicId = 1;
            var epic = new Epic
            {
                Id = epicId,
                Title = "Updated Epic",
                Description = "Updated Description",
                ProjectId = 1
            };

            // Setup User.FindFirstValue for audit logging
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockAgileService.Setup(x => x.UpdateEpicAsync(It.IsAny<Epic>()))
                .ReturnsAsync(epic);

            // Act
            var result = await _controller.EditEpic(epicId, epic);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("EpicDetails");
            redirectResult.RouteValues!["id"].Should().Be(epicId);
            _controller.TempData["Success"].Should().Be("Saved successfully");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Update,
                "Epic",
                epicId,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task EditEpic_Post_WithMismatchedId_ReturnsNotFound()
        {
            // Arrange
            var epicId = 1;
            var epic = new Epic
            {
                Id = 2, // Different ID
                Title = "Test Epic"
            };

            // Act
            var result = await _controller.EditEpic(epicId, epic);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task DeleteEpic_WithValidEpic_DeletesAndRedirectsToEpics()
        {
            // Arrange
            var epicId = 1;
            var epic = new Epic
            {
                Id = epicId,
                ProjectId = 1
            };

            // Setup User.FindFirstValue for audit logging
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockAgileService.Setup(x => x.GetEpicByIdAsync(epicId))
                .ReturnsAsync(epic);
            _mockAgileService.Setup(x => x.DeleteEpicAsync(epicId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteEpic(epicId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Epics");
            redirectResult.RouteValues!["projectId"].Should().Be(1);
            _controller.TempData["Success"].Should().Be("Deleted successfully");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Delete,
                "Epic",
                epicId,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task DeleteEpic_WithNonExistentEpic_ReturnsNotFound()
        {
            // Arrange
            var epicId = 999;
            _mockAgileService.Setup(x => x.GetEpicByIdAsync(epicId))
                .ReturnsAsync((Epic?)null);

            // Act
            var result = await _controller.DeleteEpic(epicId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task CreateUserStory_Get_WithValidProject_ReturnsViewWithNewUserStory()
        {
            // Arrange
            var projectId = 1;
            var epicId = 2;
            var sprintId = 3;
            var project = new Project { Id = projectId, Name = "Test Project" };
            var epics = new List<Epic>
            {
                new Epic { Id = epicId, Title = "Test Epic" }
            };
            var sprints = new List<Sprint>
            {
                new Sprint { Id = sprintId, Name = "Test Sprint" }
            };

            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockAgileService.Setup(x => x.GetProjectEpicsAsync(projectId))
                .ReturnsAsync(epics);
            _mockAgileService.Setup(x => x.GetProjectSprintsAsync(projectId))
                .ReturnsAsync(sprints);

            // Setup enum display names for dropdowns
            _mockLocalizationService.Setup(x => x.GetEnumDisplayNames<UserStoryStatus>())
                .Returns(new Dictionary<UserStoryStatus, string>());
            _mockLocalizationService.Setup(x => x.GetEnumDisplayNames<UserStoryPriority>())
                .Returns(new Dictionary<UserStoryPriority, string>());

            // Act
            var result = await _controller.CreateUserStory(projectId, epicId, sprintId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<UserStoryCreateViewModel>();
            var model = viewResult.Model as UserStoryCreateViewModel;
            model!.ProjectId.Should().Be(projectId);
            model.EpicId.Should().Be(epicId);
            model.SprintId.Should().Be(sprintId);
            viewResult.ViewData["Project"].Should().Be(project);
        }

        [Fact]
        public async Task CreateUserStory_Get_WithNonExistentProject_ReturnsNotFound()
        {
            // Arrange
            var projectId = 999;
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync((Project?)null);

            // Act
            var result = await _controller.CreateUserStory(projectId, null, null);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task CreateUserStory_Post_WithValidModel_CreatesUserStoryAndRedirects()
        {
            // Arrange
            var userStory = new UserStory
            {
                Title = "New User Story",
                Description = "New Description",
                AsA = "As a user",
                IWant = "I want functionality",
                SoThat = "So that I get value",
                ProjectId = 1
            };

            var existingUserStories = new List<UserStory>
            {
                new UserStory { Id = 1, StoryKey = "US-001" }
            };

            var createdUserStory = new UserStory
            {
                Id = 2,
                Title = userStory.Title,
                Description = userStory.Description,
                ProjectId = userStory.ProjectId,
                StoryKey = "US-002"
            };

            // Setup User.FindFirstValue for assignment
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockAgileService.Setup(x => x.GetProjectUserStoriesAsync(userStory.ProjectId))
                .ReturnsAsync(existingUserStories);
            _mockAgileService.Setup(x => x.CreateUserStoryAsync(It.IsAny<UserStory>()))
                .ReturnsAsync(createdUserStory);

            // Create ViewModel from UserStory
            var viewModel = new UserStoryCreateViewModel
            {
                Title = userStory.Title,
                Description = userStory.Description,
                AsA = userStory.AsA,
                IWant = userStory.IWant,
                SoThat = userStory.SoThat,
                ProjectId = userStory.ProjectId
            };

            // Act
            var result = await _controller.CreateUserStory(viewModel);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("UserStoryDetails");
            redirectResult.RouteValues!["id"].Should().Be(2);
            _controller.TempData["Success"].Should().Be("Saved successfully");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Create,
                "UserStory",
                2,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task UserStoryDetails_WithValidUserStory_ReturnsViewWithDetails()
        {
            // Arrange
            var userStoryId = 1;
            var userStory = new UserStory
            {
                Id = userStoryId,
                Title = "Test User Story",
                Description = "Test Description",
                ProjectId = 1,
                Project = new Project { Id = 1, Name = "Test Project" }
            };
            var comments = new List<UserStoryComment>
            {
                new UserStoryComment { Id = 1, Content = "Test comment", UserStoryId = userStoryId }
            };

            _mockAgileService.Setup(x => x.GetUserStoryByIdAsync(userStoryId))
                .ReturnsAsync(userStory);
            _mockAgileService.Setup(x => x.GetUserStoryCommentsAsync(userStoryId))
                .ReturnsAsync(comments);

            // Act
            var result = await _controller.UserStoryDetails(userStoryId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<UserStory>();
            var model = viewResult.Model as UserStory;
            model!.Id.Should().Be(userStoryId);
            model.Title.Should().Be("Test User Story");
            viewResult.ViewData["Comments"].Should().Be(comments);
            viewResult.ViewData["Project"].Should().Be(userStory.Project);
        }

        [Fact]
        public async Task UserStoryDetails_WithNonExistentUserStory_ReturnsNotFound()
        {
            // Arrange
            var userStoryId = 999;
            _mockAgileService.Setup(x => x.GetUserStoryByIdAsync(userStoryId))
                .ReturnsAsync((UserStory?)null);

            // Act
            var result = await _controller.UserStoryDetails(userStoryId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task CreateSprint_Get_WithValidProject_ReturnsViewWithNewSprint()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };

            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);

            // Act
            var result = await _controller.CreateSprint(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<Sprint>();
            var model = viewResult.Model as Sprint;
            model!.ProjectId.Should().Be(projectId);
            model.StartDate.Date.Should().Be(DateTime.Today);
            model.EndDate.Date.Should().Be(DateTime.Today.AddDays(14));
            viewResult.ViewData["Project"].Should().Be(project);
        }

        [Fact]
        public async Task CreateSprint_Post_WithValidModel_CreatesSprintAndRedirects()
        {
            // Arrange
            var sprint = new Sprint
            {
                Name = "New Sprint",
                Description = "New Description",
                ProjectId = 1,
                StartDate = DateTime.Today,
                EndDate = DateTime.Today.AddDays(14)
            };

            var existingSprints = new List<Sprint>
            {
                new Sprint { Id = 1, SprintKey = "SP-001" }
            };

            var createdSprint = new Sprint
            {
                Id = 2,
                Name = sprint.Name,
                Description = sprint.Description,
                ProjectId = sprint.ProjectId,
                SprintKey = "SP-002"
            };

            // Setup User.FindFirstValue for scrum master assignment
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockAgileService.Setup(x => x.GetProjectSprintsAsync(sprint.ProjectId))
                .ReturnsAsync(existingSprints);
            _mockAgileService.Setup(x => x.CreateSprintAsync(It.IsAny<Sprint>()))
                .ReturnsAsync(createdSprint);

            // Act
            var result = await _controller.CreateSprint(sprint);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("SprintDetails");
            redirectResult.RouteValues!["id"].Should().Be(2);
            _controller.TempData["Success"].Should().Be("Saved successfully");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Create,
                "Sprint",
                2,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task SprintDetails_WithValidSprint_ReturnsViewWithDetails()
        {
            // Arrange
            var sprintId = 1;
            var sprint = new Sprint
            {
                Id = sprintId,
                Name = "Test Sprint",
                Description = "Test Description",
                ProjectId = 1,
                Project = new Project { Id = 1, Name = "Test Project" }
            };
            var userStories = new List<UserStory>
            {
                new UserStory { Id = 1, Title = "Story 1", SprintId = sprintId }
            };
            var sprintMetrics = new Dictionary<string, object>
            {
                { "totalStoryPoints", 20 },
                { "completedStoryPoints", 15 }
            };
            var burndownData = new List<object>
            {
                new { Date = DateTime.Today, RemainingStoryPoints = 5 }
            };

            _mockAgileService.Setup(x => x.GetSprintByIdAsync(sprintId))
                .ReturnsAsync(sprint);
            _mockAgileService.Setup(x => x.GetSprintUserStoriesAsync(sprintId))
                .ReturnsAsync(userStories);
            _mockAgileService.Setup(x => x.GetSprintMetricsAsync(sprintId))
                .ReturnsAsync(sprintMetrics);
            _mockAgileService.Setup(x => x.GetBurndownDataAsync(sprintId))
                .ReturnsAsync(burndownData);

            // Act
            var result = await _controller.SprintDetails(sprintId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<Sprint>();
            var model = viewResult.Model as Sprint;
            model!.Id.Should().Be(sprintId);
            model.Name.Should().Be("Test Sprint");
            viewResult.ViewData["UserStories"].Should().Be(userStories);
            viewResult.ViewData["SprintMetrics"].Should().Be(sprintMetrics);
            viewResult.ViewData["BurndownData"].Should().Be(burndownData);
            viewResult.ViewData["Project"].Should().Be(sprint.Project);
        }

        [Fact]
        public async Task StartSprint_WithValidSprint_StartsSprintAndRedirects()
        {
            // Arrange
            var sprintId = 1;

            // Setup User.FindFirstValue for audit logging
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockAgileService.Setup(x => x.StartSprintAsync(sprintId))
                .ReturnsAsync(true);
            _mockLocalizationService.Setup(x => x.GetString("Agile.SprintStarted"))
                .Returns("Sprint started successfully");

            // Act
            var result = await _controller.StartSprint(sprintId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Sprints");
            _controller.TempData["Success"].Should().Be("Sprint started successfully");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.ChangeStatus,
                "Sprint",
                sprintId,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task StartSprint_WithFailedStart_RedirectsWithError()
        {
            // Arrange
            var sprintId = 1;
            _mockAgileService.Setup(x => x.StartSprintAsync(sprintId))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.StartSprint(sprintId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Sprints");
            _controller.TempData["Error"].Should().Be("An error occurred");
        }

        [Fact]
        public async Task MoveCard_WithValidRequest_ReturnsSuccessJson()
        {
            // Arrange
            var request = new MoveCardRequest
            {
                CardId = 1,
                ColumnId = 3
            };

            // Setup User.FindFirstValue for audit logging
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockAgileService.Setup(x => x.MoveUserStoryToColumnAsync(request.CardId, request.ColumnId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.MoveCard(request);

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            var value = jsonResult!.Value;
            value.Should().NotBeNull();

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Update,
                "UserStory",
                request.CardId,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task MoveCard_WithFailedMove_ReturnsFailureJson()
        {
            // Arrange
            var request = new MoveCardRequest
            {
                CardId = 1,
                ColumnId = 3
            };

            _mockAgileService.Setup(x => x.MoveUserStoryToColumnAsync(request.CardId, request.ColumnId))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.MoveCard(request);

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            jsonResult!.Value.Should().NotBeNull();
        }

        [Fact]
        public async Task GetKanbanData_WithValidProject_ReturnsKanbanJson()
        {
            // Arrange
            var projectId = 1;
            var kanbanBoard = new Dictionary<int, IEnumerable<UserStory>>
            {
                { 1, new List<UserStory>() },
                { 2, new List<UserStory>() },
                { 3, new List<UserStory>() }
            };

            _mockAgileService.Setup(x => x.GetKanbanBoardAsync(projectId))
                .ReturnsAsync(kanbanBoard);

            // Act
            var result = await _controller.GetKanbanData(projectId);

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            jsonResult!.Value.Should().Be(kanbanBoard);
        }

        [Fact]
        public async Task GetKanbanData_WithException_ReturnsErrorJson()
        {
            // Arrange
            var projectId = 1;
            _mockAgileService.Setup(x => x.GetKanbanBoardAsync(projectId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetKanbanData(projectId);

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            jsonResult!.Value.Should().NotBeNull();
        }

        [Fact]
        public async Task Analytics_WithValidProject_ReturnsViewWithAnalytics()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };
            var analytics = new Dictionary<string, object>
            {
                { "totalEpics", 5 },
                { "completedEpics", 3 }
            };
            var velocityData = new List<object>
            {
                new { SprintName = "Sprint 1", CompletedStoryPoints = 20 }
            };
            var epicProgress = new Dictionary<string, object>
            {
                { "totalProgress", 60.0 }
            };

            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockAgileService.Setup(x => x.GetAgileAnalyticsAsync(projectId))
                .ReturnsAsync(analytics);
            _mockAgileService.Setup(x => x.GetVelocityDataAsync(projectId, It.IsAny<int>()))
                .ReturnsAsync(velocityData);
            _mockAgileService.Setup(x => x.GetEpicProgressReportAsync(projectId))
                .ReturnsAsync(epicProgress);

            // Act
            var result = await _controller.Analytics(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.ViewData["Project"].Should().Be(project);
            viewResult.ViewData["Analytics"].Should().Be(analytics);
            viewResult.ViewData["VelocityData"].Should().Be(velocityData);
            viewResult.ViewData["EpicProgress"].Should().Be(epicProgress);
            viewResult.ViewData["ProjectId"].Should().Be(projectId);
        }

        [Fact]
        public async Task Analytics_WithNonExistentProject_ReturnsNotFound()
        {
            // Arrange
            var projectId = 999;
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync((Project?)null);

            // Act
            var result = await _controller.Analytics(projectId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }
    }
}
