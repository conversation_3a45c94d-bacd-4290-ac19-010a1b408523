using System.ComponentModel.DataAnnotations;

using PM.Tool.Core.Entities;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Enums;

namespace PM.Tool.Models.ViewModels
{
    public class EpicCreateViewModel : BaseCreateViewModel, IEntityViewModel<Epic>
    {
        [Required]
        [MaxLength(200)]
        [Display(Name = "Epic Title")]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(5000)]
        [Display(Name = "Description")]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Project")]
        public int ProjectId { get; set; }

        [Display(Name = "Priority")]
        public EpicPriority Priority { get; set; } = EpicPriority.Medium;

        [MaxLength(2000)]
        [Display(Name = "Acceptance Criteria")]
        public string? AcceptanceCriteria { get; set; }

        [MaxLength(1000)]
        [Display(Name = "Business Value")]
        public string? BusinessValue { get; set; }

        [Display(Name = "Estimated Story Points")]
        [Range(0, 999, ErrorMessage = "Story points must be between 0 and 999")]
        public decimal EstimatedStoryPoints { get; set; }

        [Display(Name = "Owner")]
        public string? OwnerId { get; set; }

        [Display(Name = "Target Date")]
        [DataType(DataType.Date)]
        public DateTime? TargetDate { get; set; }

        [Display(Name = "Sort Order")]
        public int SortOrder { get; set; } = 0;

        [MaxLength(500)]
        [Display(Name = "Tags")]
        public string? Tags { get; set; }

        public Epic ToEntity()
        {
            return new Epic
            {
                Title = Title,
                Description = Description,
                ProjectId = ProjectId,
                Priority = Priority,
                AcceptanceCriteria = AcceptanceCriteria,
                BusinessValue = BusinessValue,
                EstimatedStoryPoints = EstimatedStoryPoints,
                OwnerId = OwnerId,
                TargetDate = TargetDate,
                Tags = Tags,
                SortOrder = SortOrder,
                Status = EpicStatus.Draft
            };
        }

        public void UpdateEntity(Epic entity)
        {
            entity.Title = Title;
            entity.Description = Description;
            entity.ProjectId = ProjectId;
            entity.Priority = Priority;
            entity.AcceptanceCriteria = AcceptanceCriteria;
            entity.BusinessValue = BusinessValue;
            entity.EstimatedStoryPoints = EstimatedStoryPoints;
            entity.OwnerId = OwnerId;
            entity.TargetDate = TargetDate;
            entity.Tags = Tags;
            entity.SortOrder = SortOrder;
        }
    }

    public class EpicEditViewModel : EpicCreateViewModel
    {
        public int Id { get; set; }

        [Display(Name = "Epic Key")]
        public string EpicKey { get; set; } = string.Empty;

        [Display(Name = "Status")]
        public EpicStatus Status { get; set; } = EpicStatus.Draft;

        [Display(Name = "Actual Story Points")]
        [Range(0, 999, ErrorMessage = "Story points must be between 0 and 999")]
        public decimal ActualStoryPoints { get; set; }

        [Display(Name = "Sort Order")]
        public new int SortOrder { get; set; }

        public static EpicEditViewModel FromEntity(Epic epic)
        {
            return new EpicEditViewModel
            {
                Id = epic.Id,
                Title = epic.Title,
                Description = epic.Description,
                ProjectId = epic.ProjectId,
                EpicKey = epic.EpicKey,
                Status = epic.Status,
                Priority = epic.Priority,
                AcceptanceCriteria = epic.AcceptanceCriteria,
                BusinessValue = epic.BusinessValue,
                EstimatedStoryPoints = epic.EstimatedStoryPoints,
                ActualStoryPoints = epic.ActualStoryPoints,
                OwnerId = epic.OwnerId,
                TargetDate = epic.TargetDate,
                Tags = epic.Tags,
                SortOrder = epic.SortOrder
            };
        }

        public new void UpdateEntity(Epic entity)
        {
            base.UpdateEntity(entity);
            entity.EpicKey = EpicKey;
            entity.Status = Status;
            entity.ActualStoryPoints = ActualStoryPoints;
            entity.SortOrder = SortOrder;
        }
    }

    public class UserStoryCreateViewModel : BaseCreateViewModel, IEntityViewModel<UserStory>
    {
        [Required]
        [MaxLength(200)]
        [Display(Name = "Story Title")]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(5000)]
        [Display(Name = "Description")]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Project")]
        public int ProjectId { get; set; }

        [Display(Name = "Epic")]
        public int? EpicId { get; set; }

        [Display(Name = "Sprint")]
        public int? SprintId { get; set; }

        [Required]
        [MaxLength(1000)]
        [Display(Name = "As a...")]
        public string AsA { get; set; } = string.Empty;

        [Required]
        [MaxLength(1000)]
        [Display(Name = "I want...")]
        public string IWant { get; set; } = string.Empty;

        [Required]
        [MaxLength(1000)]
        [Display(Name = "So that...")]
        public string SoThat { get; set; } = string.Empty;

        [Display(Name = "Priority")]
        public UserStoryPriority Priority { get; set; } = UserStoryPriority.Medium;

        [Display(Name = "Story Points")]
        [Range(0, 100, ErrorMessage = "Story points must be between 0 and 100")]
        public decimal StoryPoints { get; set; }

        [MaxLength(2000)]
        [Display(Name = "Acceptance Criteria")]
        public string? AcceptanceCriteria { get; set; }

        [Display(Name = "Assigned To")]
        public string? AssignedToUserId { get; set; }

        public UserStory ToEntity()
        {
            return new UserStory
            {
                Title = Title,
                Description = Description,
                ProjectId = ProjectId,
                EpicId = EpicId,
                SprintId = SprintId,
                AsA = AsA,
                IWant = IWant,
                SoThat = SoThat,
                Priority = Priority,
                StoryPoints = StoryPoints,
                AcceptanceCriteria = AcceptanceCriteria,
                AssignedToUserId = AssignedToUserId,
                Status = UserStoryStatus.Backlog
            };
        }

        public void UpdateEntity(UserStory entity)
        {
            entity.Title = Title;
            entity.Description = Description;
            entity.ProjectId = ProjectId;
            entity.EpicId = EpicId;
            entity.SprintId = SprintId;
            entity.AsA = AsA;
            entity.IWant = IWant;
            entity.SoThat = SoThat;
            entity.Priority = Priority;
            entity.StoryPoints = StoryPoints;
            entity.AcceptanceCriteria = AcceptanceCriteria;
            entity.AssignedToUserId = AssignedToUserId;
        }
    }

    public class UserStoryEditViewModel : UserStoryCreateViewModel
    {
        public int Id { get; set; }

        [Display(Name = "Story Key")]
        public string StoryKey { get; set; } = string.Empty;

        [Display(Name = "Status")]
        public UserStoryStatus Status { get; set; } = UserStoryStatus.Backlog;

        [Display(Name = "Backlog Order")]
        public int BacklogOrder { get; set; }

        public static UserStoryEditViewModel FromEntity(UserStory story)
        {
            return new UserStoryEditViewModel
            {
                Id = story.Id,
                Title = story.Title,
                Description = story.Description,
                ProjectId = story.ProjectId,
                EpicId = story.EpicId,
                SprintId = story.SprintId,
                StoryKey = story.StoryKey,
                AsA = story.AsA,
                IWant = story.IWant,
                SoThat = story.SoThat,
                Status = story.Status,
                Priority = story.Priority,
                StoryPoints = story.StoryPoints,
                AcceptanceCriteria = story.AcceptanceCriteria,
                AssignedToUserId = story.AssignedToUserId,
                BacklogOrder = story.BacklogOrder
            };
        }

        public new void UpdateEntity(UserStory entity)
        {
            base.UpdateEntity(entity);
            entity.StoryKey = StoryKey;
            entity.Status = Status;
            entity.BacklogOrder = BacklogOrder;
        }
    }

    #region Feature ViewModels

    public class FeatureCreateViewModel : BaseCreateViewModel, IEntityViewModel<Feature>
    {
        [Required]
        [MaxLength(200)]
        [Display(Name = "Feature Title")]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(5000)]
        [Display(Name = "Description")]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Project")]
        public int ProjectId { get; set; }

        [Required]
        [Display(Name = "Epic")]
        public int EpicId { get; set; }

        [Display(Name = "Priority")]
        public FeaturePriority Priority { get; set; } = FeaturePriority.Medium;

        [Display(Name = "Status")]
        public FeatureStatus Status { get; set; } = FeatureStatus.Draft;

        [MaxLength(2000)]
        [Display(Name = "Business Value")]
        public string? BusinessValue { get; set; }

        [MaxLength(3000)]
        [Display(Name = "Acceptance Criteria")]
        public string? AcceptanceCriteria { get; set; }

        [Display(Name = "Estimated Story Points")]
        [Range(0, 999, ErrorMessage = "Story points must be between 0 and 999")]
        public decimal EstimatedStoryPoints { get; set; }

        [Display(Name = "Owner")]
        public string? OwnerId { get; set; }

        [Display(Name = "Target Date")]
        [DataType(DataType.Date)]
        public DateTime? TargetDate { get; set; }

        [Display(Name = "Sort Order")]
        public int SortOrder { get; set; } = 0;

        [MaxLength(500)]
        [Display(Name = "Tags")]
        public string? Tags { get; set; }

        public Feature ToEntity()
        {
            return new Feature
            {
                Title = Title,
                Description = Description,
                ProjectId = ProjectId,
                EpicId = EpicId,
                Priority = Priority,
                BusinessValue = BusinessValue,
                AcceptanceCriteria = AcceptanceCriteria,
                EstimatedStoryPoints = EstimatedStoryPoints,
                OwnerId = OwnerId,
                TargetDate = TargetDate,
                Tags = Tags,
                SortOrder = SortOrder,
                Status = FeatureStatus.Draft
            };
        }

        public void UpdateEntity(Feature entity)
        {
            entity.Title = Title;
            entity.Description = Description;
            entity.ProjectId = ProjectId;
            entity.EpicId = EpicId;
            entity.Priority = Priority;
            entity.BusinessValue = BusinessValue;
            entity.AcceptanceCriteria = AcceptanceCriteria;
            entity.EstimatedStoryPoints = EstimatedStoryPoints;
            entity.OwnerId = OwnerId;
            entity.TargetDate = TargetDate;
            entity.Tags = Tags;
            entity.SortOrder = SortOrder;
        }
    }

    public class FeatureEditViewModel : FeatureCreateViewModel
    {
        public int Id { get; set; }

        [Display(Name = "Feature Key")]
        public string FeatureKey { get; set; } = string.Empty;

        [Display(Name = "Status")]
        public FeatureStatus Status { get; set; } = FeatureStatus.Draft;

        [Display(Name = "Actual Story Points")]
        [Range(0, 999, ErrorMessage = "Story points must be between 0 and 999")]
        public decimal ActualStoryPoints { get; set; }

        [Display(Name = "Progress Percentage")]
        [Range(0, 100, ErrorMessage = "Progress must be between 0 and 100")]
        public decimal ProgressPercentage { get; set; }

        [Display(Name = "Created At")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Updated At")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "User Story Count")]
        public int UserStoryCount { get; set; }

        [Display(Name = "Completed User Story Count")]
        public int CompletedUserStoryCount { get; set; }

        public static FeatureEditViewModel FromEntity(Feature feature)
        {
            return new FeatureEditViewModel
            {
                Id = feature.Id,
                Title = feature.Title,
                Description = feature.Description,
                ProjectId = feature.ProjectId,
                EpicId = feature.EpicId,
                FeatureKey = feature.FeatureKey,
                Status = feature.Status,
                Priority = feature.Priority,
                BusinessValue = feature.BusinessValue,
                AcceptanceCriteria = feature.AcceptanceCriteria,
                EstimatedStoryPoints = feature.EstimatedStoryPoints,
                ActualStoryPoints = feature.ActualStoryPoints,
                ProgressPercentage =(decimal) feature.ProgressPercentage,
                OwnerId = feature.OwnerId,
                TargetDate = feature.TargetDate,
                Tags = feature.Tags,
                SortOrder = feature.SortOrder,
                CreatedAt = feature.CreatedAt,
                UpdatedAt = feature.UpdatedAt,
                UserStoryCount = feature.UserStoryCount,
                CompletedUserStoryCount = feature.CompletedUserStoryCount
            };
        }

        public new void UpdateEntity(Feature entity)
        {
            base.UpdateEntity(entity);
            entity.FeatureKey = FeatureKey;
            entity.Status = Status;
            entity.ActualStoryPoints = ActualStoryPoints;
            // ProgressPercentage is read-only, calculated property
        }
    }

    public class FeatureDetailsViewModel
    {
        public Feature Feature { get; set; } = new();
        public IEnumerable<UserStory> UserStories { get; set; } = new List<UserStory>();
        public IEnumerable<Bug> Bugs { get; set; } = new List<Bug>();
        public IEnumerable<TestCase> TestCases { get; set; } = new List<TestCase>();
        public FeatureMetrics Metrics { get; set; } = new();
    }

    public class FeatureMetrics
    {
        public int TotalUserStories { get; set; }
        public int CompletedUserStories { get; set; }
        public decimal CompletionPercentage { get; set; }
        public int TotalBugs { get; set; }
        public int OpenBugs { get; set; }
        public int TotalTestCases { get; set; }
        public int PassedTestCases { get; set; }
        public decimal TestPassRate { get; set; }
        public decimal TotalStoryPoints { get; set; }
        public decimal CompletedStoryPoints { get; set; }
    }

    #endregion

    #region Bug ViewModels

    public class BugCreateViewModel : BaseCreateViewModel, IEntityViewModel<Bug>
    {
        [Required]
        [MaxLength(200)]
        [Display(Name = "Bug Title")]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(5000)]
        [Display(Name = "Description")]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Project")]
        public int ProjectId { get; set; }

        [Display(Name = "Feature")]
        public int? FeatureId { get; set; }

        [Display(Name = "User Story")]
        public int? UserStoryId { get; set; }

        [Required]
        [Display(Name = "Severity")]
        public BugSeverity Severity { get; set; } = BugSeverity.Medium;

        [Required]
        [Display(Name = "Priority")]
        public BugPriority Priority { get; set; } = BugPriority.Medium;

        [Required]
        [MaxLength(3000)]
        [Display(Name = "Steps to Reproduce")]
        public string StepsToReproduce { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        [Display(Name = "Expected Result")]
        public string ExpectedResult { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        [Display(Name = "Actual Result")]
        public string ActualResult { get; set; } = string.Empty;

        [MaxLength(100)]
        [Display(Name = "Found in Version")]
        public string? FoundInVersion { get; set; }

        [MaxLength(100)]
        [Display(Name = "Fixed in Version")]
        public string? FixedInVersion { get; set; }

        [Display(Name = "Assigned To")]
        public string? AssignedToUserId { get; set; }

        [Display(Name = "Estimated Hours")]
        [Range(0, 999, ErrorMessage = "Hours must be between 0 and 999")]
        public decimal EstimatedHours { get; set; }

        [Display(Name = "Target Date")]
        [DataType(DataType.Date)]
        public DateTime? TargetDate { get; set; }

        [MaxLength(500)]
        [Display(Name = "Tags")]
        public string? Tags { get; set; }

        public Bug ToEntity()
        {
            return new Bug
            {
                Title = Title,
                Description = Description,
                ProjectId = ProjectId,
                FeatureId = FeatureId,
                UserStoryId = UserStoryId,
                Severity = Severity,
                Priority = Priority,
                StepsToReproduce = StepsToReproduce,
                ExpectedResult = ExpectedResult,
                ActualResult = ActualResult,
                FoundInVersion = FoundInVersion,
                FixedInVersion = FixedInVersion,
                AssignedToUserId = AssignedToUserId,
                EstimatedHours = EstimatedHours,
                TargetDate = TargetDate,
                Tags = Tags,
                Status = BugStatus.New
            };
        }

        public void UpdateEntity(Bug entity)
        {
            entity.Title = Title;
            entity.Description = Description;
            entity.ProjectId = ProjectId;
            entity.FeatureId = FeatureId;
            entity.UserStoryId = UserStoryId;
            entity.Severity = Severity;
            entity.Priority = Priority;
            entity.StepsToReproduce = StepsToReproduce;
            entity.ExpectedResult = ExpectedResult;
            entity.ActualResult = ActualResult;
            entity.FoundInVersion = FoundInVersion;
            entity.FixedInVersion = FixedInVersion;
            entity.AssignedToUserId = AssignedToUserId;
            entity.EstimatedHours = EstimatedHours;
            entity.TargetDate = TargetDate;
            entity.Tags = Tags;
        }
    }

    public class BugEditViewModel : BugCreateViewModel
    {
        public int Id { get; set; }

        [Display(Name = "Bug Key")]
        public string BugKey { get; set; } = string.Empty;

        [Display(Name = "Status")]
        public BugStatus Status { get; set; } = BugStatus.New;

        [Display(Name = "Actual Hours")]
        [Range(0, 999, ErrorMessage = "Hours must be between 0 and 999")]
        public decimal ActualHours { get; set; }

        [Display(Name = "Reported By")]
        public string? ReportedByUserId { get; set; }

        [Display(Name = "Resolved Date")]
        [DataType(DataType.DateTime)]
        public DateTime? ResolvedDate { get; set; }

        [Display(Name = "Verified Date")]
        [DataType(DataType.DateTime)]
        public DateTime? VerifiedDate { get; set; }

        public static BugEditViewModel FromEntity(Bug bug)
        {
            return new BugEditViewModel
            {
                Id = bug.Id,
                Title = bug.Title,
                Description = bug.Description,
                ProjectId = bug.ProjectId,
                FeatureId = bug.FeatureId,
                UserStoryId = bug.UserStoryId,
                BugKey = bug.BugKey,
                Status = bug.Status,
                Severity = bug.Severity,
                Priority = bug.Priority,
                StepsToReproduce = bug.StepsToReproduce,
                ExpectedResult = bug.ExpectedResult,
                ActualResult = bug.ActualResult,
                FoundInVersion = bug.FoundInVersion,
                FixedInVersion = bug.FixedInVersion,
                AssignedToUserId = bug.AssignedToUserId,
                ReportedByUserId = bug.ReportedByUserId,
                EstimatedHours = bug.EstimatedHours,
                ActualHours = bug.ActualHours,
                TargetDate = bug.TargetDate,
                ResolvedDate = bug.ResolvedDate,
                VerifiedDate = bug.VerifiedDate,
                Tags = bug.Tags
            };
        }

        public new void UpdateEntity(Bug entity)
        {
            base.UpdateEntity(entity);
            entity.BugKey = BugKey;
            entity.Status = Status;
            entity.ActualHours = ActualHours;
            entity.ReportedByUserId = ReportedByUserId;
            entity.ResolvedDate = ResolvedDate;
            entity.VerifiedDate = VerifiedDate;
        }
    }

    public class BugDetailsViewModel
    {
        public Bug Bug { get; set; } = new();
        public IEnumerable<BugComment> Comments { get; set; } = new List<BugComment>();
        public IEnumerable<BugAttachment> Attachments { get; set; } = new List<BugAttachment>();
        public BugMetrics Metrics { get; set; } = new();
    }

    public class BugCommentCreateViewModel
    {
        [Required]
        public int BugId { get; set; }

        [Required]
        [MaxLength(2000)]
        [Display(Name = "Comment")]
        public string Content { get; set; } = string.Empty;

        [Display(Name = "Comment Type")]
        public PM.Tool.Core.Entities.Agile.CommentType Type { get; set; } = PM.Tool.Core.Entities.Agile.CommentType.General;
    }

    public class BugMetrics
    {
        public TimeSpan? TimeToResolve { get; set; }
        public TimeSpan? TimeToVerify { get; set; }
        public int TotalComments { get; set; }
        public int TotalAttachments { get; set; }
        public bool IsOverdue { get; set; }
        public int DaysOpen { get; set; }
    }

    #endregion

    #region Test Case ViewModels

    public class TestCaseCreateViewModel : BaseCreateViewModel, IEntityViewModel<TestCase>
    {
        [Required]
        [MaxLength(200)]
        [Display(Name = "Test Case Title")]
        public string Title { get; set; } = string.Empty;

        [MaxLength(5000)]
        [Display(Name = "Description")]
        public string? Description { get; set; }

        [Required]
        [Display(Name = "Project")]
        public int ProjectId { get; set; }

        [Display(Name = "Feature")]
        public int? FeatureId { get; set; }

        [Display(Name = "User Story")]
        public int? UserStoryId { get; set; }

        [Required]
        [Display(Name = "Test Type")]
        public TestCaseType Type { get; set; } = TestCaseType.Functional;

        [Required]
        [Display(Name = "Priority")]
        public TestCasePriority Priority { get; set; } = TestCasePriority.Medium;

        [MaxLength(2000)]
        [Display(Name = "Pre-conditions")]
        public string? PreConditions { get; set; }

        [Required]
        [MaxLength(5000)]
        [Display(Name = "Test Steps")]
        public string TestSteps { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        [Display(Name = "Expected Result")]
        public string ExpectedResult { get; set; } = string.Empty;

        [Display(Name = "Assigned To")]
        public string? AssignedToUserId { get; set; }

        [Display(Name = "Estimated Execution Time (minutes)")]
        [Range(0, 999, ErrorMessage = "Time must be between 0 and 999 minutes")]
        public decimal EstimatedExecutionTime { get; set; }

        [MaxLength(500)]
        [Display(Name = "Tags")]
        public string? Tags { get; set; }

        public TestCase ToEntity()
        {
            return new TestCase
            {
                Title = Title,
                Description = Description,
                ProjectId = ProjectId,
                FeatureId = FeatureId,
                UserStoryId = UserStoryId,
                Type = Type,
                Priority = Priority,
                PreConditions = PreConditions,
                TestSteps = TestSteps,
                ExpectedResult = ExpectedResult,
                AssignedToUserId = AssignedToUserId,
                EstimatedExecutionTime = EstimatedExecutionTime,
                Tags = Tags,
                Status = TestCaseStatus.Draft
            };
        }

        public void UpdateEntity(TestCase entity)
        {
            entity.Title = Title;
            entity.Description = Description;
            entity.ProjectId = ProjectId;
            entity.FeatureId = FeatureId;
            entity.UserStoryId = UserStoryId;
            entity.Type = Type;
            entity.Priority = Priority;
            entity.PreConditions = PreConditions;
            entity.TestSteps = TestSteps;
            entity.ExpectedResult = ExpectedResult;
            entity.AssignedToUserId = AssignedToUserId;
            entity.EstimatedExecutionTime = EstimatedExecutionTime;
            entity.Tags = Tags;
        }
    }

    public class TestCaseEditViewModel : TestCaseCreateViewModel
    {
        public int Id { get; set; }

        [Display(Name = "Test Case Key")]
        public string TestCaseKey { get; set; } = string.Empty;

        [Display(Name = "Status")]
        public TestCaseStatus Status { get; set; } = TestCaseStatus.Draft;

        [Display(Name = "Created By")]
        public string? CreatedByUserId { get; set; }

        public static TestCaseEditViewModel FromEntity(TestCase testCase)
        {
            return new TestCaseEditViewModel
            {
                Id = testCase.Id,
                Title = testCase.Title,
                Description = testCase.Description,
                ProjectId = testCase.ProjectId,
                FeatureId = testCase.FeatureId,
                UserStoryId = testCase.UserStoryId,
                TestCaseKey = testCase.TestCaseKey,
                Type = testCase.Type,
                Priority = testCase.Priority,
                Status = testCase.Status,
                PreConditions = testCase.PreConditions,
                TestSteps = testCase.TestSteps,
                ExpectedResult = testCase.ExpectedResult,
                AssignedToUserId = testCase.AssignedToUserId,
                CreatedByUserId = testCase.CreatedByUserId,
                EstimatedExecutionTime =testCase.EstimatedExecutionTime,
                Tags = testCase.Tags
            };
        }

        public new void UpdateEntity(TestCase entity)
        {
            base.UpdateEntity(entity);
            entity.TestCaseKey = TestCaseKey;
            entity.Status = Status;
            entity.CreatedByUserId = CreatedByUserId;
        }
    }

    public class TestCaseDetailsViewModel
    {
        public TestCase TestCase { get; set; } = new();
        public IEnumerable<TestExecution> Executions { get; set; } = new List<TestExecution>();
        public TestCaseMetrics Metrics { get; set; } = new();
        public TestExecution? LatestExecution { get; set; }
    }

    public class TestExecutionCreateViewModel
    {
        [Required]
        public int TestCaseId { get; set; }

        [Required]
        [Display(Name = "Test Result")]
        public TestExecutionResult Result { get; set; }

        [MaxLength(2000)]
        [Display(Name = "Actual Result")]
        public string? ActualResult { get; set; }

        [MaxLength(1000)]
        [Display(Name = "Notes")]
        public string? Notes { get; set; }

        [Display(Name = "Execution Time (minutes)")]
        [Range(0, 999, ErrorMessage = "Time must be between 0 and 999 minutes")]
        public decimal ExecutionTime { get; set; }

        [Display(Name = "Related Bug")]
        public int? BugId { get; set; }

        public TestExecution ToEntity()
        {
            return new TestExecution
            {
                TestCaseId = TestCaseId,
                Result = Result,
                ActualResult = ActualResult,
                Notes = Notes,
                ExecutionTime = ExecutionTime,
                BugId = BugId,
                ExecutedAt = DateTime.UtcNow
            };
        }
    }

    public class TestCaseMetrics
    {
        public int TotalExecutions { get; set; }
        public int PassedExecutions { get; set; }
        public int FailedExecutions { get; set; }
        public decimal PassRate { get; set; }
        public TestExecutionResult? LastExecutionResult { get; set; }
        public DateTime? LastExecutedAt { get; set; }
        public int? AverageExecutionTime { get; set; }
        public bool IsOverdue { get; set; }
    }

    #endregion

    #region Enhanced Analytics ViewModels

    public class AgileAnalyticsViewModel
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public FeatureAnalytics Features { get; set; } = new();
        public BugAnalytics Bugs { get; set; } = new();
        public TestCaseAnalytics TestCases { get; set; } = new();
        public VelocityAnalytics Velocity { get; set; } = new();
        public QualityAnalytics Quality { get; set; } = new();
    }

    public class FeatureAnalytics
    {
        public int TotalFeatures { get; set; }
        public int CompletedFeatures { get; set; }
        public int InProgressFeatures { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal AverageStoryPoints { get; set; }
        public IEnumerable<FeatureProgressData> ProgressData { get; set; } = new List<FeatureProgressData>();
    }

    public class BugAnalytics
    {
        public int TotalBugs { get; set; }
        public int OpenBugs { get; set; }
        public int ResolvedBugs { get; set; }
        public decimal ResolutionRate { get; set; }
        public decimal AverageResolutionTime { get; set; }
        public Dictionary<string, int> BugsBySeverity { get; set; } = new();
        public Dictionary<string, int> BugsByStatus { get; set; } = new();
    }

    public class TestCaseAnalytics
    {
        public int TotalTestCases { get; set; }
        public int ExecutedTestCases { get; set; }
        public int PassedTestCases { get; set; }
        public decimal ExecutionRate { get; set; }
        public decimal PassRate { get; set; }
        public Dictionary<string, int> TestCasesByType { get; set; } = new();
        public double TestCoveragePercentage { get; set; }
    }

    public class VelocityAnalytics
    {
        public IEnumerable<VelocityData> VelocityTrends { get; set; } = new List<VelocityData>();
        public decimal AverageVelocity { get; set; }
        public decimal CurrentSprintVelocity { get; set; }
        public decimal PredictedVelocity { get; set; }
    }

    public class QualityAnalytics
    {
        public decimal DefectDensity { get; set; }
        public decimal TestCoverage { get; set; }
        public decimal CodeQualityScore { get; set; }
        public int CriticalBugs { get; set; }
        public int HighPriorityBugs { get; set; }
    }

    public class FeatureProgressData
    {
        public string FeatureName { get; set; } = string.Empty;
        public string FeatureKey { get; set; } = string.Empty;
        public decimal ProgressPercentage { get; set; }
        public FeatureStatus Status { get; set; }
        public DateTime? TargetDate { get; set; }
    }

    public class VelocityData
    {
        public string SprintName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal PlannedStoryPoints { get; set; }
        public decimal CompletedStoryPoints { get; set; }
        public decimal TeamCapacity { get; set; }
        public decimal CapacityUtilization { get; set; }
    }

    #endregion

    #region Sprint Planning ViewModels

    public class SprintPlanningViewModel
    {
        public int ProjectId { get; set; }
        public Project? Project { get; set; }
        public List<UserStory> BacklogStories { get; set; } = new List<UserStory>();
        public Sprint? CurrentSprint { get; set; }
        public List<UserStory> SprintStories { get; set; } = new List<UserStory>();
        public List<Sprint> AllSprints { get; set; } = new List<Sprint>();

        // Capacity planning
        public decimal TotalCapacity { get; set; }
        public decimal CommittedStoryPoints { get; set; }
        public decimal RemainingCapacity => TotalCapacity - CommittedStoryPoints;
        public bool IsOverCapacity => CommittedStoryPoints > TotalCapacity;

        // Sprint metrics
        public int TotalStories => SprintStories.Count;
        public int CompletedStories => SprintStories.Count(s => s.Status == UserStoryStatus.Done);
        public decimal CompletionPercentage => TotalStories > 0 ? (decimal)CompletedStories / TotalStories * 100 : 0;
    }

    #endregion

    #region Activity Feed ViewModels

    public class ActivityFeedViewModel
    {
        public int? ProjectId { get; set; }
        public List<ProjectSelectViewModel> UserProjects { get; set; } = new();
        public List<ActivityItemViewModel> Activities { get; set; } = new();
    }

    public class ActivityItemViewModel
    {
        public int Id { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public string TaskTitle { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    public class ProjectSelectViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
    }

    #endregion

    #region Epic Management ViewModels

    public class EpicManagementViewModel
    {
        public int? ProjectId { get; set; }
        public List<ProjectSelectViewModel> UserProjects { get; set; } = new();
        public List<Epic> Epics { get; set; } = new();
        public bool CanCreateEpic { get; set; }
        public EpicFilterOptions FilterOptions { get; set; } = new();
        public EpicStatisticsViewModel Statistics { get; set; } = new();
    }

    public class EpicStatisticsViewModel
    {
        public int TotalEpics { get; set; }
        public int CompletedEpics { get; set; }
        public int InProgressEpics { get; set; }
        public int DraftEpics { get; set; }
        public int CancelledEpics { get; set; }
        public decimal TotalStoryPoints { get; set; }
        public decimal CompletedStoryPoints { get; set; }
        public decimal ProgressPercentage { get; set; }
        public int OverdueEpics { get; set; }
    }

    public class EpicDetailsViewModel
    {
        public Epic Epic { get; set; } = null!;
        public List<Feature> Features { get; set; } = new();
        public List<UserStory> UserStories { get; set; } = new();
        public EpicProgressMetrics ProgressMetrics { get; set; } = new();
        public List<EpicTimelineEvent> Timeline { get; set; } = new();
    }



    public class EpicProgressMetrics
    {
        public int TotalFeatures { get; set; }
        public int CompletedFeatures { get; set; }
        public double FeatureProgress { get; set; }

        public int TotalUserStories { get; set; }
        public int CompletedUserStories { get; set; }
        public double UserStoryProgress { get; set; }

        public int TotalTasks { get; set; }
        public int CompletedTasks { get; set; }
        public double TaskProgress { get; set; }

        public decimal EstimatedStoryPoints { get; set; }
        public decimal ActualStoryPoints { get; set; }
        public double StoryPointProgress { get; set; }
    }

    public class EpicTimelineEvent
    {
        public DateTime Date { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
    }

    public class EpicFilterOptions
    {
        public EpicStatus? Status { get; set; }
        public EpicPriority? Priority { get; set; }
        public string? OwnerId { get; set; }
        public bool? IsOverdue { get; set; }
        public string? SearchTerm { get; set; }
    }

    #endregion

    #region Chat ViewModels

    public class ChatViewModel
    {
        public int? ProjectId { get; set; }
        public List<ProjectSelectViewModel> UserProjects { get; set; } = new();
        public List<ChatMessageViewModel> Messages { get; set; } = new();
    }

    public class ChatMessageViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string AuthorName { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string Type { get; set; } = "text"; // text, system, file, etc.
    }

    #endregion
}
