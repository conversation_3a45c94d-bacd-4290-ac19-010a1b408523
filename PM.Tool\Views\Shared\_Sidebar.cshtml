@using Microsoft.AspNetCore.Identity
@using PM.Tool.Core.Entities
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

<!-- Professional Sidebar -->
<aside id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-dark-900 border-r border-neutral-200 dark:border-dark-700 transform -translate-x-full lg:translate-x-0 transition-transform duration-200 ease-in-out flex flex-col shadow-lg">
    <!-- Sidebar Header -->
    <div class="flex items-center justify-between h-16 px-4 border-b border-neutral-200 dark:border-dark-700 flex-shrink-0">
        <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-600 to-primary-700 rounded-lg flex items-center justify-center shadow-sm">
                <i class="fas fa-project-diagram text-white text-sm"></i>
            </div>
            <span class="text-xl font-bold text-neutral-900 dark:text-dark-50">PM Tool</span>
        </div>
        <button id="sidebar-close" class="lg:hidden p-2 rounded-md hover:bg-neutral-100 dark:hover:bg-dark-800 transition-colors">
            <i class="fas fa-times text-neutral-500 dark:text-dark-400"></i>
        </button>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-neutral-300 dark:scrollbar-thumb-dark-600 scrollbar-track-transparent">
        <!-- Dashboard -->
        <a href="@Url.Action("Index", "Home")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" ? "active" : "")">
            <i class="fas fa-tachometer-alt"></i>
            <span>@Localizer["Nav.Dashboard"]</span>
        </a>

        <!-- Project Management -->
        <div class="nav-section">
            <h3 class="nav-section-title">Project Management</h3>
            <div class="nav-section-items">
                <a href="@Url.Action("Index", "Projects")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Projects" ? "active" : "")">
                    <i class="fas fa-folder-open"></i>
                    <span>@Localizer["Nav.Projects"]</span>
                </a>

                <a href="@Url.Action("Index", "Tasks")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Tasks" && ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : "")">
                    <i class="fas fa-tasks"></i>
                    <span>@Localizer["Nav.Tasks"]</span>
                </a>

                <a href="@Url.Action("MyTasks", "Tasks")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Tasks" && ViewContext.RouteData.Values["Action"]?.ToString() == "MyTasks" ? "active" : "")">
                    <i class="fas fa-user-check"></i>
                    <span>My Tasks</span>
                </a>

                <a href="@Url.Action("Index", "Agile")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Agile" ? "active" : "")">
                    <i class="fas fa-columns"></i>
                    <span>@Localizer["Nav.Kanban"]</span>
                </a>

                <a href="@Url.Action("Index", "Epic")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Epic" ? "active" : "")">
                    <i class="fas fa-mountain"></i>
                    <span>Epic Management</span>
                </a>
            </div>
        </div>

        <!-- Collaboration & Communication -->
        <div class="nav-section">
            <h3 class="nav-section-title">Collaboration</h3>
            <div class="nav-section-items">
                <a href="@Url.Action("Feed", "Activity")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Activity" ? "active" : "")">
                    <i class="fas fa-stream"></i>
                    <span>Activity Feed</span>
                    <span class="notification-badge" id="activityBadge" style="display: none;"></span>
                </a>

                <a href="@Url.Action("Index", "Chat")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Chat" ? "active" : "")">
                    <i class="fas fa-comments"></i>
                    <span>Team Chat</span>
                    <span class="notification-badge" id="chatBadge" style="display: none;"></span>
                </a>

                <a href="@Url.Action("Index", "Meeting")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Meeting" ? "active" : "")">
                    <i class="fas fa-video"></i>
                    <span>@Localizer["Nav.Meetings"]</span>
                </a>

                <a href="@Url.Action("Index", "Documentation")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Documentation" ? "active" : "")">
                    <i class="fas fa-book"></i>
                    <span>@Localizer["Nav.Documentation"]</span>
                </a>
            </div>
        </div>

        <!-- Analytics & Reports -->
        @if (SignInManager.IsSignedIn(User) && (User.IsInRole("Admin") || User.IsInRole("Manager")))
        {
            <div class="nav-section">
                <h3 class="nav-section-title">Analytics & Reports</h3>
                <div class="nav-section-items">
                    <a href="@Url.Action("Index", "Analytics")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Analytics" ? "active" : "")">
                        <i class="fas fa-chart-line"></i>
                        <span>@Localizer["Nav.AdvancedReports"]</span>
                    </a>
                </div>
            </div>
        }

        <!-- Resource Management -->
        @if (SignInManager.IsSignedIn(User) && (User.IsInRole("Admin") || User.IsInRole("Manager")))
        {
            <div class="nav-section">
                <h3 class="nav-section-title">Resource Management</h3>
                <div class="nav-section-items">
                    <a href="@Url.Action("Index", "Resource")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Resource" ? "active" : "")">
                        <i class="fas fa-users"></i>
                        <span>@Localizer["Nav.Resources"]</span>
                    </a>

                    <a href="@Url.Action("Index", "TimeTracking")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "TimeTracking" ? "active" : "")">
                        <i class="fas fa-clock"></i>
                        <span>Time Tracking</span>
                    </a>

                    <a href="@Url.Action("Index", "Person")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Person" ? "active" : "")">
                        <i class="fas fa-user-friends"></i>
                        <span>People Management</span>
                    </a>

                    <a href="@Url.Action("Index", "Stakeholder")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Stakeholder" ? "active" : "")">
                        <i class="fas fa-users-cog"></i>
                        <span>Stakeholders</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <h3 class="nav-section-title">Planning & Quality</h3>
                <div class="nav-section-items">
                    <a href="@Url.Action("Index", "Wbs")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Wbs" ? "active" : "")">
                        <i class="fas fa-sitemap"></i>
                        <span>Work Breakdown</span>
                    </a>

                    <a href="@Url.Action("Index", "Risk")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Risk" ? "active" : "")">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>@Localizer["Nav.Risks"]</span>
                    </a>

                    <a href="@Url.Action("Index", "Requirement")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Requirement" ? "active" : "")">
                        <i class="fas fa-list-alt"></i>
                        <span>@Localizer["Nav.Requirements"]</span>
                    </a>
                </div>
            </div>

            @if (User.IsInRole("Admin"))
            {
                <div class="nav-section">
                    <h3 class="nav-section-title">Administration</h3>
                    <div class="nav-section-items">
                        <a href="@Url.Action("Index", "EncryptionDemo")" class="nav-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "EncryptionDemo" ? "active" : "")">
                            <i class="fas fa-shield-alt"></i>
                            <span>Data Encryption Demo</span>
                        </a>
                    </div>
                </div>
            }
        }
    </nav>

    <!-- Sidebar Footer -->
    <div class="flex-shrink-0 p-4 border-t border-neutral-200 dark:border-dark-700">
        @if (SignInManager.IsSignedIn(User))
        {
            <div class="flex items-center space-x-3 p-3 rounded-lg bg-neutral-50 dark:bg-dark-800 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors cursor-pointer">
                <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-user text-primary-600 dark:text-primary-400"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-neutral-900 dark:text-dark-50 truncate">
                        @(User.Identity?.Name ?? "Guest")
                    </p>
                    <p class="text-xs text-neutral-500 dark:text-dark-400">
                        @if (User.IsInRole("Admin"))
                        {
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-danger-100 dark:bg-danger-900/30 text-danger-800 dark:text-danger-300">
                                Administrator
                            </span>
                        }
                        else if (User.IsInRole("Manager"))
                        {
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-warning-100 dark:bg-warning-900/30 text-warning-800 dark:text-warning-300">
                                Project Manager
                            </span>
                        }
                        else
                        {
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-300">
                                Team Member
                            </span>
                        }
                    </p>
                </div>
                <div class="flex-shrink-0">
                    <i class="fas fa-chevron-up text-neutral-400 dark:text-dark-500 text-xs"></i>
                </div>
            </div>
        }
        else
        {
            <div class="text-center">
                <a href="@Url.Action("Login", "Account", new { area = "Identity" })"
                   class="btn-primary-custom w-full justify-center">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </a>
            </div>
        }
    </div>
</aside>

<!-- Sidebar Overlay (Mobile) -->
<div id="sidebar-overlay" class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden hidden"></div>
