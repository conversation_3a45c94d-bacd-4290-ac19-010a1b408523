using PM.Tool.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Models.ViewModels
{
    public class ProjectViewModel
    {
        public int Id { get; set; }

        [Required]
        [Display(Name = "Project Name")]
        [StringLength(200, ErrorMessage = "Project name cannot exceed 200 characters.")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Description")]
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters.")]
        public string? Description { get; set; }

        [Display(Name = "Status")]
        public ProjectStatus Status { get; set; } = ProjectStatus.Planning;

        public string StatusColor => Status switch
        {
            ProjectStatus.Planning => "secondary",
            ProjectStatus.InProgress => "primary",
            ProjectStatus.OnHold => "warning",
            ProjectStatus.Completed => "success",
            ProjectStatus.Cancelled => "danger",
            _ => "secondary"
        };

        [Required]
        [Display(Name = "Start Date")]
        [DataType(DataType.Date)]
        public DateTime StartDate { get; set; } = DateTime.Today;

        [Display(Name = "End Date")]
        [DataType(DataType.Date)]
        public DateTime? EndDate { get; set; }

        [Display(Name = "Budget")]
        [DataType(DataType.Currency)]
        [Range(0, double.MaxValue, ErrorMessage = "Budget must be a positive number.")]
        public decimal Budget { get; set; }

        [Display(Name = "Client Name")]
        [StringLength(200, ErrorMessage = "Client name cannot exceed 200 characters.")]
        public string? ClientName { get; set; }

        public string CreatedByUserId { get; set; } = string.Empty;
        public string? CreatedByName { get; set; }

        [Display(Name = "Project Manager")]
        public string? ManagerId { get; set; }

        public string? ManagerName { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Computed properties
        public int TotalTasks { get; set; }
        public int CompletedTasks { get; set; }
        public double ProgressPercentage { get; set; }
        public bool IsOverdue { get; set; }
        public int MemberCount { get; set; }
    }

    public class ProjectCreateViewModel
    {
        [Required]
        [Display(Name = "Project Name")]
        [StringLength(200, ErrorMessage = "Project name cannot exceed 200 characters.")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Description")]
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters.")]
        public string? Description { get; set; }

        [Display(Name = "Status")]
        public ProjectStatus Status { get; set; } = ProjectStatus.Planning;

        [Required]
        [Display(Name = "Start Date")]
        [DataType(DataType.Date)]
        public DateTime StartDate { get; set; } = DateTime.Today;

        [Required]
        [Display(Name = "Project Manager")]
        public string ManagerId { get; set; } = string.Empty;

        [Display(Name = "End Date")]
        [DataType(DataType.Date)]
        public DateTime? EndDate { get; set; }

        [Display(Name = "Budget")]
        [DataType(DataType.Currency)]
        [Range(0, double.MaxValue, ErrorMessage = "Budget must be a positive number.")]
        public decimal Budget { get; set; }

        [Display(Name = "Client Name")]
        [StringLength(200, ErrorMessage = "Client name cannot exceed 200 characters.")]
        public string? ClientName { get; set; }
    }

    public class ProjectEditViewModel : ProjectCreateViewModel
    {
        public int Id { get; set; }

        [Display(Name = "Status")]
        public new ProjectStatus Status { get; set; } = ProjectStatus.Planning;

        [Display(Name = "Actual End Date")]
        [DataType(DataType.Date)]
        public DateTime? ActualEndDate { get; set; }
    }

    public class ProjectDetailsViewModel : ProjectViewModel
    {
        public List<ProjectMemberViewModel> Members { get; set; } = new();
        public List<TaskSummaryViewModel> RecentTasks { get; set; } = new();
        public List<MilestoneViewModel> Milestones { get; set; } = new();
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public UserRole? CurrentUserRole { get; set; }
    }

    public class ProjectMemberViewModel
    {
        public int Id { get; set; }
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public UserRole Role { get; set; }
        public DateTime JoinedAt { get; set; }
        public bool IsActive { get; set; }
    }

    public class AddMemberViewModel
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "User Email")]
        [EmailAddress]
        public string UserEmail { get; set; } = string.Empty;

        [Display(Name = "Role")]
        public UserRole Role { get; set; } = UserRole.TeamMember;
    }
}
