// PM.Tool - Real-time Collaboration JavaScript Library
// Comprehensive SignalR integration for live collaboration features

class CollaborationManager {
    constructor() {
        this.connection = null;
        this.isConnected = false;
        this.currentProjectId = null;
        this.currentUserId = null;
        this.onlineUsers = new Map();
        this.userActivities = new Map();
        this.notificationQueue = [];
        this.eventHandlers = new Map();
        
        // Initialize connection
        this.initializeConnection();
    }

    async initializeConnection() {
        try {
            // Create SignalR connection
            this.connection = new signalR.HubConnectionBuilder()
                .withUrl("/collaborationHub")
                .withAutomaticReconnect([0, 2000, 10000, 30000])
                .configureLogging(signalR.LogLevel.Information)
                .build();

            // Set up event handlers
            this.setupEventHandlers();

            // Start connection
            await this.connection.start();
            this.isConnected = true;
            
            console.log("✅ Real-time collaboration connected");
            this.showConnectionStatus("Connected", "success");
            
            // Initialize current project if available
            const projectId = this.getCurrentProjectId();
            if (projectId) {
                await this.joinProject(projectId);
            }

        } catch (error) {
            console.error("❌ SignalR connection failed:", error);
            this.showConnectionStatus("Connection Failed", "error");
            
            // Retry connection after delay
            setTimeout(() => this.initializeConnection(), 5000);
        }
    }

    setupEventHandlers() {
        if (!this.connection) return;

        // Connection events
        this.connection.onreconnecting(() => {
            console.log("🔄 Reconnecting to collaboration hub...");
            this.showConnectionStatus("Reconnecting...", "warning");
            this.isConnected = false;
        });

        this.connection.onreconnected(() => {
            console.log("✅ Reconnected to collaboration hub");
            this.showConnectionStatus("Connected", "success");
            this.isConnected = true;
            
            // Rejoin current project
            if (this.currentProjectId) {
                this.joinProject(this.currentProjectId);
            }
        });

        this.connection.onclose(() => {
            console.log("❌ Disconnected from collaboration hub");
            this.showConnectionStatus("Disconnected", "error");
            this.isConnected = false;
        });

        // Real-time event handlers
        this.connection.on("TaskUpdated", (data) => this.handleTaskUpdated(data));
        this.connection.on("TaskAssigned", (data) => this.handleTaskAssigned(data));
        this.connection.on("CommentAdded", (data) => this.handleCommentAdded(data));
        this.connection.on("CardMoved", (data) => this.handleCardMoved(data));
        this.connection.on("NotificationReceived", (data) => this.handleNotificationReceived(data));
        this.connection.on("UserPresenceChanged", (data) => this.handleUserPresenceChanged(data));
        this.connection.on("UserActivityUpdated", (data) => this.handleUserActivityUpdated(data));
        this.connection.on("ProjectUpdated", (data) => this.handleProjectUpdated(data));
    }

    // Project management
    async joinProject(projectId) {
        this.currentProjectId = projectId;
        
        if (this.isConnected) {
            try {
                // Get online users for this project
                const onlineUsers = await this.connection.invoke("GetOnlineUsers", projectId);
                this.updateOnlineUsers(onlineUsers);
                
                console.log(`📁 Joined project ${projectId} collaboration`);
            } catch (error) {
                console.error("Error joining project:", error);
            }
        }
    }

    // Activity tracking
    async updateActivity(activity, taskId = null) {
        if (!this.isConnected || !this.currentProjectId) return;

        try {
            await this.connection.invoke("UpdateUserActivity", activity, this.currentProjectId, taskId);
        } catch (error) {
            console.error("Error updating activity:", error);
        }
    }

    // Task collaboration
    async notifyTaskUpdate(taskId, updateType, updateData) {
        if (!this.isConnected) return;

        try {
            await this.connection.invoke("TaskUpdated", taskId, updateType, updateData);
        } catch (error) {
            console.error("Error notifying task update:", error);
        }
    }

    async notifyCardMoved(taskId, fromColumn, toColumn, newPosition) {
        if (!this.isConnected) return;

        try {
            await this.connection.invoke("CardMoved", taskId, fromColumn, toColumn, newPosition);
        } catch (error) {
            console.error("Error notifying card move:", error);
        }
    }

    async notifyCommentAdded(taskId, comment, authorName) {
        if (!this.isConnected) return;

        try {
            await this.connection.invoke("CommentAdded", taskId, comment, authorName);
        } catch (error) {
            console.error("Error notifying comment added:", error);
        }
    }

    // Event handlers
    handleTaskUpdated(data) {
        console.log("📝 Task updated:", data);
        
        // Update task in UI if visible
        this.updateTaskInUI(data);
        
        // Show notification if not the current user's update
        if (data.UpdatedBy !== this.getCurrentUserName()) {
            this.showNotification(
                `Task Updated: ${data.TaskTitle}`,
                `Updated by ${data.UpdatedBy}`,
                "info"
            );
        }

        // Trigger custom event handlers
        this.triggerEvent("taskUpdated", data);
    }

    handleTaskAssigned(data) {
        console.log("👤 Task assigned:", data);
        
        this.showNotification(
            `Task Assigned: ${data.TaskTitle}`,
            `Assigned to ${data.AssignedTo} by ${data.AssignedBy}`,
            "info"
        );

        this.triggerEvent("taskAssigned", data);
    }

    handleCommentAdded(data) {
        console.log("💬 Comment added:", data);
        
        // Add comment to UI if on task details page
        this.addCommentToUI(data);
        
        // Show notification
        this.showNotification(
            `New Comment: ${data.TaskTitle}`,
            `${data.AuthorName}: ${data.Comment.substring(0, 50)}...`,
            "info"
        );

        this.triggerEvent("commentAdded", data);
    }

    handleCardMoved(data) {
        console.log("🔄 Card moved:", data);
        
        // Update kanban board if visible
        this.updateKanbanBoard(data);
        
        this.triggerEvent("cardMoved", data);
    }

    handleNotificationReceived(data) {
        console.log("🔔 Notification received:", data);
        
        // Add to notification queue
        this.notificationQueue.push(data);
        
        // Show notification
        this.showNotification(data.Title, data.Message, data.Type, data);
        
        // Update notification badge
        this.updateNotificationBadge();

        this.triggerEvent("notificationReceived", data);
    }

    handleUserPresenceChanged(data) {
        console.log("👥 User presence changed:", data);
        
        // Update online users list
        if (data.IsOnline) {
            this.onlineUsers.set(data.UserId, {
                userId: data.UserId,
                userName: data.UserName,
                isOnline: true,
                lastSeen: data.LastSeen,
                currentActivity: data.CurrentActivity
            });
        } else {
            this.onlineUsers.delete(data.UserId);
        }
        
        // Update presence indicators in UI
        this.updatePresenceIndicators();

        this.triggerEvent("userPresenceChanged", data);
    }

    handleUserActivityUpdated(data) {
        console.log("🎯 User activity updated:", data);
        
        // Update user activity
        this.userActivities.set(data.UserId, {
            activity: data.Activity,
            timestamp: data.Timestamp,
            taskId: data.TaskId
        });
        
        // Update activity indicators
        this.updateActivityIndicators();

        this.triggerEvent("userActivityUpdated", data);
    }

    handleProjectUpdated(data) {
        console.log("📁 Project updated:", data);
        
        this.showNotification(
            `Project Updated: ${data.ProjectName}`,
            `Updated by ${data.UpdatedBy}`,
            "info"
        );

        this.triggerEvent("projectUpdated", data);
    }

    // UI update methods
    updateTaskInUI(data) {
        // Update task card if visible
        const taskCard = document.querySelector(`[data-task-id="${data.TaskId}"]`);
        if (taskCard) {
            // Add visual indicator for real-time update
            taskCard.classList.add('real-time-updated');
            setTimeout(() => taskCard.classList.remove('real-time-updated'), 2000);
        }
    }

    addCommentToUI(data) {
        const commentsContainer = document.querySelector('.comments-container');
        if (commentsContainer && window.location.pathname.includes(`/Tasks/Details/${data.TaskId}`)) {
            // Add new comment to the UI
            const commentHtml = this.createCommentHTML(data);
            commentsContainer.insertAdjacentHTML('beforeend', commentHtml);
            
            // Scroll to new comment
            commentsContainer.scrollTop = commentsContainer.scrollHeight;
        }
    }

    updateKanbanBoard(data) {
        // Update kanban board if on agile view
        if (window.location.pathname.includes('/Agile')) {
            const taskCard = document.querySelector(`[data-task-id="${data.TaskId}"]`);
            if (taskCard) {
                // Move card to new column
                const targetColumn = document.querySelector(`[data-column="${data.ToColumn}"] .kanban-cards`);
                if (targetColumn) {
                    targetColumn.appendChild(taskCard);
                    
                    // Add visual feedback
                    taskCard.classList.add('real-time-moved');
                    setTimeout(() => taskCard.classList.remove('real-time-moved'), 1000);
                }
            }
        }
    }

    updateOnlineUsers(users) {
        this.onlineUsers.clear();
        users.forEach(user => {
            this.onlineUsers.set(user.userId, user);
        });
        this.updatePresenceIndicators();
    }

    updatePresenceIndicators() {
        // Update online user indicators throughout the UI
        document.querySelectorAll('[data-user-id]').forEach(element => {
            const userId = element.dataset.userId;
            const isOnline = this.onlineUsers.has(userId);
            
            // Add/remove online indicator
            const indicator = element.querySelector('.presence-indicator') || this.createPresenceIndicator();
            indicator.classList.toggle('online', isOnline);
            indicator.classList.toggle('offline', !isOnline);
            
            if (!element.querySelector('.presence-indicator')) {
                element.appendChild(indicator);
            }
        });
    }

    updateActivityIndicators() {
        // Update activity indicators for users
        this.userActivities.forEach((activity, userId) => {
            const userElement = document.querySelector(`[data-user-id="${userId}"]`);
            if (userElement) {
                const activityElement = userElement.querySelector('.activity-indicator');
                if (activityElement) {
                    activityElement.textContent = activity.activity;
                    activityElement.title = `Last activity: ${new Date(activity.timestamp).toLocaleTimeString()}`;
                }
            }
        });
    }

    // Notification methods
    showNotification(title, message, type = "info", data = null) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">&times;</button>
        `;

        // Add to notification container
        const container = this.getNotificationContainer();
        container.appendChild(notification);

        // Auto-remove after delay
        setTimeout(() => {
            notification.remove();
        }, 5000);

        // Close button handler
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
    }

    showConnectionStatus(status, type) {
        const statusElement = document.querySelector('.connection-status');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = `connection-status ${type}`;
        }
    }

    updateNotificationBadge() {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            const unreadCount = this.notificationQueue.filter(n => !n.read).length;
            badge.textContent = unreadCount > 0 ? unreadCount : '';
            badge.style.display = unreadCount > 0 ? 'block' : 'none';
        }
    }

    // Utility methods
    getCurrentProjectId() {
        // Try to get project ID from various sources
        const projectIdElement = document.querySelector('[data-project-id]');
        if (projectIdElement) {
            return parseInt(projectIdElement.dataset.projectId);
        }
        
        // Try to extract from URL
        const urlMatch = window.location.pathname.match(/\/Projects\/Details\/(\d+)/);
        if (urlMatch) {
            return parseInt(urlMatch[1]);
        }
        
        return null;
    }

    getCurrentUserName() {
        const userElement = document.querySelector('[data-current-user]');
        return userElement ? userElement.dataset.currentUser : 'Unknown';
    }

    getNotificationContainer() {
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        return container;
    }

    createPresenceIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'presence-indicator';
        return indicator;
    }

    createCommentHTML(data) {
        return `
            <div class="comment real-time-comment">
                <div class="comment-author">${data.AuthorName}</div>
                <div class="comment-content">${data.Comment}</div>
                <div class="comment-time">${new Date(data.CreatedAt).toLocaleString()}</div>
            </div>
        `;
    }

    // Event system
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }

    off(event, handler) {
        if (this.eventHandlers.has(event)) {
            const handlers = this.eventHandlers.get(event);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    triggerEvent(event, data) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }

    // Public API methods
    getOnlineUsers() {
        return Array.from(this.onlineUsers.values());
    }

    getUserActivity(userId) {
        return this.userActivities.get(userId);
    }

    isUserOnline(userId) {
        return this.onlineUsers.has(userId);
    }
}

// Global instance
window.collaboration = new CollaborationManager();

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("🚀 Collaboration system initialized");
    
    // Set up activity tracking for common actions
    document.addEventListener('click', function(e) {
        if (e.target.matches('.task-card, .task-link')) {
            const taskId = e.target.dataset.taskId;
            if (taskId) {
                window.collaboration.updateActivity(`Viewing task ${taskId}`, parseInt(taskId));
            }
        }
    });
    
    // Track page navigation
    window.addEventListener('beforeunload', function() {
        window.collaboration.updateActivity('Navigating away');
    });
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CollaborationManager;
}
