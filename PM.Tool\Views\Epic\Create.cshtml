@model EpicCreateViewModel
@using PM.Tool.Core.Entities.Agile
@{
    ViewData["Title"] = "Create Epic";
    ViewData["Breadcrumbs"] = new List<object>
    {
        new { Text = "Home", Href = "/", Icon = "fas fa-home" },
        new { Text = "Agile", Href = "/Agile", Icon = "fas fa-tasks" },
        new { Text = "Epics", Href = "/Epic", Icon = "fas fa-mountain" },
        new { Text = "Create Epic", Href = (string?)null, Icon = "fas fa-plus" }
    };
}

<div class="min-h-screen bg-neutral-50 dark:bg-dark-950 transition-colors duration-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-50 flex items-center">
                    <i class="fas fa-plus text-primary-600 dark:text-primary-400 mr-3"></i>
                    Create Epic
                </h1>
                <p class="text-neutral-600 dark:text-dark-300 mt-1">Create a new epic to organize your project features and user stories</p>
            </div>

            <div class="flex gap-3">
                <a href="@Url.Action("Index", new { projectId = Model.ProjectId })" class="btn-outline-custom">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Epics
                </a>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Form -->
            <div class="lg:col-span-2">
                <div class="card-custom">
                    <!-- Card Header -->
                    <div class="card-header-custom">
                        <h2 class="text-xl font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                            <i class="fas fa-mountain text-primary-600 dark:text-primary-400 mr-3"></i>
                            Epic Details
                        </h2>
                    </div>

                    <div class="card-body-custom">
                        <form asp-action="Create" method="post" id="epicForm" class="space-y-6">
                            <div asp-validation-summary="ModelOnly" class="alert-custom alert-danger" role="alert"></div>

                            <!-- Project Selection -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label asp-for="ProjectId" class="form-label-custom required">Project</label>
                                    <select asp-for="ProjectId" class="form-select-custom" required>
                                        <option value="">Select a project</option>
                                        @if (ViewBag.UserProjects != null)
                                        {
                                            @foreach (var project in (List<ProjectSelectViewModel>)ViewBag.UserProjects)
                                            {
                                                <option value="@project.Id" selected="@(project.Id == Model.ProjectId)">
                                                    @project.Name
                                                </option>
                                            }
                                        }
                                    </select>
                                    <span asp-validation-for="ProjectId" class="form-error-custom"></span>
                                </div>

                                <div>
                                    <label asp-for="Priority" class="form-label-custom">Priority</label>
                                    <select asp-for="Priority" class="form-select-custom">
                                        <option value="@((int)EpicPriority.Critical)">Critical</option>
                                        <option value="@((int)EpicPriority.High)">High</option>
                                        <option value="@((int)EpicPriority.Medium)" selected>Medium</option>
                                        <option value="@((int)EpicPriority.Low)">Low</option>
                                    </select>
                                    <span asp-validation-for="Priority" class="form-error-custom"></span>
                                </div>

                                <div>
                                    <label class="form-label-custom">Status</label>
                                    <select class="form-select-custom opacity-60" disabled>
                                        <option selected>Draft</option>
                                    </select>
                                    <p class="form-help-custom">New epics start as Draft</p>
                                </div>
                            </div>

                            <!-- Basic Information -->
                            <div>
                                <label asp-for="Title" class="form-label-custom required">Epic Title</label>
                                <input asp-for="Title" class="form-input-custom" placeholder="Enter a descriptive title for your epic" maxlength="200" required>
                                <span asp-validation-for="Title" class="form-error-custom"></span>
                                <p class="form-help-custom">A clear, concise title that describes the epic's purpose</p>
                            </div>

                            <div>
                                <label asp-for="Description" class="form-label-custom required">Description</label>
                                <textarea asp-for="Description" class="form-textarea-custom" rows="4" placeholder="Describe what this epic aims to achieve..." maxlength="5000" required></textarea>
                                <span asp-validation-for="Description" class="form-error-custom"></span>
                                <p class="form-help-custom">Provide a detailed description of the epic's goals and scope</p>
                            </div>

                            <!-- Business Value & Acceptance Criteria -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label asp-for="BusinessValue" class="form-label-custom">Business Value</label>
                                    <textarea asp-for="BusinessValue" class="form-textarea-custom" rows="3" placeholder="Explain the business value this epic will deliver..." maxlength="1000"></textarea>
                                    <span asp-validation-for="BusinessValue" class="form-error-custom"></span>
                                    <p class="form-help-custom">Why is this epic important to the business?</p>
                                </div>

                                <div>
                                    <label asp-for="AcceptanceCriteria" class="form-label-custom">Acceptance Criteria</label>
                                    <textarea asp-for="AcceptanceCriteria" class="form-textarea-custom" rows="3" placeholder="Define the criteria for epic completion..." maxlength="2000"></textarea>
                                    <span asp-validation-for="AcceptanceCriteria" class="form-error-custom"></span>
                                    <p class="form-help-custom">What conditions must be met for this epic to be considered done?</p>
                                </div>
                            </div>

                            <!-- Planning Information -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label asp-for="EstimatedStoryPoints" class="form-label-custom">Estimated Story Points</label>
                                    <input asp-for="EstimatedStoryPoints" type="number" class="form-input-custom" min="0" step="0.5" placeholder="0">
                                    <span asp-validation-for="EstimatedStoryPoints" class="form-error-custom"></span>
                                    <p class="form-help-custom">Rough estimate of the epic's size</p>
                                </div>

                                <div>
                                    <label asp-for="TargetDate" class="form-label-custom">Target Completion Date</label>
                                    <input asp-for="TargetDate" type="date" class="form-input-custom">
                                    <span asp-validation-for="TargetDate" class="form-error-custom"></span>
                                    <p class="form-help-custom">When should this epic be completed?</p>
                                </div>

                                <div>
                                    <label asp-for="OwnerId" class="form-label-custom">Epic Owner</label>
                                    <select asp-for="OwnerId" class="form-select-custom">
                                        <option value="">Select owner</option>
                                        @if (ViewBag.TeamMembers != null)
                                        {
                                            @foreach (var member in (List<UserSelectViewModel>)ViewBag.TeamMembers)
                                            {
                                                <option value="@member.Id">@member.Name</option>
                                            }
                                        }
                                    </select>
                                    <span asp-validation-for="OwnerId" class="form-error-custom"></span>
                                    <p class="form-help-custom">Who is responsible for this epic?</p>
                                </div>
                            </div>

                            <!-- Tags -->
                            <div>
                                <label asp-for="Tags" class="form-label-custom">Tags</label>
                                <input asp-for="Tags" class="form-input-custom" placeholder="Enter tags separated by commas (e.g., frontend, api, mobile)" maxlength="500">
                                <span asp-validation-for="Tags" class="form-error-custom"></span>
                                <p class="form-help-custom">Add tags to help categorize and filter this epic</p>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-6 border-t border-neutral-200 dark:border-dark-700">
                                <div class="flex items-center mb-4 sm:mb-0">
                                    <input class="form-checkbox-custom" type="checkbox" id="createAnother">
                                    <label class="ml-2 text-sm text-neutral-700 dark:text-dark-300" for="createAnother">
                                        Create another epic after saving
                                    </label>
                                </div>

                                <div class="flex gap-3">
                                    <a href="@Url.Action("Index", new { projectId = Model.ProjectId })" class="btn-outline-custom">
                                        <i class="fas fa-times mr-2"></i>
                                        Cancel
                                    </a>
                                    <button type="button" class="btn-secondary-custom" id="saveAsDraft">
                                        <i class="fas fa-save mr-2"></i>
                                        Save as Draft
                                    </button>
                                    <button type="submit" class="btn-primary-custom">
                                        <i class="fas fa-check mr-2"></i>
                                        Create Epic
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar with Tips -->
            <div class="space-y-6">
                <!-- Epic Creation Tips -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                            <i class="fas fa-lightbulb text-warning-600 dark:text-warning-400 mr-2"></i>
                            Epic Creation Tips
                        </h3>
                    </div>
                    <div class="card-body-custom">
                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                                        <i class="fas fa-target text-primary-600 dark:text-primary-400 text-sm"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-50 mb-1">Define Clear Goals</h4>
                                    <p class="text-sm text-neutral-600 dark:text-dark-300">Make sure your epic has a clear, measurable goal that aligns with business objectives.</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 rounded-full bg-info-100 dark:bg-info-900/30 flex items-center justify-center">
                                        <i class="fas fa-puzzle-piece text-info-600 dark:text-info-400 text-sm"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-50 mb-1">Break Down Later</h4>
                                    <p class="text-sm text-neutral-600 dark:text-dark-300">After creating the epic, you can break it down into features and user stories.</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center">
                                        <i class="fas fa-users text-success-600 dark:text-success-400 text-sm"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-50 mb-1">Assign Ownership</h4>
                                    <p class="text-sm text-neutral-600 dark:text-dark-300">Assign an epic owner who will be responsible for its success and coordination.</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 rounded-full bg-warning-100 dark:bg-warning-900/30 flex items-center justify-center">
                                        <i class="fas fa-chart-line text-warning-600 dark:text-warning-400 text-sm"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-50 mb-1">Estimate Roughly</h4>
                                    <p class="text-sm text-neutral-600 dark:text-dark-300">Provide a rough story point estimate. You can refine this as you break down the epic.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Epic Template -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                            <i class="fas fa-file-alt text-info-600 dark:text-info-400 mr-2"></i>
                            Epic Template
                        </h3>
                    </div>
                    <div class="card-body-custom">
                        <p class="text-neutral-600 dark:text-dark-300 mb-4">Use this template as a starting point:</p>
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-50 mb-2">Title Format:</h4>
                                <div class="bg-neutral-100 dark:bg-dark-800 rounded-lg p-3">
                                    <code class="text-sm text-neutral-800 dark:text-dark-200">[User Type] [Action] [Outcome]</code>
                                </div>
                            </div>

                            <div>
                                <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-50 mb-2">Description Template:</h4>
                                <div class="bg-neutral-100 dark:bg-dark-800 rounded-lg p-3 space-y-2">
                                    <p class="text-sm text-neutral-800 dark:text-dark-200"><strong>Goal:</strong> What we want to achieve</p>
                                    <p class="text-sm text-neutral-800 dark:text-dark-200"><strong>Why:</strong> Business justification</p>
                                    <p class="text-sm text-neutral-800 dark:text-dark-200"><strong>Success Criteria:</strong> How we'll know we're done</p>
                                </div>
                            </div>

                            <button class="btn-outline-custom w-full" id="useTemplate">
                                <i class="fas fa-copy mr-2"></i>
                                Use Template
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Project selection change
            $('#ProjectId').on('change', function() {
                const projectId = $(this).val();
                if (projectId) {
                    // Reload page with selected project to get team members
                    window.location.href = '@Url.Action("Create")?projectId=' + projectId;
                }
            });

            // Save as draft
            $('#saveAsDraft').on('click', function() {
                // Set status to draft and submit
                $('<input>').attr({
                    type: 'hidden',
                    name: 'Status',
                    value: '1' // Draft
                }).appendTo('#epicForm');
                $('#epicForm').submit();
            });

            // Use template
            $('#useTemplate').on('click', function() {
                const title = $('#Title').val();
                const description = $('#Description').val();

                if (!title) {
                    $('#Title').val('[User Type] [Action] [Outcome]');
                }

                if (!description) {
                    $('#Description').val(`Goal: What we want to achieve

Why: Business justification

Success Criteria: How we'll know we're done`);
                }

                // Focus on title for editing
                $('#Title').focus().select();
            });

            // Form validation
            $('#epicForm').on('submit', function(e) {
                const projectId = $('#ProjectId').val();
                if (!projectId) {
                    e.preventDefault();
                    alert('Please select a project for this epic.');
                    $('#ProjectId').focus();
                    return false;
                }

                const title = $('#Title').val().trim();
                if (!title) {
                    e.preventDefault();
                    alert('Please enter a title for the epic.');
                    $('#Title').focus();
                    return false;
                }

                const description = $('#Description').val().trim();
                if (!description) {
                    e.preventDefault();
                    alert('Please enter a description for the epic.');
                    $('#Description').focus();
                    return false;
                }

                // Show loading state
                const submitBtn = $(this).find('button[type="submit"]');
                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Creating...');
            });

            // Character counters
            $('#Title').on('input', function() {
                updateCharacterCounter(this, 200);
            });

            $('#Description').on('input', function() {
                updateCharacterCounter(this, 5000);
            });

            $('#BusinessValue').on('input', function() {
                updateCharacterCounter(this, 1000);
            });

            $('#AcceptanceCriteria').on('input', function() {
                updateCharacterCounter(this, 2000);
            });
        });

        function updateCharacterCounter(element, maxLength) {
            const current = element.value.length;
            const remaining = maxLength - current;

            let counterElement = $(element).siblings('.character-counter');
            if (counterElement.length === 0) {
                counterElement = $('<div class="character-counter text-gray-500 dark:text-gray-400 text-xs mt-1"></div>');
                $(element).after(counterElement);
            }

            counterElement.text(`${current}/${maxLength} characters`);

            if (remaining < 50) {
                counterElement.removeClass('text-gray-500 dark:text-gray-400').addClass('text-yellow-600 dark:text-yellow-400');
            } else if (remaining < 0) {
                counterElement.removeClass('text-yellow-600 dark:text-yellow-400').addClass('text-red-600 dark:text-red-400');
            } else {
                counterElement.removeClass('text-yellow-600 dark:text-yellow-400 text-red-600 dark:text-red-400').addClass('text-gray-500 dark:text-gray-400');
            }
        }
    </script>
}


