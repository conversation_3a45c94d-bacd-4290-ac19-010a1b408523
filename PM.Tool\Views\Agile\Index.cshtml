@model IEnumerable<PM.Tool.Core.Entities.Agile.Epic>
@{
    ViewData["Title"] = "Agile Management";
    var hasProjects = ViewBag.HasProjects as bool? ?? false;
    var currentProject = ViewBag.CurrentProject as PM.Tool.Core.Entities.Project;

    // Standardized Page Header
    ViewData["PageTitle"] = "Agile Management";
    ViewData["PageSubtitle"] = "Manage epics, sprints, and user stories across your projects";
    ViewData["PageIcon"] = "fas fa-rocket";

    var headerActions = new List<object>();
    if (hasProjects)
    {
        if (currentProject != null)
        {
            headerActions.Add(new { Text = "Advanced Analytics", Variant = "primary", Icon = "fas fa-chart-line", Href = Url.Action("Analytics", "Agile", new { projectId = currentProject.Id }) });
        }
        headerActions.Add(new { Text = "Select Project", Variant = "outline", Icon = "fas fa-project-diagram", Href = Url.Action("Index", "Projects") });
    }
    ViewData["HeaderActions"] = headerActions;
}

<!-- Standardized Page Header -->
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Real-time Collaboration Status -->
@if (hasProjects && ViewBag.CurrentProject != null)
{
    <div class="mb-6">
        <div class="bg-white dark:bg-dark-800 rounded-lg shadow-sm border border-neutral-200 dark:border-dark-600 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-users text-primary-600 dark:text-primary-400 mr-3"></i>
                    <h3 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">Team Collaboration</h3>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="presence-indicator online mr-2"></div>
                        <span class="text-sm text-neutral-600 dark:text-dark-300">
                            <span id="onlineTeamCount">0</span> online
                        </span>
                    </div>
                    <div class="connection-status success" id="agileConnectionStatus">
                        <i class="fas fa-circle mr-1"></i>
                        Connected
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <div id="teamPresenceList" class="flex -space-x-2">
                    <!-- Online team members will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
}

@if (!hasProjects)
{
    <!-- Empty State -->
    <div class="text-center py-16">
        <div class="w-24 h-24 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-project-diagram text-4xl text-neutral-400 dark:text-dark-500"></i>
        </div>
        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No Projects Found</h3>
        <p class="text-neutral-500 dark:text-dark-400 mb-8 max-w-md mx-auto">
            You need to create or be assigned to a project before you can use agile management features.
        </p>
        @{
            ViewData["Text"] = "Create Your First Project";
            ViewData["Variant"] = "primary";
            ViewData["Icon"] = "fas fa-plus";
            ViewData["Href"] = Url.Action("Create", "Projects");
        }
        <partial name="Components/_Button" view-data="ViewData" />
    </div>
}
else
{
    <!-- Info Banner -->
    <div class="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4 mb-8">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mt-0.5"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm text-primary-700 dark:text-primary-300">
                    Showing agile management overview across <span class="font-semibold">@ViewBag.ProjectCount project(s)</span>.
                    <a href="@Url.Action("Index", "Projects")" class="font-medium underline hover:no-underline">
                        Select a specific project
                    </a> for detailed agile management.
                </p>
            </div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="border-b border-neutral-200 dark:border-dark-200 mb-8">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button class="tab-button active" id="epics-tab" data-target="#epics" type="button" role="tab" aria-selected="true">
                <i class="fas fa-mountain mr-2"></i>
                Epics
            </button>
            <button class="tab-button" id="sprints-tab" data-target="#sprints" type="button" role="tab" aria-selected="false">
                <i class="fas fa-clock mr-2"></i>
                Sprints
            </button>
            <button class="tab-button" id="backlog-tab" data-target="#backlog" type="button" role="tab" aria-selected="false">
                <i class="fas fa-list mr-2"></i>
                Product Backlog
            </button>
            <a href="@Url.Action("Burndown", "Agile", new { projectId = ViewBag.ProjectId })" class="tab-button">
                <i class="fas fa-chart-line mr-2"></i>
                Burndown Charts
            </a>
        </nav>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- Epics Tab -->
        <div class="tab-panel active" id="epics" role="tabpanel">
            @{
                ViewData["Title"] = $"Epics Overview ({Model?.Count() ?? 0})";
                ViewData["Icon"] = "fas fa-layer-group";
                ViewData["HeaderActions"] = currentProject != null ? new List<object> {
                    new { Text = "Create Epic", Variant = "primary", Icon = "fas fa-plus", Size = "sm", Href = Url.Action("CreateEpic", new { projectId = currentProject.Id }) }
                } : new List<object>();
            }
            <partial name="Components/_Card" view-data="ViewData">
                @if (Model != null && Model.Any())
                {
                    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                        @foreach (var epic in Model)
                        {
                            <partial name="Components/_EpicCard" model="epic" />
                        }
                    </div>
                }
                else
                {
                        ViewData["Icon"] = "fas fa-layer-group";
                        ViewData["Title"] = "No Epics Found";
                        ViewData["Description"] = currentProject != null
                            ? "Create your first epic to get started with agile project management."
                            : "Select a project to view and manage epics.";
                        ViewData["ActionText"] = currentProject != null ? "Create First Epic" : "Select Project";
                        ViewData["ActionIcon"] = "fas fa-plus";
                        ViewData["ActionHref"] = currentProject != null
                            ? Url.Action("CreateEpic", new { projectId = currentProject.Id })
                            : Url.Action("Index", "Projects");
                    <partial name="Components/_EmptyState" view-data="ViewData" />
                }
            </partial>
        </div>

        <!-- Sprints Tab -->
        <div class="tab-panel" id="sprints" role="tabpanel">
            @{
                ViewData["Title"] = "Sprint Management";
                ViewData["Icon"] = "fas fa-clock";
            }
            <partial name="Components/_Card" view-data="ViewData">
                <div id="sprintsContainer">
                    <div class="flex items-center justify-center py-12">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                        <span class="ml-3 text-neutral-600 dark:text-dark-400">Loading sprints...</span>
                    </div>
                </div>
            </partial>
        </div>

        <!-- Backlog Tab -->
        <div class="tab-panel" id="backlog" role="tabpanel">
            @{
                ViewData["Title"] = "Product Backlog";
                ViewData["Icon"] = "fas fa-list";
            }
            <partial name="Components/_Card" view-data="ViewData">
                <div id="backlogContainer">
                    <div class="flex items-center justify-center py-12">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                        <span class="ml-3 text-neutral-600 dark:text-dark-400">Loading backlog...</span>
                    </div>
                </div>
            </partial>
        </div>

        <!-- Analytics Tab -->
        <div class="tab-panel" id="burndown" role="tabpanel">
            @{
                ViewData["Title"] = "Analytics & Burndown";
                ViewData["Icon"] = "fas fa-chart-line";
            }
            <partial name="Components/_Card" view-data="ViewData">
                <div class="bg-neutral-50 dark:bg-dark-700 rounded-lg p-8">
                    <canvas id="burndownChart" class="w-full h-96"></canvas>
                </div>
            </partial>
        </div>
    </div>
} <!-- End of hasProjects else block -->

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize real-time collaboration
            initializeCollaboration();

            // Tab functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanels = document.querySelectorAll('.tab-panel');

            tabButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const targetId = this.getAttribute('data-target');

                    // Skip processing if this is a link without data-target (like Burndown Charts)
                    if (!targetId) {
                        return; // Let the link navigate normally
                    }

                    // Prevent default behavior for tab buttons
                    e.preventDefault();

                    // Remove active class from all tabs and panels
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.setAttribute('aria-selected', 'false');
                    });
                    tabPanels.forEach(panel => panel.classList.remove('active'));

                    // Add active class to clicked tab and corresponding panel
                    this.classList.add('active');
                    this.setAttribute('aria-selected', 'true');

                    const targetPanel = document.querySelector(targetId);
                    if (targetPanel) {
                        targetPanel.classList.add('active');
                    }

                    // Load content based on tab
                    if (targetId === '#sprints') {
                        loadSprints();
                    } else if (targetId === '#backlog') {
                        loadBacklog();
                    } else if (targetId === '#burndown') {
                        loadBurndownChart();
                    }
                });
            });
        });

        function loadSprints() {
            const container = document.getElementById('sprintsContainer');
            container.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-neutral-600 dark:text-dark-400">Loading sprints...</span>
                </div>
            `;

            fetch('@Url.Action("GetSprints", "Agile")')
                .then(response => response.json())
                .then(data => {
                    if (data.html) {
                        container.innerHTML = data.html;
                    } else {
                        container.innerHTML = `
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-clock text-2xl text-neutral-400 dark:text-dark-500"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No Sprints Found</h3>
                                <p class="text-neutral-500 dark:text-dark-400">Select a project to view and manage sprints.</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    container.innerHTML = `
                        <div class="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 mr-3"></i>
                                <span class="text-danger-700 dark:text-danger-300">Failed to load sprints. Please try again.</span>
                            </div>
                        </div>
                    `;
                });
        }

        function loadBacklog() {
            const container = document.getElementById('backlogContainer');
            container.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-neutral-600 dark:text-dark-400">Loading backlog...</span>
                </div>
            `;

            fetch('@Url.Action("GetBacklog", "Agile")')
                .then(response => response.json())
                .then(data => {
                    if (data.html) {
                        container.innerHTML = data.html;
                    } else {
                        container.innerHTML = `
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-list text-2xl text-neutral-400 dark:text-dark-500"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No User Stories Found</h3>
                                <p class="text-neutral-500 dark:text-dark-400">Select a project to view and manage the product backlog.</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    container.innerHTML = `
                        <div class="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 mr-3"></i>
                                <span class="text-danger-700 dark:text-danger-300">Failed to load backlog. Please try again.</span>
                            </div>
                        </div>
                    `;
                });
        }

        function loadBurndownChart() {
            fetch('@Url.Action("GetBurndownData", "Agile")')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Failed to load burndown data:', data.error);
                        return;
                    }
                    renderBurndownChart(data);
                })
                .catch(error => {
                    console.error('Failed to load burndown data:', error);
                });
        }

        function renderBurndownChart(data) {
            const ctx = document.getElementById('burndownChart').getContext('2d');

            // Destroy existing chart if it exists
            if (window.burndownChart) {
                window.burndownChart.destroy();
            }

            window.burndownChart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Sprint Burndown Chart',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Story Points'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Sprint Days'
                            }
                        }
                    }
                }
            });
        }

        // Real-time Collaboration Functions
        function initializeCollaboration() {
            if (window.collaboration) {
                // Update connection status
                updateConnectionStatus(window.collaboration.isConnected);

                // Listen for connection changes
                window.collaboration.on('connectionStateChanged', (isConnected) => {
                    updateConnectionStatus(isConnected);
                });

                // Listen for user presence changes
                window.collaboration.on('userPresenceChanged', (data) => {
                    updateTeamPresence();
                });

                // Join project room if we have a current project
                const projectId = '@ViewBag.CurrentProject?.Id';
                if (projectId) {
                    window.collaboration.joinProject(projectId);
                }

                // Initial load of team presence
                updateTeamPresence();
            } else {
                console.warn('Collaboration system not available');
                updateConnectionStatus(false);
            }
        }

        function updateConnectionStatus(isConnected) {
            const statusElement = document.getElementById('agileConnectionStatus');
            if (statusElement) {
                if (isConnected) {
                    statusElement.className = 'connection-status success';
                    statusElement.innerHTML = '<i class="fas fa-circle mr-1"></i>Connected';
                } else {
                    statusElement.className = 'connection-status error';
                    statusElement.innerHTML = '<i class="fas fa-circle mr-1"></i>Disconnected';
                }
            }
        }

        function updateTeamPresence() {
            if (!window.collaboration) return;

            const onlineUsers = window.collaboration.getOnlineUsers();
            const countElement = document.getElementById('onlineTeamCount');
            const listElement = document.getElementById('teamPresenceList');

            if (countElement) {
                countElement.textContent = onlineUsers.length;
            }

            if (listElement) {
                listElement.innerHTML = '';

                onlineUsers.slice(0, 8).forEach(user => {
                    const avatar = document.createElement('div');
                    avatar.className = 'w-8 h-8 rounded-full bg-primary-600 text-white text-xs font-medium flex items-center justify-center border-2 border-white dark:border-dark-800';
                    avatar.textContent = user.userName.charAt(0).toUpperCase();
                    avatar.title = `${user.userName} - ${user.currentActivity}`;
                    listElement.appendChild(avatar);
                });

                if (onlineUsers.length > 8) {
                    const moreIndicator = document.createElement('div');
                    moreIndicator.className = 'w-8 h-8 rounded-full bg-neutral-400 text-white text-xs font-medium flex items-center justify-center border-2 border-white dark:border-dark-800';
                    moreIndicator.textContent = `+${onlineUsers.length - 8}`;
                    moreIndicator.title = `${onlineUsers.length - 8} more team members online`;
                    listElement.appendChild(moreIndicator);
                }
            }
        }
    </script>
}

@section Styles {
    <style>
        /* Custom Tab Styling */
        .tab-button {
            white-space: nowrap;
            padding: 0.5rem 0.25rem;
            border-bottom: 2px solid transparent;
            font-weight: 500;
            font-size: 0.875rem;
            color: #6b7280;
            transition: all 0.2s;
            cursor: pointer;
            background: transparent;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .dark .tab-button {
            color: #9ca3af;
        }

        .tab-button:hover {
            color: #374151;
            border-bottom-color: #d1d5db;
        }

        .dark .tab-button:hover {
            color: #e5e7eb;
            border-bottom-color: #4b5563;
        }

        .tab-button.active {
            border-bottom-color: #2563eb;
            color: #2563eb;
        }

        .dark .tab-button.active {
            color: #60a5fa;
        }

        .tab-button:focus {
            outline: none;
            box-shadow: 0 0 0 2px #2563eb;
        }

        /* Tab Panel Styling */
        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        /* Epic Card Hover Effects */
        .epic-card {
            transition: all 0.3s;
        }

        .epic-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Line Clamp Utility */
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Chart Container */
        #burndownChart {
            width: 100%;
            height: 400px !important;
        }

        /* Real-time Collaboration Styles */
        .presence-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }

        .presence-indicator.online {
            background-color: #10b981;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
            animation: pulse-green 2s infinite;
        }

        .presence-indicator.away {
            background-color: #f59e0b;
        }

        .presence-indicator.offline {
            background-color: #6b7280;
        }

        .connection-status {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .connection-status.success {
            background-color: #dcfce7;
            color: #166534;
        }

        .dark .connection-status.success {
            background-color: #14532d;
            color: #bbf7d0;
        }

        .connection-status.error {
            background-color: #fef2f2;
            color: #991b1b;
        }

        .dark .connection-status.error {
            background-color: #7f1d1d;
            color: #fecaca;
        }

        @@keyframes pulse-green {
            0% {
                box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
            }
            70% {
                box-shadow: 0 0 0 6px rgba(16, 185, 129, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
            }
        }
    </style>
}
