namespace PM.Tool.Core.Enums
{
    public enum ProjectStatus
    {
        Planning = 1,
        Active = 2,
        InProgress = 3,
        OnHold = 4,
        Completed = 5,
        Cancelled = 6
    }

    public enum TaskStatus
    {
        ToDo = 1,
        InProgress = 2,
        InReview = 3,
        Done = 4,
        Cancelled = 5
    }

    public enum TaskPriority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    public enum UserRole
    {
        Admin = 1,
        ProjectManager = 2,
        TeamMember = 3
    }

    public enum NotificationType
    {
        TaskAssigned = 1,
        TaskUpdated = 2,
        TaskCompleted = 3,
        ProjectUpdated = 4,
        CommentAdded = 5,
        MilestoneReached = 6,
        DeadlineApproaching = 7,
        Mention = 8
    }

    // Meeting-related enums
    public enum MeetingType
    {
        General = 1,
        Standup = 2,
        Planning = 3,
        Review = 4,
        Retrospective = 5,
        Stakeholder = 6,
        Requirements = 7,
        Technical = 8,
        Status = 9
    }

    public enum MeetingStatus
    {
        Scheduled = 1,
        InProgress = 2,
        Completed = 3,
        Cancelled = 4,
        Postponed = 5
    }

    public enum AttendeeRole
    {
        Organizer = 1,
        Presenter = 2,
        Attendee = 3,
        Optional = 4,
        Observer = 5
    }

    public enum AttendanceStatus
    {
        Invited = 1,
        Accepted = 2,
        Declined = 3,
        Tentative = 4,
        Attended = 5,
        NoShow = 6
    }

    public enum ActionItemStatus
    {
        Open = 1,
        InProgress = 2,
        Completed = 3,
        Cancelled = 4,
        Deferred = 5
    }

    public enum DocumentType
    {
        Agenda = 1,
        Minutes = 2,
        Presentation = 3,
        Handout = 4,
        Reference = 5
    }
}
