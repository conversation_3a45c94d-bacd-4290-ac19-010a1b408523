@{
    var title = ViewData["Title"]?.ToString() ?? "";
    var icon = ViewData["Icon"]?.ToString() ?? "";
    var description = ViewData["Description"]?.ToString() ?? "";
    var fields = ViewData["Fields"] as IEnumerable<object> ?? new List<object>();
    var gridCols = ViewData["GridCols"]?.ToString() ?? "grid-cols-1 md:grid-cols-2";
}

<div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 rounded-lg p-6">
    @if (!string.IsNullOrEmpty(title))
    {
        <div class="flex items-center mb-6">
            @if (!string.IsNullOrEmpty(icon))
            {
                <i class="@icon text-neutral-600 dark:text-neutral-400 mr-3"></i>
            }
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100">@title</h3>
                @if (!string.IsNullOrEmpty(description))
                {
                    <p class="text-sm text-neutral-500 dark:text-neutral-400 mt-1">@description</p>
                }
            </div>
        </div>
    }

    <div class="grid @gridCols gap-6">
        @foreach (dynamic field in fields)
        {
            ViewData["Type"] = field.Type ?? "text";
            ViewData["Name"] = field.Name ?? "";
            ViewData["Label"] = field.Label ?? "";
            ViewData["Placeholder"] = field.Placeholder ?? "";
            ViewData["Value"] = field.Value ?? "";
            ViewData["Required"] = field.Required ?? false;
            ViewData["Help"] = field.Help ?? "";
            ViewData["Rows"] = field.Rows ?? 4;
            ViewData["Options"] = field.Options ?? new List<object>();
            ViewData["Classes"] = field.Classes ?? "";
            ViewData["Attributes"] = field.Attributes ?? new Dictionary<string, object>();
            ViewData["ColSpan"] = field.ColSpan ?? "";
            <partial name="Components/_FormField" view-data="ViewData" />
        }
    </div>
</div>
