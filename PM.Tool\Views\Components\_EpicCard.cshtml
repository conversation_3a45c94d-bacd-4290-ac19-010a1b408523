@model PM.Tool.Core.Entities.Agile.Epic
@{
    var priorityColor = Model.Priority.ToString().ToLower() switch {
        "critical" => "bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300",
        "high" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
        "medium" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
        "low" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
        _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
    };
    
    var statusColor = Model.Status.ToString().ToLower() switch {
        "draft" => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300",
        "ready" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
        "inprogress" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
        "done" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
        "cancelled" => "bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300",
        _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
    };
    
    var userStoriesCount = Model.UserStories?.Count() ?? 0;
    var progressPercentage = Model.ProgressPercentage;
    var truncatedDescription = Model.Description?.Length > 100 ? Model.Description.Substring(0, 100) + "..." : Model.Description;
}

<div class="epic-card bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 rounded-lg p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
    <!-- Epic Header -->
    <div class="flex justify-between items-start mb-4">
        <div class="flex items-center gap-2">
            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium @priorityColor">
                @Model.Priority
            </span>
            @if (!string.IsNullOrEmpty(Model.EpicKey))
            {
                <span class="text-xs text-neutral-500 dark:text-neutral-400 font-mono">@Model.EpicKey</span>
            }
        </div>
        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium @statusColor">
            @Model.Status
        </span>
    </div>

    <!-- Epic Content -->
    <div class="mb-4">
        <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2 line-clamp-2">@Model.Title</h3>
        @if (!string.IsNullOrEmpty(truncatedDescription))
        {
            <p class="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-3">@truncatedDescription</p>
        }
    </div>

    <!-- Progress Bar -->
    <div class="mb-4">
        <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-neutral-700 dark:text-neutral-300">Progress</span>
            <span class="text-sm text-neutral-600 dark:text-neutral-400">@progressPercentage.ToString("F1")%</span>
        </div>
        <div class="w-full bg-neutral-200 dark:bg-neutral-600 rounded-full h-2">
            <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: @progressPercentage%"></div>
        </div>
    </div>

    <!-- Epic Footer -->
    <div class="flex justify-between items-center">
        <div class="flex items-center gap-4 text-sm text-neutral-500 dark:text-neutral-400">
            <span class="flex items-center">
                <i class="fas fa-list mr-1"></i>
                @userStoriesCount Stories
            </span>
            @if (Model.EstimatedStoryPoints > 0)
            {
                <span class="flex items-center">
                    <i class="fas fa-chart-bar mr-1"></i>
                    @Model.EstimatedStoryPoints.ToString("F0") SP
                </span>
            }
            @if (Model.TargetDate.HasValue)
            {
                <span class="flex items-center">
                    <i class="fas fa-calendar mr-1"></i>
                    @Model.TargetDate.Value.ToString("MMM dd")
                </span>
            }
        </div>
        <div class="flex gap-2">
            @{
                ViewData["Text"] = "";
                ViewData["Variant"] = "ghost";
                ViewData["Size"] = "sm";
                ViewData["Icon"] = "fas fa-eye";
                ViewData["Href"] = Url.Action("EpicDetails", new { id = Model.Id });
                ViewData["Title"] = "View Details";
            }
            <partial name="Components/_Button" view-data="ViewData" />
            
            @{
                ViewData["Text"] = "";
                ViewData["Variant"] = "ghost";
                ViewData["Size"] = "sm";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("EditEpic", new { id = Model.Id });
                ViewData["Title"] = "Edit Epic";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>
