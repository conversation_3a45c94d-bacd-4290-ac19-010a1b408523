@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model IEnumerable<PM.Tool.Core.Entities.Requirement>
@{
    ViewData["Title"] = "Requirements Management";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Requirements", Href = "", Icon = "fas fa-clipboard-list" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Standardized Page Header -->
@{
    ViewData["Title"] = "Requirements Management";
    ViewData["Description"] = "Manage project requirements, track progress, and ensure complete traceability throughout the project lifecycle";
    ViewData["Icon"] = "fas fa-clipboard-list";
    ViewData["IconColor"] = "text-primary-600 dark:text-primary-400";
    ViewData["Actions"] = new object[] {
        new { Text = "New Requirement", Variant = "primary", Icon = "fas fa-plus", Href = Url.Action("Create"), OnClick = (string)null },
        new { Text = "Import", Variant = "outline", Icon = "fas fa-upload", Href = Url.Action("Import"), OnClick = (string)null },
        new { Text = "Export", Variant = "secondary", Icon = "fas fa-download", Href = Url.Action("Export"), OnClick = (string)null }
    };

    var statsArray = new List<object>
    {
        new { Label = "Total", Value = Model.Count().ToString(), Icon = "fas fa-clipboard-list", Color = "blue" },
        new { Label = "Completed", Value = Model.Count(r => r.Status == PM.Tool.Core.Entities.RequirementStatus.Completed).ToString(), Icon = "fas fa-check-circle", Color = "green" },
        new { Label = "In Progress", Value = Model.Count(r => r.Status == PM.Tool.Core.Entities.RequirementStatus.InProgress).ToString(), Icon = "fas fa-clock", Color = "amber" },
        new { Label = "High Priority", Value = Model.Count(r => r.Priority == PM.Tool.Core.Entities.RequirementPriority.High).ToString(), Icon = "fas fa-exclamation-triangle", Color = "red" }
    };

    ViewData["Stats"] = statsArray.ToArray();
}
<partial name="Components/_PageHeader" view-data="ViewData" />



<!-- Standardized Filter Bar -->
<div class="card-custom mb-8">
    <form id="filterForm" method="get" class="card-body-custom">
        <!-- Single Row Layout -->
        <div class="flex flex-wrap items-center gap-6">
            <!-- Quick Filters Section -->
            <div class="flex items-center gap-3">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-3">Quick:</span>
                <div class="flex items-center gap-2">
                    @{
                        ViewData["Text"] = "Critical";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-exclamation-triangle";
                        ViewData["OnClick"] = "filterRequirements('critical')";
                        ViewData["AdditionalClasses"] = "quick-filter-btn";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "High";
                        ViewData["Icon"] = "fas fa-flag";
                        ViewData["OnClick"] = "filterRequirements('high')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "In Progress";
                        ViewData["Icon"] = "fas fa-clock";
                        ViewData["OnClick"] = "filterRequirements('inprogress')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "Approved";
                        ViewData["Icon"] = "fas fa-check-circle";
                        ViewData["OnClick"] = "filterRequirements('approved')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>

            <!-- Main Filters Section -->
            <div class="flex items-center gap-4 flex-1">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text"
                           name="search"
                           placeholder="Search requirements..."
                           class="w-64 pl-11 pr-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 transition-all duration-200">
                </div>

                <!-- Filter Controls -->
                <div class="flex items-center gap-3">
                    <!-- Status Filter -->
                    <select name="status" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Status</option>
                        <option value="Draft">Draft</option>
                        <option value="UnderReview">Under Review</option>
                        <option value="Approved">Approved</option>
                        <option value="InProgress">In Progress</option>
                        <option value="Completed">Completed</option>
                    </select>

                    <!-- Priority Filter -->
                    <select name="priority" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Priority</option>
                        <option value="Critical">Critical</option>
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                    </select>

                    <!-- Type Filter -->
                    <select name="type" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Types</option>
                        <option value="Functional">Functional</option>
                        <option value="NonFunctional">Non-Functional</option>
                        <option value="Technical">Technical</option>
                        <option value="Business">Business</option>
                    </select>

                    <!-- Clear Filters Button -->
                    @{
                        ViewData["Text"] = "Clear";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-times";
                        ViewData["OnClick"] = "clearAllFilters()";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>
        </div>
    </form>
</div>

<!-- View Toggle and Controls -->
<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
    <!-- View Mode Toggle -->
    <div class="flex bg-neutral-100 dark:bg-dark-700 rounded-lg p-1">
        <button id="listViewBtn" class="view-toggle-btn active px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">
            <i class="fas fa-list mr-2"></i>List View
        </button>
        <button id="hierarchyViewBtn" class="view-toggle-btn px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">
            <i class="fas fa-sitemap mr-2"></i>Hierarchy
        </button>
        <button id="matrixViewBtn" class="view-toggle-btn px-4 py-2 text-sm font-medium rounded-md transition-all duration-200">
            <i class="fas fa-th mr-2"></i>Matrix
        </button>
    </div>

    <!-- Sort Controls -->
    <div class="relative">
        <button id="sortDropdown" class="inline-flex items-center px-4 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg text-sm font-medium text-neutral-700 dark:text-dark-300 bg-white dark:bg-dark-800 hover:bg-neutral-50 dark:hover:bg-dark-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <i class="fas fa-sort mr-2"></i>
            Sort By
            <i class="fas fa-chevron-down ml-2"></i>
        </button>
        <div id="sortMenu" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-dark-800 rounded-lg shadow-lg border border-neutral-200 dark:border-dark-600 z-10">
            <div class="py-1">
                <a href="#" class="sort-option block px-4 py-2 text-sm text-neutral-700 dark:text-dark-300 hover:bg-neutral-100 dark:hover:bg-dark-700" data-sort="priority">
                    <i class="fas fa-exclamation-triangle w-4 mr-2"></i>Priority
                </a>
                <a href="#" class="sort-option block px-4 py-2 text-sm text-neutral-700 dark:text-dark-300 hover:bg-neutral-100 dark:hover:bg-dark-700" data-sort="status">
                    <i class="fas fa-tasks w-4 mr-2"></i>Status
                </a>
                <a href="#" class="sort-option block px-4 py-2 text-sm text-neutral-700 dark:text-dark-300 hover:bg-neutral-100 dark:hover:bg-dark-700" data-sort="created">
                    <i class="fas fa-calendar-plus w-4 mr-2"></i>Created Date
                </a>
                <a href="#" class="sort-option block px-4 py-2 text-sm text-neutral-700 dark:text-dark-300 hover:bg-neutral-100 dark:hover:bg-dark-700" data-sort="updated">
                    <i class="fas fa-calendar-edit w-4 mr-2"></i>Updated Date
                </a>
                <a href="#" class="sort-option block px-4 py-2 text-sm text-neutral-700 dark:text-dark-300 hover:bg-neutral-100 dark:hover:bg-dark-700" data-sort="title">
                    <i class="fas fa-sort-alpha-down w-4 mr-2"></i>Title
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Requirements Content -->
<div id="requirementsContent">
    <!-- List View -->
    <div id="listViewContent" class="view-content">
        @if (Model != null && Model.Any())
        {
            <div class="grid grid-cols-1 gap-4" id="requirementsList">
                @foreach (var requirement in Model)
                {
                    <div class="requirement-card transition-all duration-200 hover:shadow-lg"
                         data-project="@requirement.ProjectId"
                         data-status="@requirement.Status.ToString().ToLower()"
                         data-priority="@requirement.Priority.ToString().ToLower()"
                         data-type="@requirement.Type.ToString().ToLower()">
                        <div class="card-custom">
                            <div class="card-body-custom">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1 min-w-0">
                                        <!-- Header -->
                                        <div class="flex items-center space-x-3 mb-3">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                                                <EMAIL>("D4")
                                            </span>
                                            @{
                                                var priorityClass = GetPriorityTailwindClass(requirement.Priority);
                                            }
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @priorityClass">
                                                @requirement.Priority
                                            </span>
                                            @{
                                                var statusClass = GetStatusTailwindClass(requirement.Status);
                                            }
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                                                @requirement.Status
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200">
                                                @requirement.Type
                                            </span>
                                        </div>

                                        <!-- Title and Description -->
                                        <div class="mb-4">
                                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">
                                                @requirement.Title
                                            </h3>
                                            @if (!string.IsNullOrEmpty(requirement.Description))
                                            {
                                                <p class="text-sm text-neutral-600 dark:text-dark-400 line-clamp-2">
                                                    @requirement.Description.Substring(0, Math.Min(150, requirement.Description.Length))@(requirement.Description.Length > 150 ? "..." : "")
                                                </p>
                                            }
                                        </div>

                                        <!-- Details -->
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                            <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                                <i class="fas fa-project-diagram w-4 h-4 mr-2 text-neutral-400 dark:text-dark-500"></i>
                                                <span>
                                                    @if (requirement.Project != null)
                                                    {
                                                        @requirement.Project.Name
                                                    }
                                                    else
                                                    {
                                                        <span class="text-neutral-400 dark:text-dark-500">No Project</span>
                                                    }
                                                </span>
                                            </div>
                                            <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                                <i class="fas fa-user w-4 h-4 mr-2 text-neutral-400 dark:text-dark-500"></i>
                                                <span>
                                                    @if (requirement.Developer != null)
                                                    {
                                                        @requirement.Developer.UserName
                                                    }
                                                    else
                                                    {
                                                        <span class="text-neutral-400 dark:text-dark-500">Unassigned</span>
                                                    }
                                                </span>
                                            </div>
                                            <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                                <i class="fas fa-calendar w-4 h-4 mr-2 text-neutral-400 dark:text-dark-500"></i>
                                                <span>@requirement.CreatedAt.ToString("MMM dd, yyyy")</span>
                                            </div>
                                        </div>

                                        <!-- Progress Bar -->
                                        @{
                                            var progressPercentage = requirement.Status == PM.Tool.Core.Entities.RequirementStatus.Completed ? 100 :
                                                                    requirement.Status == PM.Tool.Core.Entities.RequirementStatus.InProgress ? 50 :
                                                                    requirement.Status == PM.Tool.Core.Entities.RequirementStatus.Approved ? 25 : 0;
                                            var progressColor = progressPercentage >= 75 ? "bg-success-500" :
                                                              progressPercentage >= 50 ? "bg-primary-500" :
                                                              progressPercentage >= 25 ? "bg-warning-500" : "bg-neutral-300 dark:bg-dark-600";
                                        }
                                        <div class="mb-4">
                                            <div class="flex items-center justify-between text-xs text-neutral-500 dark:text-dark-400 mb-2">
                                                <span>Progress</span>
                                                <span>@progressPercentage%</span>
                                            </div>
                                            <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                                                <div class="@progressColor h-2 rounded-full transition-all duration-500 ease-out"
                                                     style="width: @progressPercentage%"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex flex-col space-y-1 ml-4">
                                        <a asp-action="Details" asp-route-id="@requirement.Id"
                                           class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-primary-100 dark:hover:bg-primary-900 hover:text-primary-600 dark:hover:text-primary-400 rounded-md transition-colors"
                                           title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@requirement.Id"
                                           class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-warning-100 dark:hover:bg-warning-900 hover:text-warning-600 dark:hover:text-warning-400 rounded-md transition-colors"
                                           title="Edit Requirement">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button onclick="deleteRequirement(@requirement.Id)"
                                                class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-danger-100 dark:hover:bg-danger-900 hover:text-danger-600 dark:hover:text-danger-400 rounded-md transition-colors"
                                                title="Delete Requirement">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-clipboard-list text-2xl text-neutral-400 dark:text-dark-500"></i>
                </div>
                <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Requirements Found</h3>
                <p class="text-neutral-500 dark:text-dark-400 mb-6">Create your first requirement to get started.</p>
                @{
                    ViewData["Text"] = "Create Requirement";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Href"] = Url.Action("Create");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        }
    </div>

    <!-- Hierarchy View -->
    <div id="hierarchyViewContent" class="view-content hidden">
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-sitemap text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Requirements Hierarchy</h3>
                        <p class="text-sm text-neutral-500 dark:text-dark-400">Parent-child relationships</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div id="requirementHierarchy">
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 dark:border-primary-400"></div>
                        <span class="ml-2 text-neutral-600 dark:text-dark-400">Loading hierarchy...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Matrix View -->
    <div id="matrixViewContent" class="view-content hidden">
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-th text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Traceability Matrix</h3>
                        <p class="text-sm text-neutral-500 dark:text-dark-400">Requirements relationships and coverage</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div id="requirementMatrix">
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 dark:border-primary-400"></div>
                        <span class="ml-2 text-neutral-600 dark:text-dark-400">Loading matrix...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let currentView = 'list';
        let sortDropdownOpen = false;

        $(document).ready(function() {
            initializeRequirementsPage();
        });

        function initializeRequirementsPage() {
            loadProjects();
            setupFilters();
            setupViewToggle();
            setupSorting();
            setupDropdowns();
        }

        function loadProjects() {
            $.get('@Url.Action("GetProjects", "Project")')
                .done(function(data) {
                    const select = $('#projectFilter');
                    if (data && Array.isArray(data)) {
                        data.forEach(function(project) {
                            select.append(`<option value="${project.id}">${project.name}</option>`);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function setupFilters() {
            $('#projectFilter, #statusFilter, #priorityFilter, #typeFilter').on('change', filterRequirements);
            $('#searchInput').on('input', debounce(filterRequirements, 300));
            $('#clearFilters').on('click', clearFilters);
        }

        function filterRequirements() {
            const projectFilter = $('#projectFilter').val();
            const statusFilter = $('#statusFilter').val().toLowerCase();
            const priorityFilter = $('#priorityFilter').val().toLowerCase();
            const typeFilter = $('#typeFilter').val().toLowerCase();
            const searchTerm = $('#searchInput').val().toLowerCase();

            $('.requirement-card').each(function() {
                const card = $(this);
                const project = card.data('project');
                const status = card.data('status');
                const priority = card.data('priority');
                const type = card.data('type');
                const title = card.find('h3').text().toLowerCase();
                const description = card.find('p').text().toLowerCase();

                let show = true;

                if (projectFilter && project != projectFilter) show = false;
                if (statusFilter && status !== statusFilter) show = false;
                if (priorityFilter && priority !== priorityFilter) show = false;
                if (typeFilter && type !== typeFilter) show = false;
                if (searchTerm && !title.includes(searchTerm) && !description.includes(searchTerm)) show = false;

                if (show) {
                    card.removeClass('hidden').addClass('block');
                } else {
                    card.removeClass('block').addClass('hidden');
                }
            });

            updateStatistics();
        }

        function clearFilters() {
            $('#projectFilter, #statusFilter, #priorityFilter, #typeFilter').val('');
            $('#searchInput').val('');
            $('.requirement-card').removeClass('hidden').addClass('block');
            updateStatistics();
        }

        function setupViewToggle() {
            $('.view-toggle-btn').on('click', function() {
                const viewType = $(this).attr('id').replace('ViewBtn', '').toLowerCase();
                switchView(viewType);
            });
        }

        function switchView(viewType) {
            // Update button states
            $('.view-toggle-btn').removeClass('active bg-white dark:bg-dark-800 text-primary-600 dark:text-primary-400 shadow-sm')
                                 .addClass('text-neutral-600 dark:text-dark-400 hover:text-neutral-900 dark:hover:text-dark-100');

            $(`#${viewType}ViewBtn`).removeClass('text-neutral-600 dark:text-dark-400 hover:text-neutral-900 dark:hover:text-dark-100')
                                   .addClass('active bg-white dark:bg-dark-800 text-primary-600 dark:text-primary-400 shadow-sm');

            // Hide all views
            $('.view-content').addClass('hidden');

            // Show selected view
            switch(viewType) {
                case 'list':
                    $('#listViewContent').removeClass('hidden');
                    currentView = 'list';
                    break;
                case 'hierarchy':
                    $('#hierarchyViewContent').removeClass('hidden');
                    currentView = 'hierarchy';
                    loadHierarchyView();
                    break;
                case 'matrix':
                    $('#matrixViewContent').removeClass('hidden');
                    currentView = 'matrix';
                    loadMatrixView();
                    break;
            }
        }

        function setupDropdowns() {
            // Sort dropdown
            $('#sortDropdown').on('click', function(e) {
                e.stopPropagation();
                $('#sortMenu').toggleClass('hidden');
                sortDropdownOpen = !sortDropdownOpen;
            });

            // Close dropdown when clicking outside
            $(document).on('click', function() {
                if (sortDropdownOpen) {
                    $('#sortMenu').addClass('hidden');
                    sortDropdownOpen = false;
                }
            });
        }

        function setupSorting() {
            $('.sort-option').on('click', function(e) {
                e.preventDefault();
                const sortBy = $(this).data('sort');
                sortRequirements(sortBy);
                $('#sortMenu').addClass('hidden');
                sortDropdownOpen = false;
            });
        }

        function sortRequirements(sortBy) {
            const container = $('#requirementsList');
            const cards = container.children('.requirement-card').get();

            cards.sort(function(a, b) {
                let aVal, bVal;

                switch(sortBy) {
                    case 'priority':
                        const priorityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
                        aVal = priorityOrder[$(a).data('priority')] || 0;
                        bVal = priorityOrder[$(b).data('priority')] || 0;
                        return bVal - aVal; // Descending
                    case 'status':
                        aVal = $(a).data('status');
                        bVal = $(b).data('status');
                        return aVal.localeCompare(bVal);
                    case 'title':
                        aVal = $(a).find('h3').text().toLowerCase();
                        bVal = $(b).find('h3').text().toLowerCase();
                        return aVal.localeCompare(bVal);
                    case 'created':
                    case 'updated':
                        // For now, sort by ID as proxy for creation order
                        aVal = parseInt($(a).find('span:contains("REQ-")').text().replace('REQ-', ''));
                        bVal = parseInt($(b).find('span:contains("REQ-")').text().replace('REQ-', ''));
                        return sortBy === 'created' ? aVal - bVal : bVal - aVal;
                    default:
                        return 0;
                }
            });

            $.each(cards, function(idx, card) {
                container.append(card);
            });
        }

        function loadHierarchyView() {
            $.get('@Url.Action("GetRequirementHierarchy", "Requirement")')
                .done(function(data) {
                    $('#requirementHierarchy').html(data);
                })
                .fail(function() {
                    $('#requirementHierarchy').html(`
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-danger-100 dark:bg-danger-900 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-exclamation-triangle text-2xl text-danger-600 dark:text-danger-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">Failed to Load Hierarchy</h3>
                            <p class="text-neutral-500 dark:text-dark-400">Please try again later.</p>
                        </div>
                    `);
                });
        }

        function loadMatrixView() {
            $.get('@Url.Action("GetRequirementMatrix", "Requirement")')
                .done(function(data) {
                    $('#requirementMatrix').html(data);
                })
                .fail(function() {
                    $('#requirementMatrix').html(`
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-danger-100 dark:bg-danger-900 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-exclamation-triangle text-2xl text-danger-600 dark:text-danger-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">Failed to Load Matrix</h3>
                            <p class="text-neutral-500 dark:text-dark-400">Please try again later.</p>
                        </div>
                    `);
                });
        }

        function updateStatistics() {
            const visibleCards = $('.requirement-card:not(.hidden)');
            // Update statistics based on visible cards
            // This could be enhanced to update the stats cards dynamically
        }

        function deleteRequirement(id) {
            if (confirm('Are you sure you want to delete this requirement? This action cannot be undone.')) {
                $.post('@Url.Action("Delete", "Requirement")', {
                    id: id,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                })
                .done(function(response) {
                    if (response.success) {
                        // Remove the card from the DOM
                        $(`.requirement-card[data-id="${id}"]`).fadeOut(300, function() {
                            $(this).remove();
                            updateStatistics();
                        });

                        // Show success message
                        showNotification('Requirement deleted successfully', 'success');
                    } else {
                        showNotification(response.message || 'Failed to delete requirement', 'error');
                    }
                })
                .fail(function() {
                    showNotification('Failed to delete requirement. Please try again.', 'error');
                });
            }
        }

        function showNotification(message, type) {
            // Simple notification system - could be enhanced with a proper notification component
            const alertClass = type === 'success' ? 'bg-success-100 border-success-200 text-success-800' :
                              'bg-danger-100 border-danger-200 text-danger-800';

            const notification = $(`
                <div class="fixed top-4 right-4 z-50 ${alertClass} border rounded-lg p-4 shadow-lg">
                    <div class="flex items-center">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
                        <span>${message}</span>
                    </div>
                </div>
            `);

            $('body').append(notification);

            setTimeout(function() {
                notification.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 3000);
        }

        // Utility function for debouncing
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
}

<style>
    .requirement-card {
        transition: all 0.2s ease-in-out;
    }

    .requirement-card:hover {
        transform: translateY(-2px);
    }

    .view-content {
        min-height: 400px;
    }

    .view-toggle-btn.active {
        background-color: white;
        color: #3b82f6;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .dark .view-toggle-btn.active {
        background-color: #1f2937;
        color: #60a5fa;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Custom scrollbar for better dark mode support */
    .overflow-auto::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    .overflow-auto::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 3px;
    }

    .dark .overflow-auto::-webkit-scrollbar-track {
        background: #374151;
    }

    .overflow-auto::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
    }

    .dark .overflow-auto::-webkit-scrollbar-thumb {
        background: #6b7280;
    }

    .overflow-auto::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    .dark .overflow-auto::-webkit-scrollbar-thumb:hover {
        background: #9ca3af;
    }
</style>

@functions {
    string GetPriorityTailwindClass(PM.Tool.Core.Entities.RequirementPriority priority)
    {
        return priority switch
        {
            PM.Tool.Core.Entities.RequirementPriority.Critical => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
            PM.Tool.Core.Entities.RequirementPriority.High => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
            PM.Tool.Core.Entities.RequirementPriority.Medium => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
            PM.Tool.Core.Entities.RequirementPriority.Low => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }

    string GetStatusTailwindClass(PM.Tool.Core.Entities.RequirementStatus status)
    {
        return status switch
        {
            PM.Tool.Core.Entities.RequirementStatus.Draft => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200",
            PM.Tool.Core.Entities.RequirementStatus.UnderReview => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
            PM.Tool.Core.Entities.RequirementStatus.Approved => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
            PM.Tool.Core.Entities.RequirementStatus.InProgress => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
            PM.Tool.Core.Entities.RequirementStatus.Completed => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            PM.Tool.Core.Entities.RequirementStatus.OnHold => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }
}
