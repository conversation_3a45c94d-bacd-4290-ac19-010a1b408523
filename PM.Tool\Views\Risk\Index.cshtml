@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model IEnumerable<PM.Tool.Core.Entities.Risk>
@{
    ViewData["Title"] = "Risk Management";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Risk Management", Href = "", Icon = "fas fa-exclamation-triangle" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Standardized Page Header -->
@{
    ViewData["Title"] = "Risk Management";
    ViewData["Description"] = "Identify, assess, and mitigate project risks to ensure project success";
    ViewData["Icon"] = "fas fa-exclamation-triangle";
    ViewData["IconColor"] = "text-danger-600 dark:text-danger-400";
    ViewData["Actions"] = new object[] {
        new { Text = "Add Risk", Variant = "primary", Icon = "fas fa-plus", Href = Url.Action("Create"), OnClick = (string)null },
        new { Text = "Risk Matrix", Variant = "outline", Icon = "fas fa-th", Href = Url.Action("Matrix"), OnClick = (string)null },
        new { Text = "Analytics", Variant = "secondary", Icon = "fas fa-chart-bar", Href = Url.Action("Analytics"), OnClick = (string)null }
    };

    var statsArray = new List<object>
    {
        new { Label = "Total Risks", Value = Model.Count().ToString(), Icon = "fas fa-exclamation-triangle", Color = "blue" },
        new { Label = "Critical", Value = Model.Count(r => r.RiskLevel == PM.Tool.Core.Entities.RiskLevel.Critical).ToString(), Icon = "fas fa-exclamation-circle", Color = "red" },
        new { Label = "High Risks", Value = Model.Count(r => r.RiskLevel == PM.Tool.Core.Entities.RiskLevel.High).ToString(), Icon = "fas fa-exclamation-triangle", Color = "amber" },
        new { Label = "Mitigated", Value = Model.Count(r => r.Status == PM.Tool.Core.Entities.RiskStatus.Mitigating).ToString(), Icon = "fas fa-shield-alt", Color = "green" }
    };

    ViewData["Stats"] = statsArray.ToArray();
}
<partial name="Components/_PageHeader" view-data="ViewData" />



<!-- Standardized Filter Bar -->
<div class="card-custom mb-8">
    <form id="filterForm" method="get" class="card-body-custom">
        <!-- Single Row Layout -->
        <div class="flex flex-wrap items-center gap-6">
            <!-- Quick Filters Section -->
            <div class="flex items-center gap-3">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-3">Quick:</span>
                <div class="flex items-center gap-2">
                    @{
                        ViewData["Text"] = "Critical";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-exclamation-circle";
                        ViewData["OnClick"] = "filterRisks('critical')";
                        ViewData["AdditionalClasses"] = "quick-filter-btn";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "High";
                        ViewData["Icon"] = "fas fa-exclamation-triangle";
                        ViewData["OnClick"] = "filterRisks('high')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "Active";
                        ViewData["Icon"] = "fas fa-eye";
                        ViewData["OnClick"] = "filterRisks('active')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    @{
                        ViewData["Text"] = "Mitigating";
                        ViewData["Icon"] = "fas fa-shield-alt";
                        ViewData["OnClick"] = "filterRisks('mitigating')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>

            <!-- Main Filters Section -->
            <div class="flex items-center gap-4 flex-1">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text"
                           name="search"
                           placeholder="Search risks..."
                           class="w-64 pl-11 pr-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 transition-all duration-200">
                </div>

                <!-- Filter Controls -->
                <div class="flex items-center gap-3">
                    <!-- Status Filter -->
                    <select name="status" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Status</option>
                        <option value="Identified">Identified</option>
                        <option value="Analyzing">Analyzing</option>
                        <option value="Mitigating">Mitigating</option>
                        <option value="Resolved">Resolved</option>
                    </select>

                    <!-- Risk Level Filter -->
                    <select name="riskLevel" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Levels</option>
                        <option value="Critical">Critical</option>
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                    </select>

                    <!-- Category Filter -->
                    <select name="category" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Categories</option>
                        <option value="Technical">Technical</option>
                        <option value="Business">Business</option>
                        <option value="External">External</option>
                        <option value="Organizational">Organizational</option>
                    </select>

                    <!-- Clear Filters Button -->
                    @{
                        ViewData["Text"] = "Clear";
                        ViewData["Variant"] = "outline";
                        ViewData["Size"] = "sm";
                        ViewData["Icon"] = "fas fa-times";
                        ViewData["OnClick"] = "clearAllFilters()";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Risk List -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" id="riskContainer">
    @if (Model != null && Model.Any())
    {
        @foreach (var risk in Model)
        {
            <div class="risk-card transition-all duration-200 hover:shadow-lg"
                 data-project="@risk.ProjectId"
                 data-status="@risk.Status.ToString().ToLower()"
                 data-severity="@risk.RiskLevel.ToString().ToLower()"
                 data-category="@risk.Category.ToString().ToLower()">
                <div class="card-custom border-l-4 @GetRiskLevelBorderClass(risk.RiskLevel)">
                    <div class="card-header-custom">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                @{
                                    var riskLevelClass = GetRiskLevelTailwindClass(risk.RiskLevel);
                                }
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @riskLevelClass">
                                    @risk.RiskLevel
                                </span>
                                @{
                                    var statusClass = GetStatusTailwindClass(risk.Status);
                                }
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                                    @risk.Status
                                </span>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-neutral-900 dark:text-dark-100">@((int)risk.Probability * (int)risk.Impact)</div>
                                <div class="text-xs text-neutral-500 dark:text-dark-400">Risk Score</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">@risk.Title</h3>
                        <p class="text-sm text-neutral-600 dark:text-dark-400 mb-4 line-clamp-2">@risk.Description</p>

                        <!-- Risk Metrics -->
                        <div class="grid grid-cols-3 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-bold text-neutral-900 dark:text-dark-100">@((int)risk.Probability)</div>
                                <div class="text-xs text-neutral-500 dark:text-dark-400">Probability</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-neutral-900 dark:text-dark-100">@((int)risk.Impact)</div>
                                <div class="text-xs text-neutral-500 dark:text-dark-400">Impact</div>
                            </div>
                            <div class="text-center">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                                    @risk.Category
                                </span>
                            </div>
                        </div>

                        <!-- Risk Details -->
                        <div class="space-y-2 mb-4">
                            @if (risk.Owner != null)
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                    <i class="fas fa-user w-4 h-4 mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    <span>@risk.Owner.UserName</span>
                                </div>
                            }
                            @if (risk.TargetResolutionDate.HasValue)
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                    <i class="fas fa-calendar w-4 h-4 mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    <span class="@(risk.TargetResolutionDate.Value < DateTime.Now ? "text-danger-600 dark:text-danger-400" : "")">
                                        @risk.TargetResolutionDate.Value.ToString("MMM dd, yyyy")
                                    </span>
                                </div>
                            }
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center justify-between">
                            <small class="text-neutral-500 dark:text-dark-400">
                                Updated @(risk.UpdatedAt?.ToString("MMM dd") ?? "N/A")
                            </small>
                            <div class="flex space-x-1">
                                <a asp-action="Details" asp-route-id="@risk.Id"
                                   class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-primary-100 dark:hover:bg-primary-900 hover:text-primary-600 dark:hover:text-primary-400 rounded-md transition-colors"
                                   title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a asp-action="Edit" asp-route-id="@risk.Id"
                                   class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-warning-100 dark:hover:bg-warning-900 hover:text-warning-600 dark:hover:text-warning-400 rounded-md transition-colors"
                                   title="Edit Risk">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @if (risk.Status == PM.Tool.Core.Entities.RiskStatus.Analyzing)
                                {
                                    <button onclick="mitigateRisk(@risk.Id)"
                                            class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-success-100 dark:hover:bg-success-900 hover:text-success-600 dark:hover:text-success-400 rounded-md transition-colors"
                                            title="Start Mitigation">
                                        <i class="fas fa-shield-alt"></i>
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-span-full text-center py-12">
            <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-exclamation-triangle text-2xl text-neutral-400 dark:text-dark-500"></i>
            </div>
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Risks Found</h3>
            <p class="text-neutral-500 dark:text-dark-400 mb-6">Start by identifying and documenting project risks.</p>
            @{
                ViewData["Text"] = "Add Risk";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            loadProjects();
            setupFilters();
        });

        function loadProjects() {
            $.get('@Url.Action("GetProjects", "Projects")')
                .done(function(data) {
                    const select = $('#projectFilter');
                    if (data && Array.isArray(data)) {
                        data.forEach(function(project) {
                            select.append(`<option value="${project.id}">${project.name}</option>`);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function setupFilters() {
            $('#projectFilter, #statusFilter, #severityFilter, #categoryFilter').on('change', filterRisks);
            $('#searchInput').on('input', debounce(filterRisks, 300));
            $('#clearFilters').on('click', clearFilters);
        }

        function filterRisks() {
            const projectFilter = $('#projectFilter').val();
            const statusFilter = $('#statusFilter').val().toLowerCase();
            const severityFilter = $('#severityFilter').val().toLowerCase();
            const categoryFilter = $('#categoryFilter').val().toLowerCase();
            const searchTerm = $('#searchInput').val().toLowerCase();

            $('.risk-card').each(function() {
                const card = $(this);
                const project = card.data('project');
                const status = card.data('status');
                const severity = card.data('severity');
                const category = card.data('category');
                const title = card.find('h3').text().toLowerCase();
                const description = card.find('p').text().toLowerCase();

                let show = true;

                if (projectFilter && project != projectFilter) show = false;
                if (statusFilter && status !== statusFilter) show = false;
                if (severityFilter && severity !== severityFilter) show = false;
                if (categoryFilter && category !== categoryFilter) show = false;
                if (searchTerm && !title.includes(searchTerm) && !description.includes(searchTerm)) show = false;

                if (show) {
                    card.removeClass('hidden').addClass('block');
                } else {
                    card.removeClass('block').addClass('hidden');
                }
            });
        }

        function clearFilters() {
            $('#projectFilter, #statusFilter, #severityFilter, #categoryFilter').val('');
            $('#searchInput').val('');
            $('.risk-card').removeClass('hidden').addClass('block');
        }

        function mitigateRisk(riskId) {
            if (confirm('Start mitigation process for this risk?')) {
                $.post('@Url.Action("Mitigate", "Risk")', {
                    id: riskId,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                })
                .done(function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || 'Failed to update risk status');
                    }
                })
                .fail(function() {
                    alert('Failed to update risk status. Please try again.');
                });
            }
        }

        // Utility function for debouncing
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
}

<style>
    .risk-card {
        transition: all 0.2s ease-in-out;
    }

    .risk-card:hover {
        transform: translateY(-2px);
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>

@functions {
    string GetRiskLevelTailwindClass(PM.Tool.Core.Entities.RiskLevel riskLevel)
    {
        return riskLevel switch
        {
            PM.Tool.Core.Entities.RiskLevel.Critical => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
            PM.Tool.Core.Entities.RiskLevel.High => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
            PM.Tool.Core.Entities.RiskLevel.Medium => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
            PM.Tool.Core.Entities.RiskLevel.Low => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }

    string GetRiskLevelBorderClass(PM.Tool.Core.Entities.RiskLevel riskLevel)
    {
        return riskLevel switch
        {
            PM.Tool.Core.Entities.RiskLevel.Critical => "border-danger-500",
            PM.Tool.Core.Entities.RiskLevel.High => "border-warning-500",
            PM.Tool.Core.Entities.RiskLevel.Medium => "border-info-500",
            PM.Tool.Core.Entities.RiskLevel.Low => "border-success-500",
            _ => "border-neutral-300 dark:border-dark-600"
        };
    }

    string GetStatusTailwindClass(PM.Tool.Core.Entities.RiskStatus status)
    {
        return status switch
        {
            PM.Tool.Core.Entities.RiskStatus.Identified => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200",
            PM.Tool.Core.Entities.RiskStatus.Analyzing => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
            PM.Tool.Core.Entities.RiskStatus.Mitigating => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            PM.Tool.Core.Entities.RiskStatus.Resolved => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }
}
