@model PM.Tool.Core.Entities.Stakeholder
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "Stakeholder Details";
    ViewData["PageTitle"] = Model.Name;
    ViewData["PageDescription"] = $"Detailed information about {Model.Name}";
}



<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 @GetStakeholderTypeIconBg(Model.Type) rounded-xl flex items-center justify-center">
                <i class="@GetStakeholderTypeIcon(Model.Type) text-white text-2xl"></i>
            </div>
            <div>
                <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                    @Model.Name
                </h1>
                <div class="flex items-center space-x-3 mt-1">
                    <span class="text-sm text-neutral-500 dark:text-dark-400">@Model.Title</span>
                    @if (Model.IsActive)
                    {
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200">
                            Active
                        </span>
                    }
                    else
                    {
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200">
                            Inactive
                        </span>
                    }
                </div>
            </div>
        </div>
        <div class="flex space-x-3 mt-4 sm:mt-0">
            @{
                ViewData["Text"] = "Edit Stakeholder";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to List";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
    <!-- Main Information -->
    <div class="xl:col-span-2 space-y-6">
        <!-- Contact Information -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-address-book text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Contact Information</h3>
                        <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Communication details and contact methods</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Email</label>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-envelope text-neutral-400"></i>
                            <a href="mailto:@Model.Email" class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">@Model.Email</a>
                        </div>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.Phone))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Phone</label>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-phone text-neutral-400"></i>
                                <a href="tel:@Model.Phone" class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">@Model.Phone</a>
                            </div>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(Model.Organization))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Organization</label>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-building text-neutral-400"></i>
                                <span class="text-neutral-900 dark:text-dark-100">@Model.Organization</span>
                            </div>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(Model.Department))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Department</label>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-sitemap text-neutral-400"></i>
                                <span class="text-neutral-900 dark:text-dark-100">@Model.Department</span>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Communication Preferences -->
        @if (!string.IsNullOrEmpty(Model.CommunicationPreferences))
        {
            <div class="info-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-comments mr-3 text-primary-600"></i>
                    Communication Preferences
                </h2>
                <p class="text-neutral-700 dark:text-dark-300 leading-relaxed">@Model.CommunicationPreferences</p>
            </div>
        }

        <!-- Notes -->
        @if (!string.IsNullOrEmpty(Model.Notes))
        {
            <div class="info-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-sticky-note mr-3 text-primary-600"></i>
                    Notes
                </h2>
                <p class="text-neutral-700 dark:text-dark-300 leading-relaxed">@Model.Notes</p>
            </div>
        }

        <!-- Project Assignments -->
        <div class="info-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
            <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-4 flex items-center">
                <i class="fas fa-project-diagram mr-3 text-primary-600"></i>
                Project Assignments
            </h2>
            @if (Model.ProjectStakeholders.Any())
            {
                <div class="space-y-3">
                    @foreach (var projectStakeholder in Model.ProjectStakeholders)
                    {
                        <div class="flex items-center justify-between p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-folder text-primary-600"></i>
                                <div>
                                    <div class="font-medium text-neutral-900 dark:text-white">@projectStakeholder.Project.Name</div>
                                    <div class="text-sm text-neutral-600 dark:text-dark-400">
                                        Role: @projectStakeholder.ProjectRole
                                        @if (projectStakeholder.IsKeyStakeholder)
                                        {
                                            <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200">
                                                Key Stakeholder
                                            </span>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="text-sm text-neutral-500 dark:text-dark-500">
                                Assigned: @projectStakeholder.AssignedDate.ToString("MMM dd, yyyy")
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-8">
                    <i class="fas fa-folder-open text-4xl text-neutral-300 dark:text-dark-600 mb-3"></i>
                    <p class="text-neutral-600 dark:text-dark-400">Not assigned to any projects yet.</p>
                </div>
            }
        </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Stakeholder Classification -->
        <div class="info-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Classification</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Type</label>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @GetTypeBadgeClass(Model.Type)">
                        @Model.Type
                    </span>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Role</label>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @GetRoleBadgeClass(Model.Role)">
                        @Model.Role
                    </span>
                </div>
            </div>
        </div>

        <!-- Influence & Interest Matrix -->
        <div class="info-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Influence & Interest</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Influence Level</label>
                    <div class="flex items-center">
                        <div class="flex-1 bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                            <div class="@GetInfluenceBarClass(Model.Influence) h-2 rounded-full" style="width: @GetInfluencePercentage(Model.Influence)%"></div>
                        </div>
                        <span class="ml-3 text-sm font-medium text-neutral-900 dark:text-white">@Model.Influence</span>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Interest Level</label>
                    <div class="flex items-center">
                        <div class="flex-1 bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                            <div class="@GetInterestBarClass(Model.Interest) h-2 rounded-full" style="width: @GetInterestPercentage(Model.Interest)%"></div>
                        </div>
                        <span class="ml-3 text-sm font-medium text-neutral-900 dark:text-white">@Model.Interest</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Priority -->
        <div class="info-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Priority</h3>
            <div class="text-center">
                <div class="inline-flex items-center px-4 py-2 rounded-full text-lg font-semibold @GetPriorityBadgeClass(Model.Priority)">
                    <span class="priority-indicator @GetPriorityIndicatorClass(Model.Priority)"></span>
                    @Model.Priority Priority
                </div>
                <p class="text-sm text-neutral-600 dark:text-dark-400 mt-2">
                    @GetPriorityDescription(Model.Priority)
                </p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="info-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Quick Actions</h3>
            <div class="space-y-3">
                <button class="w-full btn-outline text-left" onclick="sendEmail('@Model.Email')">
                    <i class="fas fa-envelope mr-2"></i>
                    Send Email
                </button>
                @if (!string.IsNullOrEmpty(Model.Phone))
                {
                    <button class="w-full btn-outline text-left" onclick="makeCall('@Model.Phone')">
                        <i class="fas fa-phone mr-2"></i>
                        Call
                    </button>
                }
                <button class="w-full btn-outline text-left" onclick="scheduleeMeeting(@Model.Id)">
                    <i class="fas fa-calendar-plus mr-2"></i>
                    Schedule Meeting
                </button>
                @if (Model.IsActive)
                {
                    <button class="w-full btn-outline-danger text-left" onclick="deactivateStakeholder(@Model.Id)">
                        <i class="fas fa-user-slash mr-2"></i>
                        Deactivate
                    </button>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function sendEmail(email) {
            window.location.href = `mailto:${email}`;
        }

        function makeCall(phone) {
            window.location.href = `tel:${phone}`;
        }

        function scheduleeMeeting(stakeholderId) {
            // Redirect to meeting creation with stakeholder pre-selected
            window.location.href = '@Url.Action("Create", "Meeting")?stakeholderId=' + stakeholderId;
        }

        function deactivateStakeholder(stakeholderId) {
            if (confirm('Are you sure you want to deactivate this stakeholder?')) {
                $.post('@Url.Action("Deactivate")', { id: stakeholderId })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.message);
                        }
                    })
                    .fail(function() {
                        alert('Error deactivating stakeholder.');
                    });
            }
        }
    </script>
}

@functions {
    private string GetStakeholderTypeIcon(StakeholderType type)
    {
        return type switch
        {
            StakeholderType.Internal => "fas fa-user-tie",
            StakeholderType.External => "fas fa-user",
            StakeholderType.Customer => "fas fa-handshake",
            StakeholderType.Vendor => "fas fa-truck",
            StakeholderType.Regulatory => "fas fa-gavel",
            StakeholderType.Partner => "fas fa-users",
            _ => "fas fa-user"
        };
    }

    private string GetStakeholderTypeIconBg(StakeholderType type)
    {
        return type switch
        {
            StakeholderType.Internal => "bg-blue-500",
            StakeholderType.External => "bg-green-500",
            StakeholderType.Customer => "bg-purple-500",
            StakeholderType.Vendor => "bg-orange-500",
            StakeholderType.Regulatory => "bg-red-500",
            StakeholderType.Partner => "bg-indigo-500",
            _ => "bg-neutral-500"
        };
    }

    private string GetTypeBadgeClass(StakeholderType type)
    {
        return type switch
        {
            StakeholderType.Internal => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            StakeholderType.External => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            StakeholderType.Customer => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
            StakeholderType.Vendor => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            StakeholderType.Regulatory => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            StakeholderType.Partner => "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }

    private string GetRoleBadgeClass(StakeholderRole role)
    {
        return role switch
        {
            StakeholderRole.Sponsor => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
            StakeholderRole.Owner => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            StakeholderRole.User => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            StakeholderRole.Approver => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            StakeholderRole.Reviewer => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            StakeholderRole.Consultant => "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
            StakeholderRole.Observer => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }

    private string GetInfluenceBarClass(InfluenceLevel influence)
    {
        return influence switch
        {
            InfluenceLevel.VeryLow => "bg-neutral-400",
            InfluenceLevel.Low => "bg-yellow-400",
            InfluenceLevel.Medium => "bg-blue-400",
            InfluenceLevel.High => "bg-orange-400",
            InfluenceLevel.VeryHigh => "bg-red-400",
            _ => "bg-neutral-400"
        };
    }

    private string GetInterestBarClass(InterestLevel interest)
    {
        return interest switch
        {
            InterestLevel.VeryLow => "bg-neutral-400",
            InterestLevel.Low => "bg-yellow-400",
            InterestLevel.Medium => "bg-blue-400",
            InterestLevel.High => "bg-green-400",
            InterestLevel.VeryHigh => "bg-purple-400",
            _ => "bg-neutral-400"
        };
    }

    private int GetInfluencePercentage(InfluenceLevel influence)
    {
        return influence switch
        {
            InfluenceLevel.VeryLow => 20,
            InfluenceLevel.Low => 40,
            InfluenceLevel.Medium => 60,
            InfluenceLevel.High => 80,
            InfluenceLevel.VeryHigh => 100,
            _ => 0
        };
    }

    private int GetInterestPercentage(InterestLevel interest)
    {
        return interest switch
        {
            InterestLevel.VeryLow => 20,
            InterestLevel.Low => 40,
            InterestLevel.Medium => 60,
            InterestLevel.High => 80,
            InterestLevel.VeryHigh => 100,
            _ => 0
        };
    }

    private string GetPriorityBadgeClass(StakeholderPriority priority)
    {
        return priority switch
        {
            StakeholderPriority.Low => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200",
            StakeholderPriority.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            StakeholderPriority.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            StakeholderPriority.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }

    private string GetPriorityIndicatorClass(StakeholderPriority priority)
    {
        return priority switch
        {
            StakeholderPriority.Low => "bg-neutral-400",
            StakeholderPriority.Medium => "bg-yellow-400",
            StakeholderPriority.High => "bg-orange-400",
            StakeholderPriority.Critical => "bg-red-400",
            _ => "bg-neutral-400"
        };
    }

    private string GetPriorityDescription(StakeholderPriority priority)
    {
        return priority switch
        {
            StakeholderPriority.Low => "Monitor - Low influence and interest",
            StakeholderPriority.Medium => "Keep Informed - Moderate engagement needed",
            StakeholderPriority.High => "Keep Satisfied - High influence, manage carefully",
            StakeholderPriority.Critical => "Manage Closely - High influence and interest",
            _ => "Priority not determined"
        };
    }
}
