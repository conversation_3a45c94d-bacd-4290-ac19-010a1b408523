@model PM.Tool.Models.ViewModels.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
    ViewData["Description"] = "Welcome back! Here's what's happening with your projects.";
    ViewData["Icon"] = "fas fa-tachometer-alt";
    ViewData["IconColor"] = "text-primary-600 dark:text-primary-400";
    
    ViewData["Actions"] = new object[] {
        new { Text = "New Project", Variant = "primary", Icon = "fas fa-plus", Href = Url.Action("Create", "Projects") },
        new { Text = "New Task", Variant = "outline", Icon = "fas fa-tasks", Href = Url.Action("Create", "Tasks") },
        new { Text = "Analytics", Variant = "secondary", Icon = "fas fa-chart-line", Href = Url.Action("Index", "Analytics") }
    };

    var statsArray = new List<object>
    {
        new { Label = "Total Projects", Value = Model.Stats.TotalProjects.ToString(), Icon = "fas fa-project-diagram", Color = "blue" },
        new { Label = "Active Projects", Value = Model.Stats.ActiveProjects.ToString(), Icon = "fas fa-play", Color = "green" },
        new { Label = "Total Tasks", Value = Model.Stats.TotalTasks.ToString(), Icon = "fas fa-tasks", Color = "indigo" },
        new { Label = "Overdue Tasks", Value = Model.Stats.OverdueTasks.ToString(), Icon = "fas fa-exclamation-triangle", Color = "red" }
    };

    ViewData["Stats"] = statsArray.ToArray();
}
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Main Content Grid -->
<div class="grid grid-cols-1 xl:grid-cols-2 gap-8 mb-8">
    <!-- Recent Projects -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-folder-open text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Recent Projects</h3>
                        <p class="text-sm text-neutral-500 dark:text-neutral-400">Latest project updates</p>
                    </div>
                </div>
                @{
                    ViewData["Text"] = "View All";
                    ViewData["Variant"] = "outline";
                    ViewData["Size"] = "sm";
                    ViewData["Href"] = Url.Action("Index", "Projects");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
        <div class="card-body-custom">
            @if (Model.RecentProjects.Any())
            {
                <div class="space-y-4">
                    @foreach (var project in Model.RecentProjects.Take(5))
                    {
                        <div class="flex items-center justify-between p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-600 transition-colors">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-project-diagram text-primary-600 dark:text-primary-400"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-neutral-900 dark:text-white">@project.Name</h4>
                                    <p class="text-sm text-neutral-500 dark:text-neutral-400">@project.Status</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-neutral-900 dark:text-white">@project.ProgressPercentage.ToString("F0")%</div>
                                <div class="text-xs text-neutral-500 dark:text-neutral-400">@project.Status</div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-folder-open text-neutral-400 dark:text-dark-500 text-xl"></i>
                    </div>
                    <h4 class="text-lg font-medium text-neutral-900 dark:text-white mb-2">No Projects Yet</h4>
                    <p class="text-neutral-500 dark:text-neutral-400 mb-4">Get started by creating your first project</p>
                    @{
                        ViewData["Text"] = "Create Project";
                        ViewData["Variant"] = "primary";
                        ViewData["Icon"] = "fas fa-plus";
                        ViewData["Href"] = Url.Action("Create", "Projects");
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            }
        </div>
    </div>

    <!-- My Tasks -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-info-100 dark:bg-info-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tasks text-info-600 dark:text-info-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">My Tasks</h3>
                        <p class="text-sm text-neutral-500 dark:text-neutral-400">Your assigned tasks</p>
                    </div>
                </div>
                @{
                    ViewData["Text"] = "View All";
                    ViewData["Variant"] = "outline";
                    ViewData["Size"] = "sm";
                    ViewData["Href"] = Url.Action("Index", "Tasks");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
        <div class="card-body-custom">
            @if (Model.MyTasks.Any())
            {
                <div class="space-y-3">
                    @foreach (var task in Model.MyTasks.Take(5))
                    {
                        <div class="flex items-center justify-between p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 rounded-full @(task.Status == PM.Tool.Core.Enums.TaskStatus.Done ? "bg-success-500" : task.Status == PM.Tool.Core.Enums.TaskStatus.InProgress ? "bg-info-500" : "bg-neutral-400")"></div>
                                <div>
                                    <h4 class="font-medium text-neutral-900 dark:text-white text-sm">@task.Title</h4>
                                    <p class="text-xs text-neutral-500 dark:text-neutral-400">@task.ProjectName</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-neutral-500 dark:text-neutral-400">
                                    @if (task.DueDate.HasValue)
                                    {
                                        @task.DueDate.Value.ToString("MMM dd")
                                    }
                                    else
                                    {
                                        <span>No due date</span>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-tasks text-neutral-400 dark:text-dark-500 text-xl"></i>
                    </div>
                    <h4 class="text-lg font-medium text-neutral-900 dark:text-white mb-2">No Tasks Assigned</h4>
                    <p class="text-neutral-500 dark:text-neutral-400">Tasks will appear here when assigned to you</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Alert Tasks Section -->
@if (Model.OverdueTasks.Any() || Model.UpcomingTasks.Any())
{
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-8 mb-8">
        <!-- Overdue Tasks -->
        @if (Model.OverdueTasks.Any())
        {
            <div class="card-custom border-l-4 border-danger-500">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-danger-100 dark:bg-danger-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-danger-700 dark:text-danger-400">Overdue Tasks</h3>
                            <p class="text-sm text-danger-600 dark:text-danger-500">Tasks that need immediate attention</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-3">
                        @foreach (var task in Model.OverdueTasks.Take(5))
                        {
                            <div class="flex items-center justify-between p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg">
                                <div>
                                    <h4 class="font-medium text-danger-900 dark:text-danger-100 text-sm">@task.Title</h4>
                                    <p class="text-xs text-danger-600 dark:text-danger-400">@task.ProjectName</p>
                                </div>
                                <div class="text-xs text-danger-600 dark:text-danger-400">
                                    @if (task.DueDate.HasValue)
                                    {
                                        var daysOverdue = (DateTime.UtcNow - task.DueDate.Value).Days;
                                        <text>@daysOverdue days overdue</text>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }

        <!-- Upcoming Tasks -->
        @if (Model.UpcomingTasks.Any())
        {
            <div class="card-custom border-l-4 border-warning-500">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-warning-600 dark:text-warning-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-warning-700 dark:text-warning-400">Upcoming Tasks</h3>
                            <p class="text-sm text-warning-600 dark:text-warning-500">Tasks due soon</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-3">
                        @foreach (var task in Model.UpcomingTasks.Take(5))
                        {
                            <div class="flex items-center justify-between p-3 bg-warning-50 dark:bg-warning-900/20 rounded-lg">
                                <div>
                                    <h4 class="font-medium text-warning-900 dark:text-warning-100 text-sm">@task.Title</h4>
                                    <p class="text-xs text-warning-600 dark:text-warning-400">@task.ProjectName</p>
                                </div>
                                <div class="text-xs text-warning-600 dark:text-warning-400">
                                    @if (task.DueDate.HasValue)
                                    {
                                        @task.DueDate.Value.ToString("MMM dd")
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
}

<!-- Charts and Analytics Section -->
@if (Model.Charts != null)
{
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-8 mb-8">
        <!-- Project Progress Chart -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-pie text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Project Progress</h3>
                        <p class="text-sm text-neutral-500 dark:text-neutral-400">Overall project completion status</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="h-64">
                    <canvas id="projectProgressChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Task Status Chart -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-info-100 dark:bg-info-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-info-600 dark:text-info-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Task Distribution</h3>
                        <p class="text-sm text-neutral-500 dark:text-neutral-400">Task status breakdown</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="h-64">
                    <canvas id="taskStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
}
