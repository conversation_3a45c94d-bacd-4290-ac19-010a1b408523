@model PM.Tool.Models.ViewModels.EpicCreateViewModel
@{
    ViewData["Title"] = "Create Epic";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;

    // Standardized Page Header
    ViewData["PageTitle"] = "Create New Epic";
    ViewData["PageSubtitle"] = $"Create a new epic for {project?.Name}";
    ViewData["PageIcon"] = "fas fa-layer-group";
    ViewData["Breadcrumbs"] = new List<object> {
        new { Text = "Projects", Icon = "fas fa-project-diagram", Href = Url.Action("Index", "Projects") },
        new { Text = project?.Name, Href = Url.Action("Details", "Projects", new { id = project?.Id }) },
        new { Text = "Epics", Href = Url.Action("Epics", new { projectId = project?.Id }) },
        new { Text = "Create Epic", IsActive = true }
    };
}

<!-- Standardized Page Header -->
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Form -->
<div class="max-w-4xl">
    <form asp-action="CreateEpic" method="post" class="space-y-8">
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.ProjectId)

        <!-- Basic Information -->
        @{
            ViewData["Title"] = "Basic Information";
            ViewData["Icon"] = "fas fa-info-circle";
            ViewData["GridCols"] = "grid-cols-1 md:grid-cols-2";
            ViewData["Fields"] = new List<object> {
                new {
                    Name = "Title",
                    Label = "Epic Title",
                    Placeholder = "Enter a descriptive title for the epic",
                    Required = true,
                    ColSpan = "md:col-span-2"
                },
                new {
                    Name = "Priority",
                    Label = "Priority",
                    Type = "select",
                    Options = ViewBag.EpicPriorities
                },
                new {
                    Name = "TargetDate",
                    Label = "Target Date",
                    Type = "date",
                    Attributes = new Dictionary<string, object> { { "min", DateTime.Today.ToString("yyyy-MM-dd") } }
                },
                new {
                    Name = "EstimatedStoryPoints",
                    Label = "Estimated Story Points",
                    Type = "number",
                    Placeholder = "0",
                    Attributes = new Dictionary<string, object> { { "step", "0.5" }, { "min", "0" } }
                }
            };
        }
        <partial name="Components/_FormSection" view-data="ViewData" />

        <!-- Description -->
        @{
            ViewData["Title"] = "Description";
            ViewData["Icon"] = "fas fa-align-left";
            ViewData["GridCols"] = "grid-cols-1";
            ViewData["Fields"] = new List<object> {
                new {
                    Name = "Description",
                    Label = "Epic Description",
                    Type = "textarea",
                    Rows = 6,
                    Placeholder = "Provide a detailed description of what this epic aims to achieve...",
                    Required = true,
                    Help = "Describe the epic's purpose, scope, and expected outcomes."
                }
            };
        }
        <partial name="Components/_FormSection" view-data="ViewData" />

        <!-- Acceptance Criteria -->
        @{
            ViewData["Title"] = "Acceptance Criteria";
            ViewData["Icon"] = "fas fa-check-circle";
            ViewData["GridCols"] = "grid-cols-1";
            ViewData["Fields"] = new List<object> {
                new {
                    Name = "AcceptanceCriteria",
                    Label = "Acceptance Criteria",
                    Type = "textarea",
                    Rows = 4,
                    Placeholder = "Define the criteria that must be met for this epic to be considered complete...",
                    Help = "List the specific conditions that must be satisfied for the epic to be marked as done."
                }
            };
        }
        <partial name="Components/_FormSection" view-data="ViewData" />

        <!-- Business Value -->
        @{
            ViewData["Title"] = "Business Value";
            ViewData["Icon"] = "fas fa-chart-line";
            ViewData["GridCols"] = "grid-cols-1";
            ViewData["Fields"] = new List<object> {
                new {
                    Name = "BusinessValue",
                    Label = "Business Value",
                    Type = "textarea",
                    Rows = 4,
                    Placeholder = "Explain the business value and benefits this epic will deliver...",
                    Help = "Describe how this epic contributes to business goals and what value it provides to users or stakeholders."
                }
            };
        }
        <partial name="Components/_FormSection" view-data="ViewData" />

        <!-- Additional Information -->
        @{
            ViewData["Title"] = "Additional Information";
            ViewData["Icon"] = "fas fa-tags";
            ViewData["GridCols"] = "grid-cols-1 md:grid-cols-2";
            ViewData["Fields"] = new List<object> {
                new {
                    Name = "Tags",
                    Label = "Tags",
                    Placeholder = "feature, enhancement, bug-fix",
                    Help = "Separate tags with commas"
                },
                new {
                    Name = "SortOrder",
                    Label = "Sort Order",
                    Type = "number",
                    Placeholder = "0",
                    Attributes = new Dictionary<string, object> { { "min", "0" } },
                    Help = "Lower numbers appear first"
                }
            };
        }
        <partial name="Components/_FormSection" view-data="ViewData" />

        <!-- Form Actions -->
        @{
            ViewData["SubmitText"] = "Create Epic";
            ViewData["SubmitIcon"] = "fas fa-save";
            ViewData["CancelUrl"] = Url.Action("Epics", new { projectId = Model.ProjectId });
        }
        <partial name="Components/_FormActions" view-data="ViewData" />
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set minimum date to today
            const dateInputs = document.querySelectorAll('input[type="date"]');
            const today = new Date().toISOString().split('T')[0];
            dateInputs.forEach(input => {
                input.setAttribute('min', today);
            });
        });
    </script>
}
