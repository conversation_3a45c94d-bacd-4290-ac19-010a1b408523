@model MyTasksViewModel
@using PM.Tool.Core.Enums

<script src="~/lib/fullcalendar/main.min.js"></script>
<script>
    $(document).ready(function () {
        // Initialize view toggle
        initializeViewToggle();

        // Initialize bulk actions
        initializeBulkActions();

        // Initialize quick filters
        initializeQuickFilters();

        // Initialize compact header
        initializeCompactHeader();

        // Initialize calendar with delay to ensure FullCalendar is loaded
        setTimeout(() => {
            initializeCalendar();
        }, 300);

        // Initialize task checkboxes
        initializeTaskCheckboxes();

        // Initialize table functionality
        initializeTableFeatures();

        // Initialize advanced filters toggle
        initializeAdvancedFilters();
    });

    function initializeViewToggle() {
        $('.view-toggle-btn').on('click', function() {
            const view = $(this).data('view');

            // Update button states
            $('.view-toggle-btn').removeClass('active bg-primary-600 text-white').addClass('text-neutral-600 dark:text-neutral-400');
            $(this).addClass('active bg-primary-600 text-white').removeClass('text-neutral-600 dark:text-neutral-400');

            // Show/hide views
            $('.view-content').addClass('hidden');
            $(`#${view}View`).removeClass('hidden');

            // Initialize or re-render calendar if switching to calendar view
            if (view === 'calendar') {
                setTimeout(() => {
                    if (window.taskCalendar && typeof window.taskCalendar.render === 'function') {
                        console.log('Re-rendering existing calendar');
                        window.taskCalendar.render();
                    } else {
                        console.log('Initializing new calendar');
                        // Initialize calendar if not already done
                        initializeCalendar();
                    }
                }, 200);
            }
        });

        // Set initial view state
        const activeBtn = $('.view-toggle-btn.active');
        if (activeBtn.length > 0) {
            const initialView = activeBtn.data('view');
            $('.view-content').addClass('hidden');
            $(`#${initialView}View`).removeClass('hidden');
        }
    }

    function initializeBulkActions() {
        $('#bulkAction, #applyBulkAction').prop('disabled', true);

        $('#applyBulkAction').on('click', function() {
            const action = $('#bulkAction').val();
            const selectedTasks = $('.task-checkbox:checked').map(function() {
                return $(this).val();
            }).get();

            if (selectedTasks.length === 0) {
                alert('Please select at least one task.');
                return;
            }

            if (!action) {
                alert('Please select an action.');
                return;
            }

            // Handle different bulk actions
            switch(action) {
                case 'status':
                    showBulkStatusModal(selectedTasks);
                    break;
                case 'priority':
                    showBulkPriorityModal(selectedTasks);
                    break;
                case 'assign':
                    showBulkAssignModal(selectedTasks);
                    break;
                case 'delete':
                    if (confirm(`Are you sure you want to delete ${selectedTasks.length} task(s)?`)) {
                        bulkDeleteTasks(selectedTasks);
                    }
                    break;
            }
        });
    }

    function initializeQuickFilters() {
        $('.quick-filter-btn').on('click', function() {
            const filter = $(this).data('filter');
            const currentUrl = new URL(window.location);

            // Clear existing filters
            currentUrl.searchParams.delete('status');
            currentUrl.searchParams.delete('priority');
            currentUrl.searchParams.delete('isOverdue');
            currentUrl.searchParams.delete('dueDateFrom');
            currentUrl.searchParams.delete('dueDateTo');

            // Remove active class from all quick filter buttons
            $('.quick-filter-btn').removeClass('active');
            // Add active class to clicked button
            $(this).addClass('active');

            // Apply quick filter
            switch(filter) {
                case 'today':
                    const today = new Date().toISOString().split('T')[0];
                    currentUrl.searchParams.set('dueDateFrom', today);
                    currentUrl.searchParams.set('dueDateTo', today);
                    break;
                case 'thisweek':
                    const startOfWeek = new Date();
                    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
                    const endOfWeek = new Date(startOfWeek);
                    endOfWeek.setDate(endOfWeek.getDate() + 6);
                    currentUrl.searchParams.set('dueDateFrom', startOfWeek.toISOString().split('T')[0]);
                    currentUrl.searchParams.set('dueDateTo', endOfWeek.toISOString().split('T')[0]);
                    break;
                case 'overdue':
                    currentUrl.searchParams.set('isOverdue', 'true');
                    break;
                case 'highpriority':
                    currentUrl.searchParams.set('priority', 'High');
                    break;
                case 'assigned':
                    currentUrl.searchParams.set('assignedToMe', 'true');
                    break;
            }

            window.location.href = currentUrl.toString();
        });
    }

    function initializeCalendar() {
        const calendarEl = document.getElementById('taskCalendar');
        if (!calendarEl) {
            console.warn('Calendar element not found');
            return;
        }

        // Check if FullCalendar is loaded
        if (typeof FullCalendar === 'undefined') {
            console.warn('FullCalendar library not loaded, retrying in 500ms...');
            setTimeout(initializeCalendar, 500);
            return;
        }

        try {
            // Prepare events data safely
            const events = [];
            @if (Model?.Tasks != null)
            {
                @foreach (var task in Model.Tasks.Where(t => t.DueDate.HasValue))
                {
                    <text>
                    events.push({
                        id: '@task.Id',
                        title: '@Html.Raw(Html.Encode(task.Title))',
                        start: '@(task.DueDate?.ToString("yyyy-MM-dd") ?? DateTime.Now.ToString("yyyy-MM-dd"))',
                        url: '@Url.Action("Details", new { id = task.Id })',
                        backgroundColor: '@(task.Status.ToString().ToLower() switch {
                            "done" => "#10b981",
                            "inprogress" => "#3b82f6",
                            "inreview" => "#f59e0b",
                            _ => "#6b7280"
                        })',
                        borderColor: '@(task.Status.ToString().ToLower() switch {
                            "done" => "#059669",
                            "inprogress" => "#2563eb",
                            "inreview" => "#d97706",
                            _ => "#4b5563"
                        })',
                        extendedProps: {
                            project: '@Html.Raw(Html.Encode(task.ProjectName))',
                            status: '@task.Status.ToString()',
                            priority: '@task.Priority.ToString()',
                            isOverdue: @task.IsOverdue.ToString().ToLower()
                        }
                    });
                    </text>
                }
            }

            console.log('Initializing calendar with', events.length, 'events');

            // Initialize calendar
            window.taskCalendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,listWeek'
                },
                height: 'auto',
                events: events,
                eventClick: function(info) {
                    info.jsEvent.preventDefault();
                    if (info.event.url) {
                        window.location.href = info.event.url;
                    }
                },
                eventDidMount: function(info) {
                    // Add tooltip
                    const props = info.event.extendedProps;
                    if (props) {
                        info.el.setAttribute('title',
                            `${info.event.title}\nProject: ${props.project}\nStatus: ${props.status}\nPriority: ${props.priority}${props.isOverdue ? '\n⚠️ OVERDUE' : ''}`
                        );
                    }

                    // Add overdue styling
                    if (info.event.extendedProps.isOverdue) {
                        info.el.style.border = '2px solid #dc2626';
                        info.el.style.fontWeight = 'bold';
                    }
                },
                loading: function(isLoading) {
                    if (isLoading) {
                        console.log('Calendar is loading...');
                    } else {
                        console.log('Calendar loaded successfully');
                    }
                }
            });

            // Render the calendar
            window.taskCalendar.render();
            console.log('Calendar render completed');

        } catch (error) {
            console.error('Error initializing calendar:', error);
            // Show a user-friendly message
            calendarEl.innerHTML = `
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-600 dark:text-red-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">Calendar Error</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400 mb-4">Unable to load calendar view. Please try refreshing the page.</p>
                    <button onclick="location.reload()" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors">
                        <i class="fas fa-refresh mr-2"></i>Refresh Page
                    </button>
                </div>
            `;
        }
    }

    function initializeTaskCheckboxes() {
        $('.task-checkbox').on('change', function() {
            const checkedCount = $('.task-checkbox:checked').length;
            $('#bulkAction, #applyBulkAction').prop('disabled', checkedCount === 0);
        });
    }

    function updateTaskStatus(taskId, newStatus) {
        $.ajax({
            url: '@Url.Action("UpdateTaskStatus", "Api")',
            method: 'POST',
            data: {
                taskId: taskId,
                status: newStatus
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error updating task status: ' + response.message);
                }
            },
            error: function() {
                alert('Error updating task status. Please try again.');
            }
        });
    }

    function showExportModal() {
        // Implementation for export modal
        alert('Export functionality will be implemented here.');
    }

    function toggleCalendarView() {
        $('.view-toggle-btn[data-view="calendar"]').click();
    }

    // Bulk action modal functions
    function showBulkStatusModal(taskIds) {
        // Implementation for bulk status change modal
        const newStatus = prompt('Enter new status (ToDo, InProgress, Done, Cancelled):');
        if (newStatus) {
            bulkUpdateStatus(taskIds, newStatus);
        }
    }

    function showBulkPriorityModal(taskIds) {
        // Implementation for bulk priority change modal
        const newPriority = prompt('Enter new priority (Low, Medium, High, Critical):');
        if (newPriority) {
            bulkUpdatePriority(taskIds, newPriority);
        }
    }

    function showBulkAssignModal(taskIds) {
        // Implementation for bulk assign modal
        alert('Bulk assign functionality will be implemented here.');
    }

    function bulkUpdateStatus(taskIds, status) {
        $.ajax({
            url: '@Url.Action("BulkUpdateStatus", "Api")',
            method: 'POST',
            data: {
                taskIds: taskIds,
                status: status
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error updating tasks: ' + response.message);
                }
            },
            error: function() {
                alert('Error updating tasks. Please try again.');
            }
        });
    }

    function bulkUpdatePriority(taskIds, priority) {
        $.ajax({
            url: '@Url.Action("BulkUpdatePriority", "Api")',
            method: 'POST',
            data: {
                taskIds: taskIds,
                priority: priority
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error updating tasks: ' + response.message);
                }
            },
            error: function() {
                alert('Error updating tasks. Please try again.');
            }
        });
    }

    function bulkDeleteTasks(taskIds) {
        $.ajax({
            url: '@Url.Action("BulkDelete", "Api")',
            method: 'POST',
            data: {
                taskIds: taskIds
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error deleting tasks: ' + response.message);
                }
            },
            error: function() {
                alert('Error deleting tasks. Please try again.');
            }
        });
    }

    // Table Features
    function initializeTableFeatures() {
        // Select All functionality
        $('#selectAll').on('change', function() {
            const isChecked = $(this).is(':checked');
            $('.task-checkbox').prop('checked', isChecked);
            updateBulkActionsToolbar();
        });

        // Individual checkbox functionality
        $(document).on('change', '.task-checkbox', function() {
            updateBulkActionsToolbar();

            // Update select all checkbox
            const totalCheckboxes = $('.task-checkbox').length;
            const checkedCheckboxes = $('.task-checkbox:checked').length;

            $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
            $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes);
        });

        // Bulk action buttons
        $('.bulk-action-btn').on('click', function() {
            const action = $(this).data('action');
            const selectedTasks = $('.task-checkbox:checked').map(function() {
                return $(this).val();
            }).get();

            if (selectedTasks.length === 0) {
                alert('Please select tasks first.');
                return;
            }

            performBulkAction(action, selectedTasks);
        });

        // Clear selection
        $('#clearSelection').on('click', function() {
            $('.task-checkbox, #selectAll').prop('checked', false);
            updateBulkActionsToolbar();
        });

        // Task actions dropdown
        $(document).on('click', function(e) {
            if (!$(e.target).closest('[onclick*="toggleTaskActions"]').length) {
                $('[id^="taskActions-"]').addClass('hidden');
            }
        });
    }

    function updateBulkActionsToolbar() {
        const checkedCount = $('.task-checkbox:checked').length;
        const toolbar = $('#bulkActionsToolbar');
        const compactBulkActions = $('#compactBulkActions');
        const countSpan = $('#selectedCount');

        if (checkedCount > 0) {
            // Show both toolbars
            toolbar.removeClass('hidden');
            compactBulkActions.removeClass('hidden');
            countSpan.text(`${checkedCount} selected`);

            // Enable bulk action controls
            $('#bulkAction, #applyBulkAction').prop('disabled', false);
        } else {
            // Hide both toolbars
            toolbar.addClass('hidden');
            compactBulkActions.addClass('hidden');

            // Disable bulk action controls
            $('#bulkAction, #applyBulkAction').prop('disabled', true);
        }
    }

    function performBulkAction(action, taskIds) {
        let actionText = '';
        let endpoint = '';

        switch (action) {
            case 'start':
                actionText = 'start';
                endpoint = '@Url.Action("BulkUpdateStatus", "Tasks")';
                break;
            case 'complete':
                actionText = 'complete';
                endpoint = '@Url.Action("BulkUpdateStatus", "Tasks")';
                break;
            case 'assign':
                actionText = 'assign';
                // This would open an assignment modal
                showAssignmentModal(taskIds);
                return;
            default:
                return;
        }

        if (confirm(`Are you sure you want to ${actionText} ${taskIds.length} task(s)?`)) {
            $.ajax({
                url: endpoint,
                method: 'POST',
                data: {
                    taskIds: taskIds,
                    status: action === 'start' ? 'InProgress' : 'Done'
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function() {
                    alert('Error performing bulk action. Please try again.');
                }
            });
        }
    }

    function toggleTaskActions(taskId) {
        const dropdown = $(`#taskActions-${taskId}`);
        const isVisible = !dropdown.hasClass('hidden');

        // Hide all dropdowns
        $('[id^="taskActions-"]').addClass('hidden');

        // Show this dropdown if it was hidden
        if (!isVisible) {
            dropdown.removeClass('hidden');
        }
    }

    function sortTable(column) {
        const currentSort = new URLSearchParams(window.location.search).get('sortBy');
        const currentOrder = new URLSearchParams(window.location.search).get('sortOrder') || 'asc';

        let newOrder = 'asc';
        if (currentSort === column && currentOrder === 'asc') {
            newOrder = 'desc';
        }

        const url = new URL(window.location);
        url.searchParams.set('sortBy', column);
        url.searchParams.set('sortOrder', newOrder);

        window.location.href = url.toString();
    }

    // Advanced Filters Toggle
    function initializeAdvancedFilters() {
        $('#toggleAdvanced').on('click', function() {
            const advancedFilters = $('#advancedFilters');
            const icon = $(this).find('i');
            const text = $(this).find('span');

            if (advancedFilters.hasClass('hidden')) {
                advancedFilters.removeClass('hidden');
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                text.text('Fewer Filters');
            } else {
                advancedFilters.addClass('hidden');
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                text.text('More Filters');
            }
        });
    }

    function showAssignmentModal(taskIds) {
        // This would show a modal for bulk assignment
        // For now, just show an alert
        alert(`Assignment modal for ${taskIds.length} task(s) would open here.`);
    }

    // Compact Header Functions
    function initializeCompactHeader() {
        // Export modal functionality
        window.showExportModal = function() {
            alert('Export functionality would be implemented here.');
        };
    }

    // Make functions globally available
    window.toggleTaskActions = toggleTaskActions;
    window.sortTable = sortTable;

    // Backup calendar initialization on window load
    window.addEventListener('load', function() {
        setTimeout(() => {
            if (!window.taskCalendar && document.getElementById('taskCalendar')) {
                console.log('Backup calendar initialization triggered');
                initializeCalendar();
            }
        }, 500);
    });
</script>
