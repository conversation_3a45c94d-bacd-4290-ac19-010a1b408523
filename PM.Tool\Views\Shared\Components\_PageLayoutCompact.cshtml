@{
    var pageTitle = ViewData["PageTitle"]?.ToString() ?? "Page Title";
    var pageSubtitle = ViewData["PageSubtitle"]?.ToString();
    var pageIcon = ViewData["PageIcon"]?.ToString();
    var pageActions = ViewData["PageActions"]?.ToString();
    var showBreadcrumb = ViewData["ShowBreadcrumb"] as bool? ?? false;
    var breadcrumbItems = ViewData["BreadcrumbItems"] as IEnumerable<object>;
    var showStats = ViewData["ShowStats"] as bool? ?? false;
    var statsContent = ViewData["StatsContent"]?.ToString();
    var showFilters = ViewData["ShowFilters"] as bool? ?? false;
    var filtersContent = ViewData["FiltersContent"]?.ToString();
    var sidebarContent = ViewData["SidebarContent"]?.ToString();
    var hasSidebar = !string.IsNullOrEmpty(sidebarContent);
}

<div class="layout-compact">
    @* Breadcrumb Navigation *@
    @if (showBreadcrumb && breadcrumbItems != null)
    {
        <nav class="nav-compact">
            @foreach (var item in breadcrumbItems)
            {
                var itemData = item as dynamic;
                if (itemData?.IsActive == true)
                {
                    <span class="nav-item-compact active">@itemData.Text</span>
                }
                else
                {
                    <a href="@itemData?.Href" class="nav-item-compact">@itemData?.Text</a>
                }
            }
        </nav>
    }

    @* Page Header *@
    <div class="page-header-compact">
        <div class="flex items-start gap-4">
            @if (!string.IsNullOrEmpty(pageIcon))
            {
                <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center flex-shrink-0">
                    <i class="@pageIcon text-primary-600 dark:text-primary-400 text-lg"></i>
                </div>
            }
            
            <div>
                <h1 class="page-title-compact">@pageTitle</h1>
                @if (!string.IsNullOrEmpty(pageSubtitle))
                {
                    <p class="page-subtitle-compact">@pageSubtitle</p>
                }
            </div>
        </div>
        
        @if (!string.IsNullOrEmpty(pageActions))
        {
            <div class="page-actions-compact">
                @Html.Raw(pageActions)
            </div>
        }
    </div>

    @* Statistics Section *@
    @if (showStats && !string.IsNullOrEmpty(statsContent))
    {
        <div class="stats-grid-compact">
            @Html.Raw(statsContent)
        </div>
    }

    @* Filters Section *@
    @if (showFilters && !string.IsNullOrEmpty(filtersContent))
    {
        <div class="card-compact mb-6">
            <div class="card-compact-body">
                @Html.Raw(filtersContent)
            </div>
        </div>
    }

    @* Main Content Area *@
    <div class="@(hasSidebar ? "content-grid-compact" : "")">
        <div class="main-content">
            @RenderBody()
        </div>
        
        @if (hasSidebar)
        {
            <div class="sidebar-content">
                @Html.Raw(sidebarContent)
            </div>
        }
    </div>
</div>

@* Usage Examples:

<!-- Basic Page Layout -->
@{
    ViewData["PageTitle"] = "Projects";
    ViewData["PageSubtitle"] = "Manage your project portfolio";
    ViewData["PageIcon"] = "fas fa-folder-open";
    ViewData["PageActions"] = "<button class='btn-compact btn-compact-primary'><i class='fas fa-plus'></i> New Project</button>";
}
<partial name="Components/_PageLayoutCompact" view-data="ViewData">
    <!-- Main content goes here -->
    <div class="space-y-6">
        <!-- Content -->
    </div>
</partial>

<!-- Page with Stats and Filters -->
@{
    ViewData["PageTitle"] = "Dashboard";
    ViewData["PageIcon"] = "fas fa-tachometer-alt";
    ViewData["ShowStats"] = true;
    ViewData["StatsContent"] = "<!-- Stats cards HTML -->";
    ViewData["ShowFilters"] = true;
    ViewData["FiltersContent"] = "<!-- Filters HTML -->";
}
<partial name="Components/_PageLayoutCompact" view-data="ViewData">
    <!-- Dashboard content -->
</partial>

<!-- Page with Sidebar -->
@{
    ViewData["PageTitle"] = "Project Details";
    ViewData["SidebarContent"] = "<!-- Sidebar content HTML -->";
}
<partial name="Components/_PageLayoutCompact" view-data="ViewData">
    <!-- Main project details -->
</partial>

<!-- Page with Breadcrumbs -->
@{
    ViewData["PageTitle"] = "Task Details";
    ViewData["ShowBreadcrumb"] = true;
    ViewData["BreadcrumbItems"] = new[] {
        new { Text = "Projects", Href = "/Projects", IsActive = false },
        new { Text = "Project Alpha", Href = "/Projects/1", IsActive = false },
        new { Text = "Task Details", Href = "", IsActive = true }
    };
}
<partial name="Components/_PageLayoutCompact" view-data="ViewData">
    <!-- Task details content -->
</partial>

*@
