@model ActivityFeedViewModel
@{
    ViewData["Title"] = "Activity Feed";
    ViewData["Breadcrumbs"] = new List<object>
    {
        new { Text = "Home", Href = "/", Icon = "fas fa-home" },
        new { Text = "Activity Feed", Href = (string?)null, Icon = "fas fa-stream" }
    };
}

<div class="min-h-screen bg-neutral-50 dark:bg-dark-950 transition-colors duration-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-50 flex items-center">
                    <i class="fas fa-stream text-primary-600 dark:text-primary-400 mr-3"></i>
                    Activity Feed
                </h1>
                <p class="text-neutral-600 dark:text-dark-300 mt-1">Real-time project activities and updates</p>
            </div>

            <!-- Controls -->
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                <!-- Project Filter -->
                <div class="w-full sm:w-auto">
                    <select id="projectFilter" class="form-select-custom w-full sm:w-64">
                        <option value="">All Projects</option>
                        @foreach (var project in Model.UserProjects)
                        {
                            <option value="@project.Id" selected="@(project.Id == Model.ProjectId)">
                                @project.Name
                            </option>
                        }
                    </select>
                </div>

                <!-- Connection Status -->
                <div class="flex items-center">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200" id="connectionStatus">
                        <span class="w-2 h-2 rounded-full mr-2 animate-pulse"></span>
                        Connecting...
                    </span>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Activity Feed -->
            <div class="lg:col-span-2">
                <div class="card-custom">
                    <!-- Card Header -->
                    <div class="card-header-custom">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <h2 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center mb-3 sm:mb-0">
                                <i class="fas fa-clock text-primary-600 dark:text-primary-400 mr-2"></i>
                                Recent Activities
                            </h2>
                            <div class="flex gap-2">
                                <button class="btn-outline-custom text-sm" id="refreshFeed">
                                    <i class="fas fa-sync-alt mr-1"></i>
                                    Refresh
                                </button>
                                <button class="btn-secondary-custom text-sm" id="markAllRead">
                                    <i class="fas fa-check-double mr-1"></i>
                                    Mark All Read
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card-body-custom p-0">
                        <!-- Loading State -->
                        <div id="loadingState" class="flex flex-col items-center justify-center py-12">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 dark:border-primary-400"></div>
                            <p class="text-neutral-600 dark:text-dark-300 mt-3">Loading activities...</p>
                        </div>

                        <!-- Activity List -->
                        <div id="activityList" class="divide-y divide-neutral-200 dark:divide-dark-700" style="display: none;">
                            @foreach (var activity in Model.Activities)
                            {
                                <div class="activity-item p-6 hover:bg-neutral-50 dark:hover:bg-dark-800 transition-colors duration-200"
                                     data-activity-id="@activity.Id" data-activity-type="@activity.Type">
                                    <div class="flex items-start space-x-4">
                                        <!-- Activity Icon -->
                                        <div class="flex-shrink-0">
                                            <div class="w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                                                <i class="@GetActivityIcon(activity.Type) text-sm"></i>
                                            </div>
                                        </div>

                                        <!-- Activity Content -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center justify-between mb-1">
                                                <h3 class="text-sm font-medium text-neutral-900 dark:text-dark-50 truncate">
                                                    @activity.Title
                                                </h3>
                                                <time class="text-xs text-neutral-500 dark:text-dark-400 flex-shrink-0 ml-2">
                                                    @GetRelativeTime(activity.Timestamp)
                                                </time>
                                            </div>

                                            <p class="text-sm text-neutral-600 dark:text-dark-300 mb-2">
                                                by <span class="font-medium">@activity.UserName</span>
                                            </p>

                                            @if (!string.IsNullOrEmpty(activity.Description))
                                            {
                                                <p class="text-sm text-neutral-700 dark:text-dark-200 mb-3">@activity.Description</p>
                                            }

                                            @if (!string.IsNullOrEmpty(activity.ProjectName))
                                            {
                                                <div class="flex flex-wrap gap-2">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                                                        @activity.ProjectName
                                                    </span>
                                                    @if (!string.IsNullOrEmpty(activity.TaskTitle))
                                                    {
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                                                            @activity.TaskTitle
                                                        </span>
                                                    }
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        <!-- Empty State -->
                        <div id="emptyState" class="flex flex-col items-center justify-center py-16" style="display: none;">
                            <div class="w-16 h-16 rounded-full bg-neutral-100 dark:bg-dark-800 flex items-center justify-center mb-4">
                                <i class="fas fa-inbox text-2xl text-neutral-400 dark:text-dark-500"></i>
                            </div>
                            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-50 mb-2">No activities yet</h3>
                            <p class="text-neutral-600 dark:text-dark-300 text-center max-w-sm">
                                Activities will appear here as team members work on projects.
                            </p>
                        </div>

                        <!-- Load More -->
                        <div class="border-t border-neutral-200 dark:border-dark-700 p-6 text-center" id="loadMoreContainer" style="display: none;">
                            <button class="btn-outline-custom" id="loadMoreBtn">
                                <i class="fas fa-chevron-down mr-2"></i>
                                Load More Activities
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Online Users -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                            <i class="fas fa-users text-success-600 dark:text-success-400 mr-2"></i>
                            Online Team Members
                        </h3>
                    </div>
                    <div class="card-body-custom">
                        <div id="onlineUsersList" class="space-y-3">
                            <!-- Online users will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                            <i class="fas fa-chart-bar text-info-600 dark:text-info-400 mr-2"></i>
                            Today's Activity
                        </h3>
                    </div>
                    <div class="card-body-custom">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-neutral-900 dark:text-dark-50" id="todayTasks">-</div>
                                <div class="text-sm text-neutral-600 dark:text-dark-300">Tasks Updated</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-neutral-900 dark:text-dark-50" id="todayComments">-</div>
                                <div class="text-sm text-neutral-600 dark:text-dark-300">Comments Added</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Notifications -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                            <i class="fas fa-bell text-warning-600 dark:text-warning-400 mr-2"></i>
                            Recent Notifications
                        </h3>
                    </div>
                    <div class="card-body-custom">
                        <div id="recentNotifications" class="space-y-3">
                            <!-- Notifications will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden data for JavaScript -->
<div data-project-id="@Model.ProjectId" style="display: none;"></div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize activity feed
            const activityFeed = new ActivityFeedManager();
            
            // Project filter change
            $('#projectFilter').on('change', function() {
                const projectId = $(this).val();
                activityFeed.filterByProject(projectId);
            });
            
            // Refresh feed
            $('#refreshFeed').on('click', function() {
                activityFeed.refresh();
            });
            
            // Mark all as read
            $('#markAllRead').on('click', function() {
                activityFeed.markAllAsRead();
            });
            
            // Load more
            $('#loadMoreBtn').on('click', function() {
                activityFeed.loadMore();
            });
            
            // Set up real-time updates
            if (window.collaboration) {
                window.collaboration.on('taskUpdated', function(data) {
                    activityFeed.addRealTimeActivity({
                        type: 'task_updated',
                        title: `Task Updated: ${data.TaskTitle}`,
                        userName: data.UpdatedBy,
                        timestamp: data.UpdatedAt,
                        projectName: data.ProjectName
                    });
                });
                
                window.collaboration.on('commentAdded', function(data) {
                    activityFeed.addRealTimeActivity({
                        type: 'comment_added',
                        title: `Comment Added: ${data.TaskTitle}`,
                        userName: data.AuthorName,
                        timestamp: data.CreatedAt,
                        description: data.Comment.substring(0, 100) + '...'
                    });
                });
                
                window.collaboration.on('userPresenceChanged', function(data) {
                    activityFeed.updateOnlineUsers();
                });
            }
        });
        
        // Activity Feed Manager Class
        class ActivityFeedManager {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 20;
                this.isLoading = false;
                this.hasMoreData = true;
                
                this.loadInitialData();
                this.updateConnectionStatus();
            }
            
            async loadInitialData() {
                this.showLoading();
                await this.loadActivities(1);
                this.hideLoading();
                this.updateOnlineUsers();
                this.updateTodayStats();
            }
            
            async loadActivities(page = 1) {
                if (this.isLoading) return;
                
                this.isLoading = true;
                const projectId = $('#projectFilter').val();
                
                try {
                    const response = await fetch(`/Activity/GetActivities?projectId=${projectId}&page=${page}&pageSize=${this.pageSize}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        if (page === 1) {
                            this.renderActivities(data.activities);
                        } else {
                            this.appendActivities(data.activities);
                        }
                        
                        this.hasMoreData = data.activities.length === this.pageSize;
                        this.updateLoadMoreButton();
                    }
                } catch (error) {
                    console.error('Error loading activities:', error);
                } finally {
                    this.isLoading = false;
                }
            }
            
            renderActivities(activities) {
                const container = $('#activityList');
                container.empty();
                
                if (activities.length === 0) {
                    $('#emptyState').show();
                    container.hide();
                    return;
                }
                
                $('#emptyState').hide();
                container.show();
                
                activities.forEach(activity => {
                    container.append(this.createActivityElement(activity));
                });
            }
            
            appendActivities(activities) {
                const container = $('#activityList');
                activities.forEach(activity => {
                    container.append(this.createActivityElement(activity));
                });
            }
            
            createActivityElement(activity) {
                const icon = this.getActivityIcon(activity.type);
                return `
                    <div class="activity-item p-6 hover:bg-neutral-50 dark:hover:bg-dark-800 transition-colors duration-200 border-b border-neutral-200 dark:border-dark-700"
                         data-activity-id="${activity.id}" data-activity-type="${activity.type}">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                                    <i class="${icon} text-sm"></i>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-1">
                                    <h3 class="text-sm font-medium text-neutral-900 dark:text-dark-50 truncate">
                                        ${activity.title}
                                    </h3>
                                    <time class="text-xs text-neutral-500 dark:text-dark-400 flex-shrink-0 ml-2">
                                        ${activity.relativeTime}
                                    </time>
                                </div>
                                <p class="text-sm text-neutral-600 dark:text-dark-300 mb-2">
                                    by <span class="font-medium">${activity.userName}</span>
                                </p>
                                ${activity.description ? `<p class="text-sm text-neutral-700 dark:text-dark-200 mb-3">${activity.description}</p>` : ''}
                                ${activity.projectName ? `
                                    <div class="flex flex-wrap gap-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                                            ${activity.projectName}
                                        </span>
                                        ${activity.taskTitle ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">${activity.taskTitle}</span>` : ''}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            getActivityIcon(type) {
                const iconMap = {
                    'task_created': 'fas fa-plus-circle text-success',
                    'task_updated': 'fas fa-edit text-primary',
                    'task_completed': 'fas fa-check-circle text-success',
                    'comment_added': 'fas fa-comment text-info',
                    'project_updated': 'fas fa-project-diagram text-warning',
                    'user_assigned': 'fas fa-user-plus text-primary'
                };
                return iconMap[type] || 'fas fa-circle text-secondary';
            }
            
            addRealTimeActivity(activity) {
                const container = $('#activityList');
                const element = $(this.createActivityElement(activity));
                container.prepend(element);
                
                // Remove oldest if too many
                const items = container.find('.activity-item');
                if (items.length > 50) {
                    items.last().remove();
                }
            }
            
            async filterByProject(projectId) {
                this.currentPage = 1;
                await this.loadActivities(1);
            }
            
            async refresh() {
                this.currentPage = 1;
                await this.loadActivities(1);
            }
            
            async loadMore() {
                this.currentPage++;
                await this.loadActivities(this.currentPage);
            }
            
            async markAllAsRead() {
                try {
                    const response = await fetch('/Activity/MarkAsRead', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                        },
                        body: JSON.stringify({ activityIds: [] })
                    });
                    
                    if (response.ok) {
                        $('.activity-item').removeClass('unread');
                    }
                } catch (error) {
                    console.error('Error marking activities as read:', error);
                }
            }
            
            updateLoadMoreButton() {
                const container = $('#loadMoreContainer');
                if (this.hasMoreData) {
                    container.show();
                } else {
                    container.hide();
                }
            }
            
            updateConnectionStatus() {
                const statusElement = $('#connectionStatus');
                if (window.collaboration && window.collaboration.isConnected) {
                    statusElement.removeClass('bg-neutral-500 bg-danger-500 text-neutral-100 text-danger-100')
                               .addClass('bg-success-500 text-success-100');
                    statusElement.html('<span class="w-2 h-2 rounded-full bg-success-300 mr-2"></span>Connected');
                } else {
                    statusElement.removeClass('bg-success-500 bg-neutral-500 text-success-100 text-neutral-100')
                               .addClass('bg-danger-500 text-danger-100');
                    statusElement.html('<span class="w-2 h-2 rounded-full bg-danger-300 mr-2 animate-pulse"></span>Disconnected');
                }
            }

            updateOnlineUsers() {
                const container = $('#onlineUsersList');
                if (window.collaboration) {
                    const onlineUsers = window.collaboration.getOnlineUsers();
                    container.empty();

                    if (onlineUsers.length === 0) {
                        container.html(`
                            <div class="text-center py-4">
                                <div class="w-12 h-12 rounded-full bg-neutral-100 dark:bg-dark-800 flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-user-slash text-neutral-400 dark:text-dark-500"></i>
                                </div>
                                <p class="text-sm text-neutral-600 dark:text-dark-300">No team members online</p>
                            </div>
                        `);
                        return;
                    }

                    onlineUsers.forEach(user => {
                        container.append(`
                            <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-neutral-50 dark:hover:bg-dark-800 transition-colors duration-200">
                                <div class="relative">
                                    <div class="w-8 h-8 rounded-full bg-primary-600 dark:bg-primary-500 flex items-center justify-center text-white text-sm font-medium">
                                        ${user.userName.charAt(0).toUpperCase()}
                                    </div>
                                    <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full bg-success-500 border-2 border-white dark:border-dark-800"></div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-neutral-900 dark:text-dark-50 truncate">${user.userName}</p>
                                    <p class="text-xs text-neutral-600 dark:text-dark-300 truncate">${user.currentActivity || 'Active'}</p>
                                </div>
                            </div>
                        `);
                    });
                } else {
                    container.html(`
                        <div class="text-center py-4">
                            <div class="w-12 h-12 rounded-full bg-neutral-100 dark:bg-dark-800 flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-wifi-slash text-neutral-400 dark:text-dark-500"></i>
                            </div>
                            <p class="text-sm text-neutral-600 dark:text-dark-300">Connection unavailable</p>
                        </div>
                    `);
                }
            }
            
            updateTodayStats() {
                // Update today's statistics
                $('#todayTasks').text('12'); // Placeholder
                $('#todayComments').text('8'); // Placeholder
            }
            
            showLoading() {
                $('#loadingState').show();
                $('#activityList').hide();
                $('#emptyState').hide();
            }
            
            hideLoading() {
                $('#loadingState').hide();
            }
        }
    </script>
}

@functions {
    private string GetActivityIcon(string type)
    {
        return type switch
        {
            "task_created" => "fas fa-plus-circle text-success",
            "task_updated" => "fas fa-edit text-primary",
            "task_completed" => "fas fa-check-circle text-success",
            "comment_added" => "fas fa-comment text-info",
            "project_updated" => "fas fa-project-diagram text-warning",
            "user_assigned" => "fas fa-user-plus text-primary",
            _ => "fas fa-circle text-secondary"
        };
    }
    
    private string GetRelativeTime(DateTime timestamp)
    {
        var timeSpan = DateTime.UtcNow - timestamp;
        
        if (timeSpan.TotalMinutes < 1)
            return "Just now";
        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes} minutes ago";
        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours} hours ago";
        if (timeSpan.TotalDays < 7)
            return $"{(int)timeSpan.TotalDays} days ago";
            
        return timestamp.ToString("MMM dd, yyyy");
    }
}
