@model PM.Tool.Core.Entities.Agile.UserStory

<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-start justify-between">
        <div>
            <div class="flex items-center gap-3 mb-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                    <EMAIL>
                </span>
                @{
                    var priorityClass = Model.Priority switch
                    {
                        PM.Tool.Core.Entities.Agile.UserStoryPriority.Critical => "bg-danger-100 text-danger-700 dark:bg-danger-900/30 dark:text-danger-300",
                        PM.Tool.Core.Entities.Agile.UserStoryPriority.High => "bg-warning-100 text-warning-700 dark:bg-warning-900/30 dark:text-warning-300",
                        PM.Tool.Core.Entities.Agile.UserStoryPriority.Medium => "bg-info-100 text-info-700 dark:bg-info-900/30 dark:text-info-300",
                        PM.Tool.Core.Entities.Agile.UserStoryPriority.Low => "bg-success-100 text-success-700 dark:bg-success-900/30 dark:text-success-300",
                        _ => "bg-neutral-100 text-neutral-700 dark:bg-dark-700 dark:text-dark-300"
                    };
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium @priorityClass">
                    @Model.Priority
                </span>
                @if (Model.StoryPoints > 0)
                {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300">
                        @Model.StoryPoints SP
                    </span>
                }
            </div>
            <h2 class="text-xl font-semibold text-neutral-900 dark:text-dark-50 mb-2">@Model.Title</h2>
            @if (Model.Epic != null)
            {
                <div class="flex items-center gap-2 text-sm text-neutral-600 dark:text-dark-400">
                    <i class="fas fa-mountain text-purple-600 dark:text-purple-400"></i>
                    <span>Epic: @Model.Epic.Title (@Model.Epic.EpicKey)</span>
                </div>
            }
        </div>
        
        @{
            var statusClass = Model.Status switch
            {
                PM.Tool.Core.Entities.Agile.UserStoryStatus.Backlog => "bg-neutral-100 text-neutral-700 dark:bg-dark-700 dark:text-dark-300",
                PM.Tool.Core.Entities.Agile.UserStoryStatus.Ready => "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
                PM.Tool.Core.Entities.Agile.UserStoryStatus.InProgress => "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300",
                PM.Tool.Core.Entities.Agile.UserStoryStatus.Review => "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300",
                PM.Tool.Core.Entities.Agile.UserStoryStatus.Done => "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300",
                _ => "bg-neutral-100 text-neutral-700 dark:bg-dark-700 dark:text-dark-300"
            };
        }
        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @statusClass">
            @Model.Status
        </span>
    </div>

    <!-- Description -->
    @if (!string.IsNullOrEmpty(Model.Description))
    {
        <div>
            <h3 class="text-sm font-medium text-neutral-900 dark:text-dark-50 mb-2">Description</h3>
            <div class="text-sm text-neutral-700 dark:text-dark-300 bg-neutral-50 dark:bg-dark-800 rounded-lg p-4">
                @Html.Raw(Model.Description.Replace("\n", "<br>"))
            </div>
        </div>
    }

    <!-- Acceptance Criteria -->
    @if (!string.IsNullOrEmpty(Model.AcceptanceCriteria))
    {
        <div>
            <h3 class="text-sm font-medium text-neutral-900 dark:text-dark-50 mb-2">Acceptance Criteria</h3>
            <div class="text-sm text-neutral-700 dark:text-dark-300 bg-neutral-50 dark:bg-dark-800 rounded-lg p-4">
                @Html.Raw(Model.AcceptanceCriteria.Replace("\n", "<br>"))
            </div>
        </div>
    }

    <!-- Details Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Assignment -->
        <div>
            <h3 class="text-sm font-medium text-neutral-900 dark:text-dark-50 mb-3">Assignment</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-neutral-600 dark:text-dark-400">Assigned To:</span>
                    @if (Model.AssignedTo != null && !string.IsNullOrEmpty(Model.AssignedTo.UserName))
                    {
                        <div class="flex items-center gap-2">
                            <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-xs font-semibold text-white">@Model.AssignedTo.UserName.Substring(0, 1).ToUpper()</span>
                            </div>
                            <span class="text-sm font-medium text-neutral-900 dark:text-dark-50">@Model.AssignedTo.UserName</span>
                        </div>
                    }
                    else
                    {
                        <span class="text-sm text-neutral-500 dark:text-dark-400">Unassigned</span>
                    }
                </div>
                
                @if (Model.Sprint != null)
                {
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-neutral-600 dark:text-dark-400">Sprint:</span>
                        <span class="text-sm font-medium text-neutral-900 dark:text-dark-50">@Model.Sprint.Name</span>
                    </div>
                }
            </div>
        </div>

        <!-- Metadata -->
        <div>
            <h3 class="text-sm font-medium text-neutral-900 dark:text-dark-50 mb-3">Details</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-neutral-600 dark:text-dark-400">Created:</span>
                    <span class="text-sm text-neutral-900 dark:text-dark-50">@Model.CreatedAt.ToString("MMM dd, yyyy")</span>
                </div>
                
                @if (Model.UpdatedAt.HasValue)
                {
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-neutral-600 dark:text-dark-400">Updated:</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-50">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</span>
                    </div>
                }
                
                @if (Model.StoryPoints > 0)
                {
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-neutral-600 dark:text-dark-400">Story Points:</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-50">@Model.StoryPoints points</span>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="flex items-center justify-between pt-4 border-t border-neutral-200 dark:border-dark-700">
        <div class="flex gap-3">
            <a href="@Url.Action("EditUserStory", new { id = Model.Id })" class="btn-primary-custom">
                <i class="fas fa-edit mr-2"></i>
                Edit Story
            </a>
            <button onclick="closeUserStoryModal()" class="btn-outline-custom">
                Close
            </button>
        </div>
        
        <div class="text-xs text-neutral-500 dark:text-dark-400">
            Last updated @(Model.UpdatedAt?.ToString("MMM dd, yyyy 'at' h:mm tt") ?? Model.CreatedAt.ToString("MMM dd, yyyy 'at' h:mm tt"))
        </div>
    </div>
</div>
