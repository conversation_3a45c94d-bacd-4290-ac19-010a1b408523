@*
    Standardized Filter Bar Component
    Usage: <partial name="Components/_FilterBar" view-data="ViewData" />
    
    Required ViewData:
    - QuickFilters: Array of quick filter objects { Text, Icon, OnClick }
    - SearchPlaceholder: String for search input placeholder
    - FilterDropdowns: Array of dropdown objects { Name, Label, Options }
    
    Optional ViewData:
    - FormId: Form ID (default: "filterForm")
    - SearchName: Search input name (default: "search")
    - ShowClearButton: Boolean to show clear button (default: true)
*@

@{
    var formId = ViewData["FormId"]?.ToString() ?? "filterForm";
    var searchName = ViewData["SearchName"]?.ToString() ?? "search";
    var searchPlaceholder = ViewData["SearchPlaceholder"]?.ToString() ?? "Search...";
    var showClearButton = ViewData["ShowClearButton"] as bool? ?? true;
    var quickFilters = ViewData["QuickFilters"] as object[] ?? new object[0];
    var filterDropdowns = ViewData["FilterDropdowns"] as object[] ?? new object[0];
}

<!-- Standardized Filter Bar -->
<div class="card-custom mb-8">
    <form id="@formId" method="get" class="card-body-custom">
        <!-- Single Row Layout -->
        <div class="flex flex-wrap items-center gap-6">
            @if (quickFilters.Any())
            {
                <!-- Quick Filters Section -->
                <div class="flex items-center gap-3">
                    <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-3">Quick:</span>
                    <div class="flex items-center gap-2">
                        @foreach (dynamic filter in quickFilters)
                        {
                            ViewData["Text"] = filter.Text;
                            ViewData["Variant"] = "outline";
                            ViewData["Size"] = "sm";
                            ViewData["Icon"] = filter.Icon;
                            ViewData["OnClick"] = filter.OnClick;
                            ViewData["AdditionalClasses"] = "quick-filter-btn";
                            ViewData["Href"] = null;
                            <partial name="Components/_Button" view-data="ViewData" />
                        }
                    </div>
                </div>
            }

            <!-- Main Filters Section -->
            <div class="flex items-center gap-4 flex-1">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text"
                           name="@searchName"
                           placeholder="@searchPlaceholder"
                           class="w-64 pl-11 pr-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 transition-all duration-200">
                </div>

                @if (filterDropdowns.Any())
                {
                    <!-- Filter Controls -->
                    <div class="flex items-center gap-3">
                        @foreach (dynamic dropdown in filterDropdowns)
                        {
                            <select name="@dropdown.Name" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                                @Html.Raw(dropdown.Options)
                            </select>
                        }

                        @if (showClearButton)
                        {
                            <!-- Clear Filters Button -->
                            ViewData["Text"] = "Clear";
                            ViewData["Variant"] = "outline";
                            ViewData["Size"] = "sm";
                            ViewData["Icon"] = "fas fa-times";
                            ViewData["OnClick"] = "clearAllFilters()";
                            ViewData["Href"] = null;
                            <partial name="Components/_Button" view-data="ViewData" />
                        }
                    </div>
                }
            </div>
        </div>
    </form>
</div>

<style>
    /* Quick Filter Button Styles */
    .quick-filter-btn {
        transition: all 0.2s ease;
    }

    .quick-filter-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .quick-filter-btn.active {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        border-color: var(--primary-500);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        transform: translateY(-1px);
    }

    .dark .quick-filter-btn.active {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        border-color: var(--primary-400);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    /* Filter Bar Enhancement */
    .filter-bar {
        background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,250,252,0.98));
        backdrop-filter: blur(8px);
    }

    .dark .filter-bar {
        background: linear-gradient(135deg, rgba(31,41,55,0.95), rgba(17,24,39,0.98));
    }
</style>

<script>
    // Standard filter functionality
    function clearAllFilters() {
        // Clear all form inputs
        const form = document.getElementById('@formId');
        if (form) {
            const inputs = form.querySelectorAll('input, select');
            inputs.forEach(input => {
                if (input.type === 'text' || input.type === 'search') {
                    input.value = '';
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                }
            });

            // Remove active class from quick filter buttons
            document.querySelectorAll('.quick-filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Trigger filter update
            if (typeof applyFilters === 'function') {
                applyFilters();
            }
        }
    }

    // Initialize filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listeners for form changes
        const form = document.getElementById('@formId');
        if (form) {
            const inputs = form.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.addEventListener('change', function() {
                    if (typeof applyFilters === 'function') {
                        applyFilters();
                    }
                });

                // For text inputs, add debounced search
                if (input.type === 'text' || input.type === 'search') {
                    let searchTimeout;
                    input.addEventListener('input', function() {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            if (typeof applyFilters === 'function') {
                                applyFilters();
                            }
                        }, 300);
                    });
                }
            });
        }
    });
</script>
