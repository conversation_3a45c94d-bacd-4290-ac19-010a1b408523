using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using System.Text.RegularExpressions;

namespace PM.Tool.Application.Services
{
    public interface IMentionService
    {
        /// <summary>
        /// Parse mentions from content and return mentioned user IDs
        /// </summary>
        Task<List<string>> ParseMentionsAsync(string content);

        /// <summary>
        /// Process mentions in content and send notifications
        /// </summary>
        Task ProcessMentionsAsync(string content, string authorId, int? taskId = null, int? projectId = null, string? context = null);

        /// <summary>
        /// Format content with mention highlights for display
        /// </summary>
        Task<string> FormatMentionsAsync(string content);

        /// <summary>
        /// Get users available for mentioning in a project context
        /// </summary>
        Task<List<object>> GetMentionableUsersAsync(int? projectId = null);

        /// <summary>
        /// Validate if a user can be mentioned in the given context
        /// </summary>
        Task<bool> CanMentionUserAsync(string userId, int? projectId = null);
    }

    public class MentionService : IMentionService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly INotificationService _notificationService;
        private readonly ILogger<MentionService> _logger;

        // Regex pattern to match @mentions (username or display name)
        private static readonly Regex MentionRegex = new(@"@([a-zA-Z0-9._-]+|""[^""]+"")", RegexOptions.Compiled | RegexOptions.IgnoreCase);

        public MentionService(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            INotificationService notificationService,
            ILogger<MentionService> logger)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
            _logger = logger;
        }

        public async Task<List<string>> ParseMentionsAsync(string content)
        {
            if (string.IsNullOrWhiteSpace(content))
                return new List<string>();

            var mentionedUserIds = new List<string>();
            var matches = MentionRegex.Matches(content);

            foreach (Match match in matches)
            {
                var mention = match.Groups[1].Value.Trim('"'); // Remove quotes if present
                
                // Try to find user by username first
                var user = await _userManager.FindByNameAsync(mention);
                
                // If not found by username, try by display name (FirstName LastName)
                if (user == null)
                {
                    user = await _context.Users
                        .FirstOrDefaultAsync(u => u.IsActive && 
                            (u.FirstName + " " + u.LastName).Trim().Equals(mention, StringComparison.OrdinalIgnoreCase));
                }

                // If still not found, try by email
                if (user == null)
                {
                    user = await _userManager.FindByEmailAsync(mention);
                }

                if (user != null && user.IsActive && !mentionedUserIds.Contains(user.Id))
                {
                    mentionedUserIds.Add(user.Id);
                }
            }

            return mentionedUserIds;
        }

        public async Task ProcessMentionsAsync(string content, string authorId, int? taskId = null, int? projectId = null, string? context = null)
        {
            try
            {
                var mentionedUserIds = await ParseMentionsAsync(content);
                if (!mentionedUserIds.Any())
                    return;

                var author = await _userManager.FindByIdAsync(authorId);
                if (author == null)
                    return;

                foreach (var userId in mentionedUserIds)
                {
                    // Don't notify the author of their own mention
                    if (userId == authorId)
                        continue;

                    // Check if user can be mentioned in this context
                    if (!await CanMentionUserAsync(userId, projectId))
                        continue;

                    // Create notification
                    var title = "You were mentioned";
                    var message = $"{author.FirstName} {author.LastName} mentioned you in {context ?? "a comment"}.";

                    await _notificationService.CreateNotificationAsync(
                        userId, 
                        title, 
                        message, 
                        Core.Enums.NotificationType.Mention, 
                        projectId, 
                        taskId);

                    _logger.LogInformation("Mention notification sent to user {UserId} from {AuthorId}", userId, authorId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing mentions in content: {Content}", content);
            }
        }

        public async Task<string> FormatMentionsAsync(string content)
        {
            if (string.IsNullOrWhiteSpace(content))
                return content;

            var formattedContent = content;
            var matches = MentionRegex.Matches(content);

            // Process matches in reverse order to maintain string positions
            foreach (Match match in matches.Cast<Match>().Reverse())
            {
                var mention = match.Groups[1].Value.Trim('"');
                var user = await FindUserByMentionAsync(mention);

                if (user != null)
                {
                    var displayName = $"{user.FirstName} {user.LastName}".Trim();
                    if (string.IsNullOrEmpty(displayName))
                        displayName = user.UserName ?? "Unknown User";

                    var mentionHtml = $"<span class=\"mention\" data-user-id=\"{user.Id}\" title=\"{displayName}\">@{displayName}</span>";
                    formattedContent = formattedContent.Remove(match.Index, match.Length)
                                                     .Insert(match.Index, mentionHtml);
                }
            }

            return formattedContent;
        }

        public async Task<List<object>> GetMentionableUsersAsync(int? projectId = null)
        {
            try
            {
                IQueryable<ApplicationUser> usersQuery = _context.Users.Where(u => u.IsActive);

                // If project context is provided, filter to project members
                if (projectId.HasValue)
                {
                    usersQuery = usersQuery.Where(u => _context.ProjectMembers
                        .Any(pm => pm.ProjectId == projectId.Value && pm.UserId == u.Id));
                }

                var users = await usersQuery
                    .Select(u => new
                    {
                        id = u.Id,
                        username = u.UserName,
                        displayName = (u.FirstName + " " + u.LastName).Trim(),
                        email = u.Email,
                        avatar = u.ProfilePictureUrl ?? "/images/default-avatar.png"
                    })
                    .OrderBy(u => u.displayName)
                    .ToListAsync();

                return users.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mentionable users for project {ProjectId}", projectId);
                return new List<object>();
            }
        }

        public async Task<bool> CanMentionUserAsync(string userId, int? projectId = null)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                    return false;

                // If no project context, any active user can be mentioned
                if (!projectId.HasValue)
                    return true;

                // Check if user is a member of the project
                var isMember = await _context.ProjectMembers
                    .AnyAsync(pm => pm.ProjectId == projectId.Value && pm.UserId == userId);

                return isMember;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {UserId} can be mentioned in project {ProjectId}", userId, projectId);
                return false;
            }
        }

        private async Task<ApplicationUser?> FindUserByMentionAsync(string mention)
        {
            // Try username first
            var user = await _userManager.FindByNameAsync(mention);
            if (user != null && user.IsActive)
                return user;

            // Try display name using EF Core compatible methods
            var mentionLower = mention.ToLower();
            var activeUsers = await _context.Users
                .Where(u => u.IsActive)
                .ToListAsync(); // Load to memory first

            user = activeUsers.FirstOrDefault(u =>
                (u.FirstName + " " + u.LastName).Trim().Equals(mention, StringComparison.OrdinalIgnoreCase));
            if (user != null)
                return user;

            // Try email
            user = await _userManager.FindByEmailAsync(mention);
            if (user != null && user.IsActive)
                return user;

            return null;
        }
    }
}
