﻿<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - PM <PERSON></title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="~/css/tailwind.css" asp-append-version="true" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <!-- Third-party CSS (Tailwind compatible) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
    <link rel="stylesheet" href="~/css/select2-tailwind.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css" />

    <!-- Mentions System CSS -->
    <link rel="stylesheet" href="~/css/mentions.css" asp-append-version="true" />

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Page-specific styles -->
    @await RenderSectionAsync("Styles", required: false)

    <!-- Fix for undefined colors and styles -->
    @await Html.PartialAsync("_UndefinedStylesFix")

    <!-- Immediate Theme Application -->
    <script>
        // Apply saved theme immediately to prevent flash
        (function() {
            try {
                const savedTheme = localStorage.getItem('pm-tool-theme') || 'light';
                if (savedTheme === 'dark') {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }
            } catch (error) {
                console.warn('Theme initialization error:', error);
            }
        })();
    </script>


</head>
<body class="h-full bg-neutral-50 dark:bg-neutral-900 font-sans antialiased">
    <!-- Sidebar -->
    <partial name="_Sidebar" />

    <!-- Main Content Area -->
    <div class="lg:ml-64 flex flex-col min-h-screen">
        <!-- Topbar -->
        <partial name="_Topbar" />

        <!-- Main Content -->
        <main class="flex-1 pt-16 px-6 py-8">
            <!-- Alert Messages -->
            @if (TempData["Success"] != null)
            {
                <div class="alert-success-custom mb-6 animate-slide-in" role="alert">
                    <i class="fas fa-check-circle text-success-600 dark:text-success-400"></i>
                    <div>
                        <p class="font-medium">Success!</p>
                        <p class="text-sm">@TempData["Success"]</p>
                    </div>
                    <button type="button" class="ml-auto text-success-600 dark:text-success-400 hover:text-success-800 dark:hover:text-success-200" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            }
            @if (TempData["Error"] != null)
            {
                <div class="alert-danger-custom mb-6 animate-slide-in" role="alert">
                    <i class="fas fa-exclamation-circle text-danger-600 dark:text-danger-400"></i>
                    <div>
                        <p class="font-medium">Error!</p>
                        <p class="text-sm">@TempData["Error"]</p>
                    </div>
                    <button type="button" class="ml-auto text-danger-600 dark:text-danger-400 hover:text-danger-800 dark:hover:text-danger-200" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            }
            @if (TempData["Warning"] != null)
            {
                <div class="alert-warning-custom mb-6 animate-slide-in" role="alert">
                    <i class="fas fa-exclamation-triangle text-warning-600 dark:text-warning-400"></i>
                    <div>
                        <p class="font-medium">Warning!</p>
                        <p class="text-sm">@TempData["Warning"]</p>
                    </div>
                    <button type="button" class="ml-auto text-warning-600 dark:text-warning-400 hover:text-warning-800 dark:hover:text-warning-200" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            }
            @if (TempData["Info"] != null)
            {
                <div class="alert-info-custom mb-6 animate-slide-in" role="alert">
                    <i class="fas fa-info-circle text-info-600 dark:text-info-400"></i>
                    <div>
                        <p class="font-medium">Information</p>
                        <p class="text-sm">@TempData["Info"]</p>
                    </div>
                    <button type="button" class="ml-auto text-info-600 dark:text-info-400 hover:text-info-800 dark:hover:text-info-200" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            }

            <!-- Page Content -->
            <div class="max-w-7xl mx-auto">
                @RenderBody()
            </div>
        </main>

        <!-- Footer -->
        <partial name="_Footer" />
    </div>

    <!-- Project Selector Modal -->
    <div id="projectSelectorModal" class="modal-custom hidden">
        <div class="modal-backdrop" onclick="closeProjectModal()"></div>
        <div class="modal-content-custom">
            <div class="modal-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-white" id="projectSelectorModalLabel">Select Project</h3>
                <button type="button" class="text-neutral-400 dark:text-gray-400 hover:text-neutral-600 dark:hover:text-gray-300" onclick="closeProjectModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body-custom">
                <p class="text-neutral-600 dark:text-gray-300 mb-4">Please select a project to view the chart:</p>
                <div id="projectList" class="space-y-2">
                    <!-- Projects will be loaded here -->
                </div>
            </div>
            <div class="modal-footer-custom">
                <button type="button" class="btn-secondary-custom" onclick="closeProjectModal()">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Real-time Collaboration Status -->
    <div class="connection-status" style="display: none;">Connecting...</div>

    <!-- Hidden data for JavaScript -->
    @if (User.Identity?.IsAuthenticated == true)
    {
        <div data-current-user="@User.Identity.Name" style="display: none;"></div>
    }

    <!-- Toast Container for Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2" aria-live="polite" aria-atomic="true"></div>

    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>

    <!-- Third-party Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>

    <!-- Chart.js for analytics and reporting -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

    <!-- Sortable.js for drag-and-drop functionality -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

    <!-- SignalR for real-time collaboration -->
    <script src="https://unpkg.com/@@microsoft/signalr@latest/dist/browser/signalr.min.js"></script>

    <!-- Application Scripts -->
    <script src="~/js/modern-ui.js" asp-append-version="true"></script>
    <script src="~/js/tailwind-theme-manager.js" asp-append-version="true"></script>
    <script src="~/js/select2-init.js" asp-append-version="true"></script>
    <script src="~/js/mentions.js" asp-append-version="true"></script>
    <script src="~/js/collaboration.js" asp-append-version="true"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <!-- Page-specific scripts -->
    @await RenderSectionAsync("Scripts", required: false)

    <!-- Initialize Application -->
    <script>
        // Global functions for modal management
        function closeProjectModal() {
            document.getElementById('projectSelectorModal').classList.add('hidden');
        }

        function showProjectModal(chartType) {
            const modal = document.getElementById('projectSelectorModal');
            const title = document.getElementById('projectSelectorModalLabel');
            const projectList = document.getElementById('projectList');

            title.textContent = 'Select Project for ' + chartType.charAt(0).toUpperCase() + chartType.slice(1) + ' Chart';

            // Show loading state
            projectList.innerHTML = `
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-neutral-600 dark:text-gray-300">Loading projects...</span>
                </div>
            `;

            modal.classList.remove('hidden');

            // Simulate loading projects (replace with actual AJAX call)
            setTimeout(function() {
                projectList.innerHTML = `
                    <div class="alert-info-custom">
                        <i class="fas fa-info-circle text-info-600 dark:text-info-400"></i>
                        <div>
                            <p class="font-medium">Feature Coming Soon!</p>
                            <p class="text-sm">Charts will be available once projects are selected.</p>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <a href="/Analytics" class="btn-primary-custom">
                            <i class="fas fa-chart-line mr-2"></i>
                            Go to Analytics Dashboard
                        </a>
                    </div>
                `;
            }, 1000);
        }

        $(document).ready(function() {
            // Initialize theme manager
            if (window.TailwindThemeManager) {
                console.log('Tailwind Theme Manager ready');
            } else {
                console.warn('Tailwind Theme Manager not found');
            }

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                document.querySelectorAll('[class*="alert-"]').forEach(function(alert) {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-10px)';
                    setTimeout(function() {
                        alert.remove();
                    }, 300);
                });
            }, 5000);

            // Initialize sidebar toggle functionality
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebarClose = document.getElementById('sidebar-close');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            function openSidebar() {
                sidebar.classList.remove('-translate-x-full');
                sidebarOverlay.classList.remove('hidden');
            }

            function closeSidebar() {
                sidebar.classList.add('-translate-x-full');
                sidebarOverlay.classList.add('hidden');
            }

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', openSidebar);
            }

            if (sidebarClose) {
                sidebarClose.addEventListener('click', closeSidebar);
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }

            // Initialize dropdown toggles
            const userMenuToggle = document.getElementById('user-menu-toggle');
            const userMenu = document.getElementById('user-menu');
            const quickActionsToggle = document.getElementById('quick-actions-toggle');
            const quickActionsMenu = document.getElementById('quick-actions-menu');
            const languageToggle = document.getElementById('language-toggle');
            const languageMenu = document.getElementById('language-menu');

            function toggleDropdown(toggle, menu) {
                if (toggle && menu) {
                    toggle.addEventListener('click', function(e) {
                        e.stopPropagation();
                        menu.classList.toggle('hidden');

                        // Close other dropdowns
                        document.querySelectorAll('[id$="-menu"]').forEach(function(otherMenu) {
                            if (otherMenu !== menu) {
                                otherMenu.classList.add('hidden');
                            }
                        });
                    });
                }
            }

            toggleDropdown(userMenuToggle, userMenu);
            toggleDropdown(quickActionsToggle, quickActionsMenu);
            toggleDropdown(languageToggle, languageMenu);

            // Initialize notifications
            const notificationsToggle = document.getElementById('notifications-toggle');
            const notificationsMenu = document.getElementById('notifications-menu');
            toggleDropdown(notificationsToggle, notificationsMenu);

            // Load notifications when dropdown is opened
            if (notificationsToggle) {
                notificationsToggle.addEventListener('click', function() {
                    if (!notificationsMenu.classList.contains('hidden')) {
                        loadNotifications();
                    }
                });
            }

            // Initialize global search
            initializeGlobalSearch();

            // Close dropdowns when clicking outside
            document.addEventListener('click', function() {
                document.querySelectorAll('[id$="-menu"]').forEach(function(menu) {
                    menu.classList.add('hidden');
                });

                // Also close search results
                const searchResults = document.getElementById('search-results');
                if (searchResults) {
                    searchResults.classList.add('hidden');
                }
            });

            // Add click handlers for language links with debugging
            document.querySelectorAll('#language-menu a').forEach(function(link) {
                link.addEventListener('click', function(e) {
                    console.log('Language link clicked:', this.href);
                    // Allow the default action to proceed
                });
            });

            // Debug current culture
            console.log('Current culture:', '@System.Globalization.CultureInfo.CurrentCulture.Name');
            console.log('Current UI culture:', '@System.Globalization.CultureInfo.CurrentUICulture.Name');
        });

        // Notifications functionality
        async function loadNotifications() {
            const loadingEl = document.getElementById('notifications-loading');
            const emptyEl = document.getElementById('notifications-empty');
            const listEl = document.getElementById('notifications-list');

            if (loadingEl) loadingEl.classList.remove('hidden');
            if (emptyEl) emptyEl.classList.add('hidden');
            if (listEl) listEl.innerHTML = '';

            try {
                const response = await fetch('/api/notifications?limit=10');
                const data = await response.json();



                if (loadingEl) loadingEl.classList.add('hidden');

                if (data.notifications && data.notifications.length > 0) {
                    displayNotifications(data.notifications);
                } else {
                    if (emptyEl) emptyEl.classList.remove('hidden');
                }

                // Update notification count
                updateNotificationBadge();
            } catch (error) {
                console.error('Error loading notifications:', error);
                if (loadingEl) loadingEl.classList.add('hidden');
                if (emptyEl) emptyEl.classList.remove('hidden');
            }
        }

        function displayNotifications(notifications) {
            const listEl = document.getElementById('notifications-list');
            if (!listEl) return;

            console.log('Displaying notifications:', notifications); // Debug log

            listEl.innerHTML = notifications.map(notification => {
    
                return `
                <div class="notification-item px-4 py-3 hover:bg-neutral-50 dark:hover:bg-dark-700 cursor-pointer border-b border-neutral-100 dark:border-dark-200 ${!notification.IsRead ? 'bg-primary-50 dark:bg-primary-900/20' : ''}"
                     data-id="${notification.Id}"
                     data-url="${notification.Url || '#'}"
                     onclick="handleNotificationClick(${notification.Id}, '${notification.Url || '#'}')">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center ${getNotificationIconBg(notification.Type)}">
                                <i class="${getNotificationIcon(notification.Type)} ${getNotificationIconColor(notification.Type)} text-sm"></i>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-neutral-900 dark:text-white">${notification.Title || 'Notification'}</p>
                            <p class="text-xs text-neutral-500 dark:text-dark-400 mt-1">${notification.Message || ''}</p>
                            <p class="text-xs text-neutral-400 dark:text-dark-500 mt-1">${formatNotificationTime(notification.CreatedAt)}</p>
                        </div>
                        ${!notification.IsRead ? '<div class="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"></div>' : ''}
                    </div>
                </div>
            `;
            }).join('');
        }

        async function handleNotificationClick(notificationId, url) {
            try {
                // Mark as read
                await fetch(`/api/notifications/${notificationId}/read`, { method: 'POST' });

                // Update UI
                const notificationEl = document.querySelector(`[data-id="${notificationId}"]`);
                if (notificationEl) {
                    notificationEl.classList.remove('bg-primary-50', 'dark:bg-primary-900/20');
                    const unreadDot = notificationEl.querySelector('.w-2.h-2.bg-primary-500');
                    if (unreadDot) unreadDot.remove();
                }

                // Update badge
                updateNotificationBadge();

                // Navigate if URL provided
                if (url && url !== '#') {
                    window.location.href = url;
                }
            } catch (error) {
                console.error('Error marking notification as read:', error);
            }
        }

        async function markAllNotificationsAsRead() {
            try {
                const response = await fetch('/api/notifications/mark-all-read', { method: 'POST' });
                if (response.ok) {
                    // Reload notifications
                    loadNotifications();
                    // Update badge
                    updateNotificationBadge();
                }
            } catch (error) {
                console.error('Error marking all notifications as read:', error);
            }
        }

        async function updateNotificationBadge() {
            try {
                const response = await fetch('/api/notifications/count');
                const data = await response.json();
                const badge = document.getElementById('notification-badge');

                if (badge) {
                    if (data.count > 0) {
                        badge.textContent = data.count > 99 ? '99+' : data.count;
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                }
            } catch (error) {
                console.error('Error updating notification badge:', error);
            }
        }

        function getNotificationIcon(type) {
            if (!type) return 'fas fa-bell';
            const icons = {
                'Info': 'fas fa-info-circle',
                'Success': 'fas fa-check-circle',
                'Warning': 'fas fa-exclamation-triangle',
                'Error': 'fas fa-exclamation-circle',
                'TaskAssigned': 'fas fa-tasks',
                'TaskCompleted': 'fas fa-check',
                'ProjectUpdated': 'fas fa-folder',
                'MeetingReminder': 'fas fa-calendar',
                'CommentAdded': 'fas fa-comment'
            };
            return icons[type] || 'fas fa-bell';
        }

        function getNotificationIconBg(type) {
            if (!type) return 'bg-neutral-100 dark:bg-dark-700';
            const backgrounds = {
                'Info': 'bg-info-100 dark:bg-info-900',
                'Success': 'bg-success-100 dark:bg-success-900',
                'Warning': 'bg-warning-100 dark:bg-warning-900',
                'Error': 'bg-danger-100 dark:bg-danger-900',
                'TaskAssigned': 'bg-primary-100 dark:bg-primary-900',
                'TaskCompleted': 'bg-success-100 dark:bg-success-900',
                'ProjectUpdated': 'bg-info-100 dark:bg-info-900',
                'MeetingReminder': 'bg-warning-100 dark:bg-warning-900',
                'CommentAdded': 'bg-primary-100 dark:bg-primary-900'
            };
            return backgrounds[type] || 'bg-neutral-100 dark:bg-dark-700';
        }

        function getNotificationIconColor(type) {
            if (!type) return 'text-neutral-600 dark:text-dark-400';
            const colors = {
                'Info': 'text-info-600 dark:text-info-400',
                'Success': 'text-success-600 dark:text-success-400',
                'Warning': 'text-warning-600 dark:text-warning-400',
                'Error': 'text-danger-600 dark:text-danger-400',
                'TaskAssigned': 'text-primary-600 dark:text-primary-400',
                'TaskCompleted': 'text-success-600 dark:text-success-400',
                'ProjectUpdated': 'text-info-600 dark:text-info-400',
                'MeetingReminder': 'text-warning-600 dark:text-warning-400',
                'CommentAdded': 'text-primary-600 dark:text-primary-400'
            };
            return colors[type] || 'text-neutral-600 dark:text-dark-400';
        }

        function formatNotificationTime(dateString) {
            if (!dateString) return 'Unknown time';
            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return 'Unknown time';

                const now = new Date();
                const diffMs = now - date;
                const diffMins = Math.floor(diffMs / 60000);
                const diffHours = Math.floor(diffMs / 3600000);
                const diffDays = Math.floor(diffMs / 86400000);

                if (diffMins < 1) return 'Just now';
                if (diffMins < 60) return `${diffMins}m ago`;
                if (diffHours < 24) return `${diffHours}h ago`;
                if (diffDays < 7) return `${diffDays}d ago`;
                return date.toLocaleDateString();
            } catch (error) {
                return 'Unknown time';
            }
        }

        // Global Search functionality
        function initializeGlobalSearch() {
            const searchInput = document.getElementById('global-search');
            const searchResults = document.getElementById('search-results');
            let searchTimeout;

            if (!searchInput || !searchResults) return;

            searchInput.addEventListener('input', function(e) {
                const query = e.target.value.trim();

                clearTimeout(searchTimeout);

                if (query.length < 2) {
                    searchResults.classList.add('hidden');
                    return;
                }

                searchTimeout = setTimeout(() => {
                    performGlobalSearch(query);
                }, 300);
            });

            searchInput.addEventListener('focus', function() {
                const query = this.value.trim();
                if (query.length >= 2) {
                    searchResults.classList.remove('hidden');
                }
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    searchInput.focus();
                }

                if (e.key === 'Escape') {
                    searchResults.classList.add('hidden');
                    searchInput.blur();
                }
            });
        }

        async function performGlobalSearch(query) {
            const searchResults = document.getElementById('search-results');
            const loadingEl = document.getElementById('search-loading');
            const noResultsEl = document.getElementById('search-no-results');
            const resultsContainer = document.getElementById('search-results-container');
            const viewAllEl = document.getElementById('search-view-all');

            if (!searchResults) return;

            // Show loading state
            searchResults.classList.remove('hidden');
            if (loadingEl) loadingEl.classList.remove('hidden');
            if (noResultsEl) noResultsEl.classList.add('hidden');
            if (resultsContainer) resultsContainer.innerHTML = '';
            if (viewAllEl) viewAllEl.classList.add('hidden');

            try {
                const response = await fetch(`/api/search?q=${encodeURIComponent(query)}&limit=3`);
                const data = await response.json();



                if (loadingEl) loadingEl.classList.add('hidden');

                if (data.totalResults > 0) {
                    // Pass the nested results structure directly
                    displaySearchResults(data.results);
                    if (viewAllEl) {
                        viewAllEl.classList.remove('hidden');
                        const viewAllLink = viewAllEl.querySelector('a');
                        if (viewAllLink) {
                            viewAllLink.href = `/search?q=${encodeURIComponent(query)}`;
                        }
                    }
                } else {
                    if (noResultsEl) noResultsEl.classList.remove('hidden');
                }
            } catch (error) {
                console.error('Error performing search:', error);
                if (loadingEl) loadingEl.classList.add('hidden');
                if (noResultsEl) noResultsEl.classList.remove('hidden');
            }
        }

        function displaySearchResults(results) {
            const resultsContainer = document.getElementById('search-results-container');
            if (!resultsContainer) return;

            let html = '';

            // Projects
            if (results.projects && results.projects.length > 0) {
                html += '<div class="px-3 py-2 text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide border-b border-neutral-200 dark:border-dark-200">Projects</div>';
                results.projects.forEach(item => {
                    html += createSearchResultItem(item);
                });
            }

            // Tasks
            if (results.tasks && results.tasks.length > 0) {
                html += '<div class="px-3 py-2 text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide border-b border-neutral-200 dark:border-dark-200">Tasks</div>';
                results.tasks.forEach(item => {
                    html += createSearchResultItem(item);
                });
            }

            // People
            if (results.people && results.people.length > 0) {
                html += '<div class="px-3 py-2 text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide border-b border-neutral-200 dark:border-dark-200">People</div>';
                results.people.forEach(item => {
                    html += createSearchResultItem(item);
                });
            }

            // Meetings
            if (results.meetings && results.meetings.length > 0) {
                html += '<div class="px-3 py-2 text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide border-b border-neutral-200 dark:border-dark-200">Meetings</div>';
                results.meetings.forEach(item => {
                    html += createSearchResultItem(item);
                });
            }

            // Requirements
            if (results.requirements && results.requirements.length > 0) {
                html += '<div class="px-3 py-2 text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide border-b border-neutral-200 dark:border-dark-200">Requirements</div>';
                results.requirements.forEach(item => {
                    html += createSearchResultItem(item);
                });
            }

            resultsContainer.innerHTML = html;
        }

        function createSearchResultItem(item) {
            return `
                <a href="${item.url || '#'}" class="flex items-center px-4 py-3 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors">
                    <div class="flex-shrink-0 w-8 h-8 rounded-lg bg-neutral-100 dark:bg-dark-700 flex items-center justify-center mr-3">
                        <i class="${item.icon || 'fas fa-file'} text-neutral-600 dark:text-dark-400 text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-neutral-900 dark:text-white truncate">${item.title || 'Untitled'}</p>
                        <p class="text-xs text-neutral-500 dark:text-dark-400 truncate">${item.description || ''}</p>
                        ${item.projectName ? `<p class="text-xs text-neutral-400 dark:text-dark-500">${item.projectName}</p>` : ''}
                    </div>
                    <div class="flex-shrink-0">
                        <span class="text-xs text-neutral-400 dark:text-dark-500 bg-neutral-100 dark:bg-dark-700 px-2 py-1 rounded">${item.type || 'Item'}</span>
                    </div>
                </a>
            `;
        }

        // Mark all as read button handler
        document.addEventListener('DOMContentLoaded', function() {
            const markAllReadBtn = document.getElementById('mark-all-read');
            if (markAllReadBtn) {
                markAllReadBtn.addEventListener('click', markAllNotificationsAsRead);
            }

            // Load initial notification count
            updateNotificationBadge();

            // Initialize global search
            initializeGlobalSearch();
        });
    </script>
</body>
</html>
