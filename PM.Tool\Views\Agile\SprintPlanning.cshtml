@model PM.Tool.Models.ViewModels.SprintPlanningViewModel
@{
    ViewData["Title"] = "Sprint Planning";
    ViewData["PageTitle"] = $"Sprint Planning - {Model.Project?.Name}";
}

@section Styles {
    <style>
        .sprint-planning-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            height: calc(100vh - 200px);
        }
        
        .planning-column {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-y: auto;
        }
        
        .dark .planning-column {
            background: #1f2937;
            border-color: #374151;
        }
        
        .story-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            cursor: move;
            transition: all 0.2s;
        }
        
        .story-card:hover {
            background: #f3f4f6;
            border-color: #d1d5db;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .dark .story-card {
            background: #374151;
            border-color: #4b5563;
        }
        
        .dark .story-card:hover {
            background: #4b5563;
            border-color: #6b7280;
        }
        
        .story-card.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }
        
        .drop-zone {
            min-height: 100px;
            border: 2px dashed #d1d5db;
            border-radius: 0.375rem;
            padding: 1rem;
            text-align: center;
            color: #6b7280;
            transition: all 0.2s;
        }
        
        .drop-zone.drag-over {
            border-color: #3b82f6;
            background-color: #eff6ff;
            color: #3b82f6;
        }
        
        .capacity-indicator {
            background: linear-gradient(90deg, #10b981 0%, #f59e0b 70%, #ef4444 90%);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .capacity-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s ease;
        }
        
        .story-points {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            font-size: 0.75rem;
            font-weight: 600;
        }
    </style>
}

<!-- Page Header -->
<div class="page-header-compact">
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <i class="fas fa-tasks text-blue-600 dark:text-blue-400"></i>
            </div>
            <div>
                <h1 class="text-xl font-bold text-neutral-900 dark:text-white">Sprint Planning</h1>
                <p class="text-sm text-neutral-500 dark:text-neutral-400">@Model.Project?.Name</p>
            </div>
        </div>
        <div class="flex items-center gap-2">
            <select id="sprintSelector" class="input-compact">
                <option value="">Select Sprint</option>
                @foreach (var sprint in Model.AllSprints)
                {
                    <option value="@sprint.Id" selected="@(sprint.Id == Model.CurrentSprint?.Id)">
                        @sprint.Name (@sprint.Status)
                    </option>
                }
            </select>
            <a href="@Url.Action("Kanban", "Agile", new { projectId = Model.ProjectId })" class="btn-compact btn-compact-secondary">
                <i class="fas fa-columns"></i> Kanban Board
            </a>
            <a href="@Url.Action("Burndown", "Agile", new { projectId = Model.ProjectId })" class="btn-compact btn-compact-secondary">
                <i class="fas fa-chart-line"></i> Burndown Charts
            </a>
            <a href="@Url.Action("CreateSprint", new { projectId = Model.ProjectId })" class="btn-compact btn-compact-primary">
                <i class="fas fa-plus"></i> New Sprint
            </a>
        </div>
    </div>
</div>

<!-- Sprint Info & Capacity -->
@if (Model.CurrentSprint != null)
{
    <div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <h3 class="font-semibold text-neutral-900 dark:text-white">@Model.CurrentSprint.Name</h3>
                <p class="text-sm text-neutral-500 dark:text-neutral-400">@Model.CurrentSprint.StartDate.ToString("MMM dd") - @Model.CurrentSprint.EndDate.ToString("MMM dd, yyyy")</p>
            </div>
            <div>
                <p class="text-sm text-neutral-500 dark:text-neutral-400">Capacity</p>
                <p class="font-semibold text-neutral-900 dark:text-white">@Model.CommittedStoryPoints / @Model.TotalCapacity SP</p>
                <div class="capacity-indicator mt-1">
                    <div class="capacity-fill" style="width: @Math.Min(100, (Model.TotalCapacity > 0 ? (Model.CommittedStoryPoints / Model.TotalCapacity * 100) : 0))%"></div>
                </div>
            </div>
            <div>
                <p class="text-sm text-neutral-500 dark:text-neutral-400">Stories</p>
                <p class="font-semibold text-neutral-900 dark:text-white">@Model.TotalStories stories</p>
            </div>
            <div>
                <p class="text-sm text-neutral-500 dark:text-neutral-400">Goal</p>
                <p class="font-semibold text-neutral-900 dark:text-white">@(Model.CurrentSprint.Goal ?? "No goal set")</p>
            </div>
        </div>
    </div>
}

<!-- Sprint Planning Board -->
<div class="sprint-planning-container">
    <!-- Product Backlog -->
    <div class="planning-column">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-neutral-900 dark:text-white">
                <i class="fas fa-list-ul mr-2"></i>Product Backlog
            </h2>
            <span class="text-sm text-neutral-500 dark:text-neutral-400">@Model.BacklogStories.Count stories</span>
        </div>
        
        <div id="backlog-stories" class="space-y-2">
            @if (Model.BacklogStories.Any())
            {
                @foreach (var story in Model.BacklogStories.OrderBy(s => s.BacklogOrder))
                {
                    <div class="story-card" data-story-id="@story.Id" data-story-points="@story.StoryPoints">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-neutral-900 dark:text-white text-sm">@story.Title</h4>
                                <p class="text-xs text-neutral-500 dark:text-neutral-400 mt-1"><EMAIL></p>
                                @if (!string.IsNullOrEmpty(story.Description))
                                {
                                    <p class="text-xs text-neutral-600 dark:text-neutral-300 mt-1 line-clamp-2">@story.Description</p>
                                }
                            </div>
                            <div class="flex items-center gap-2 ml-2">
                                @if (story.StoryPoints > 0)
                                {
                                    <span class="story-points">@story.StoryPoints</span>
                                }
                                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                    @story.Priority
                                </span>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="drop-zone">
                    <i class="fas fa-inbox text-2xl mb-2"></i>
                    <p>No stories in backlog</p>
                </div>
            }
        </div>
    </div>

    <!-- Sprint Backlog -->
    <div class="planning-column">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-neutral-900 dark:text-white">
                <i class="fas fa-rocket mr-2"></i>Sprint Backlog
            </h2>
            <span class="text-sm text-neutral-500 dark:text-neutral-400">@Model.SprintStories.Count stories</span>
        </div>
        
        @if (Model.CurrentSprint == null)
        {
            <div class="drop-zone">
                <i class="fas fa-plus-circle text-2xl mb-2"></i>
                <p>Select or create a sprint to start planning</p>
            </div>
        }
        else
        {
            <div id="sprint-stories" class="space-y-2">
                @if (Model.SprintStories.Any())
                {
                    @foreach (var story in Model.SprintStories.OrderBy(s => s.BacklogOrder))
                    {
                        <div class="story-card" data-story-id="@story.Id" data-story-points="@story.StoryPoints">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h4 class="font-medium text-neutral-900 dark:text-white text-sm">@story.Title</h4>
                                    <p class="text-xs text-neutral-500 dark:text-neutral-400 mt-1"><EMAIL></p>
                                    @if (!string.IsNullOrEmpty(story.Description))
                                    {
                                        <p class="text-xs text-neutral-600 dark:text-neutral-300 mt-1 line-clamp-2">@story.Description</p>
                                    }
                                </div>
                                <div class="flex items-center gap-2 ml-2">
                                    @if (story.StoryPoints > 0)
                                    {
                                        <span class="story-points">@story.StoryPoints</span>
                                    }
                                    <span class="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400">
                                        @story.Status
                                    </span>
                                </div>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="drop-zone" id="sprint-drop-zone">
                        <i class="fas fa-hand-paper text-2xl mb-2"></i>
                        <p>Drag stories here to add to sprint</p>
                    </div>
                }
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeSprintPlanning();
        });

        function initializeSprintPlanning() {
            // Initialize sortable for backlog
            const backlogContainer = document.getElementById('backlog-stories');
            const sprintContainer = document.getElementById('sprint-stories');
            
            if (backlogContainer) {
                new Sortable(backlogContainer, {
                    group: 'stories',
                    animation: 150,
                    ghostClass: 'story-card-ghost',
                    chosenClass: 'story-card-chosen',
                    dragClass: 'dragging',
                    onEnd: function(evt) {
                        handleStoryMove(evt);
                    }
                });
            }
            
            if (sprintContainer) {
                new Sortable(sprintContainer, {
                    group: 'stories',
                    animation: 150,
                    ghostClass: 'story-card-ghost',
                    chosenClass: 'story-card-chosen',
                    dragClass: 'dragging',
                    onEnd: function(evt) {
                        handleStoryMove(evt);
                    }
                });
            }

            // Sprint selector change handler
            document.getElementById('sprintSelector')?.addEventListener('change', function() {
                const sprintId = this.value;
                if (sprintId) {
                    window.location.href = `@Url.Action("SprintPlanning", new { projectId = Model.ProjectId })?sprintId=${sprintId}`;
                } else {
                    window.location.href = `@Url.Action("SprintPlanning", new { projectId = Model.ProjectId })`;
                }
            });
        }

        function handleStoryMove(evt) {
            const storyId = evt.item.dataset.storyId;
            const storyPoints = parseInt(evt.item.dataset.storyPoints) || 0;
            const fromContainer = evt.from.id;
            const toContainer = evt.to.id;
            
            // Determine if moving to/from sprint
            const isMovingToSprint = toContainer === 'sprint-stories';
            const isMovingFromSprint = fromContainer === 'sprint-stories';
            
            if (isMovingToSprint && !isMovingFromSprint) {
                // Adding to sprint
                addStoryToSprint(storyId);
            } else if (isMovingFromSprint && !isMovingToSprint) {
                // Removing from sprint
                removeStoryFromSprint(storyId);
            }
            
            // Update capacity indicator
            updateCapacityIndicator();
        }

        function addStoryToSprint(storyId) {
            const sprintId = document.getElementById('sprintSelector').value;
            if (!sprintId) return;
            
            fetch('@Url.Action("AddUserStoryToSprint", "Agile")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                },
                body: JSON.stringify({
                    UserStoryId: parseInt(storyId),
                    SprintId: parseInt(sprintId)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.error('Failed to add story to sprint:', data.message);
                    // Revert the move
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error adding story to sprint:', error);
                location.reload();
            });
        }

        function removeStoryFromSprint(storyId) {
            fetch('@Url.Action("RemoveUserStoryFromSprint", "Agile")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                },
                body: JSON.stringify({
                    UserStoryId: parseInt(storyId)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.error('Failed to remove story from sprint:', data.message);
                    // Revert the move
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error removing story from sprint:', error);
                location.reload();
            });
        }

        function updateCapacityIndicator() {
            // Calculate current story points in sprint
            const sprintStories = document.querySelectorAll('#sprint-stories .story-card');
            let totalPoints = 0;
            
            sprintStories.forEach(card => {
                totalPoints += parseInt(card.dataset.storyPoints) || 0;
            });
            
            // Update capacity indicator (this would need the total capacity from the server)
            const capacityFill = document.querySelector('.capacity-fill');
            if (capacityFill) {
                const totalCapacity = @Model.TotalCapacity;
                const percentage = totalCapacity > 0 ? Math.min(100, (totalPoints / totalCapacity) * 100) : 0;
                capacityFill.style.width = percentage + '%';
            }
        }
    </script>
}
