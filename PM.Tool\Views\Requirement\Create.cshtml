@model RequirementCreateViewModel
@{
    ViewData["Title"] = "Create Requirement";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Requirements", Href = Url.Action("Index", "Requirement"), Icon = "fas fa-clipboard-list" },
        new { Text = "Create Requirement", Href = "", Icon = "fas fa-plus" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-plus mr-3 text-primary-600 dark:text-primary-400"></i>
                Create New Requirement
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Define a new project requirement with detailed specifications
            </p>
        </div>
        @{
            ViewData["Text"] = "Back to Requirements";
            ViewData["Variant"] = "secondary";
            ViewData["Icon"] = "fas fa-arrow-left";
            ViewData["Href"] = Url.Action("Index");
        }
        <partial name="Components/_Button" view-data="ViewData" />
    </div>
</div>

<!-- Form -->
<form asp-action="Create" method="post" id="requirementForm" class="space-y-8">
    <div asp-validation-summary="ModelOnly" class="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-4 text-danger-800 dark:text-danger-200"></div>

    <!-- Basic Information -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Basic Information</h3>
                    <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Enter the requirement's basic details</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                <div class="md:col-span-2">
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.Title).ToString();
                        ViewData["Name"] = "Title";
                        ViewData["Type"] = "text";
                        ViewData["Required"] = true;
                        ViewData["Icon"] = "fas fa-tag";
                        ViewData["Placeholder"] = "Enter requirement title";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Title");
                        ViewData["ContainerClasses"] = "";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Project -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.ProjectId).ToString();
                        ViewData["Name"] = "ProjectId";
                        ViewData["Type"] = "select";
                        ViewData["Required"] = true;
                        ViewData["Icon"] = "fas fa-project-diagram";
                        ViewData["Options"] = "<option value=\"\">Select a project</option>";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("ProjectId");
                        ViewData["UseSelect2"] = true;
                        ViewData["Select2Options"] = "{\"placeholder\": \"Select Project\", \"allowClear\": true, \"theme\": \"tailwind\"}";
                        ViewData["Id"] = "projectSelect";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Requirement ID -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.RequirementId).ToString();
                        ViewData["Name"] = "RequirementId";
                        ViewData["Type"] = "text";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-hashtag";
                        ViewData["Placeholder"] = "REQ-001 (auto-generated if empty)";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("RequirementId");
                        ViewData["HelpText"] = "Leave empty for auto-generation";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Type -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.Type).ToString();
                        ViewData["Name"] = "Type";
                        ViewData["Type"] = "select";
                        ViewData["Required"] = true;
                        ViewData["Icon"] = "fas fa-list";
                        ViewData["Options"] = "<option value=\"Functional\">Functional</option><option value=\"NonFunctional\">Non-Functional</option><option value=\"Technical\">Technical</option><option value=\"Business\">Business</option>";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Type");
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Priority -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.Priority).ToString();
                        ViewData["Name"] = "Priority";
                        ViewData["Type"] = "select";
                        ViewData["Required"] = true;
                        ViewData["Icon"] = "fas fa-exclamation-triangle";
                        ViewData["Options"] = "<option value=\"Low\">Low</option><option value=\"Medium\" selected>Medium</option><option value=\"High\">High</option><option value=\"Critical\">Critical</option>";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Priority");
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Stakeholder User -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.StakeholderUserId).ToString();
                        ViewData["Name"] = "StakeholderUserId";
                        ViewData["Type"] = "select";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-user-tag";
                        ViewData["Options"] = "<option value=\"\">Select stakeholder</option>";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("StakeholderUserId");
                        ViewData["UseSelect2"] = true;
                        ViewData["Select2Options"] = "{\"placeholder\": \"Select Stakeholder\", \"allowClear\": true, \"theme\": \"tailwind\"}";
                        ViewData["Id"] = "stakeholderSelect";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Status -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.Status).ToString();
                        ViewData["Name"] = "Status";
                        ViewData["Type"] = "select";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-flag";
                        ViewData["Options"] = "<option value=\"Draft\" selected>Draft</option><option value=\"UnderReview\">Under Review</option><option value=\"Approved\">Approved</option><option value=\"InProgress\">In Progress</option><option value=\"Completed\">Completed</option>";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Status");
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>
            </div>
        </div>
    </div>

    <!-- Description -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-align-left text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Description</h3>
                    <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Provide detailed requirement information</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Description).ToString();
                ViewData["Name"] = "Description";
                ViewData["Type"] = "textarea";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-align-left";
                ViewData["Placeholder"] = "Provide a detailed description of the requirement...";
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Description");
                ViewData["HelpText"] = "Describe what the requirement should accomplish and any relevant context.";
                ViewData["Rows"] = 6;
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </div>
    </div>

    <!-- Acceptance Criteria -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-square text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Acceptance Criteria</h3>
                    <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Define completion criteria</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.AcceptanceCriteria).ToString();
                ViewData["Name"] = "AcceptanceCriteria";
                ViewData["Type"] = "textarea";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-check-square";
                ViewData["Placeholder"] = "Define the criteria that must be met for this requirement to be considered complete...";
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("AcceptanceCriteria");
                ViewData["HelpText"] = "List specific, measurable criteria that define when this requirement is complete.";
                ViewData["Rows"] = 4;
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </div>
    </div>

    <!-- Business Justification -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-business-time text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Business Justification</h3>
                    <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Explain the business value</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.BusinessJustification).ToString();
                ViewData["Name"] = "BusinessJustification";
                ViewData["Type"] = "textarea";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-business-time";
                ViewData["Placeholder"] = "Explain the business value and rationale for this requirement...";
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("BusinessJustification");
                ViewData["HelpText"] = "Describe why this requirement is important to the business.";
                ViewData["Rows"] = 3;
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </div>
    </div>

    <!-- Assignments -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Assignments</h3>
                    <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Assign team members to this requirement</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Stakeholder -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.StakeholderUserId).ToString();
                        ViewData["Name"] = "StakeholderUserId";
                        ViewData["Type"] = "select";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-user-tie";
                        ViewData["Options"] = "<option value=\"\">Select stakeholder</option>";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("StakeholderUserId");
                        ViewData["UseSelect2"] = true;
                        ViewData["Select2Options"] = "{\"placeholder\": \"Select Stakeholder\", \"allowClear\": true, \"theme\": \"tailwind\"}";
                        ViewData["Id"] = "stakeholderSelect2";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Business Analyst -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.AnalystUserId).ToString();
                        ViewData["Name"] = "AnalystUserId";
                        ViewData["Type"] = "select";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-user-chart";
                        ViewData["Options"] = "<option value=\"\">Select analyst</option>";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("AnalystUserId");
                        ViewData["UseSelect2"] = true;
                        ViewData["Select2Options"] = "{\"placeholder\": \"Select Analyst\", \"allowClear\": true, \"theme\": \"tailwind\"}";
                        ViewData["Id"] = "analystSelect";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Developer -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.DeveloperUserId).ToString();
                        ViewData["Name"] = "DeveloperUserId";
                        ViewData["Type"] = "select";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-code";
                        ViewData["Options"] = "<option value=\"\">Select developer</option>";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("DeveloperUserId");
                        ViewData["UseSelect2"] = true;
                        ViewData["Select2Options"] = "{\"placeholder\": \"Select Developer\", \"allowClear\": true, \"theme\": \"tailwind\"}";
                        ViewData["Id"] = "developerSelect";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Tester -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.TesterUserId).ToString();
                        ViewData["Name"] = "TesterUserId";
                        ViewData["Type"] = "select";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-bug";
                        ViewData["Options"] = "<option value=\"\">Select tester</option>";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("TesterUserId");
                        ViewData["UseSelect2"] = true;
                        ViewData["Select2Options"] = "{\"placeholder\": \"Select Tester\", \"allowClear\": true, \"theme\": \"tailwind\"}";
                        ViewData["Id"] = "testerSelect";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-cog text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Additional Information</h3>
                    <p class="text-sm text-neutral-600 dark:text-dark-400 mt-1">Optional details and relationships</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Estimated Effort -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.EstimatedEffort).ToString();
                        ViewData["Name"] = "EstimatedEffort";
                        ViewData["Type"] = "number";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-clock";
                        ViewData["Placeholder"] = "0";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("EstimatedEffort");
                        ViewData["HelpText"] = "Estimated effort in hours";
                        ViewData["Attributes"] = "step=\"0.5\" min=\"0\"";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Parent Requirement -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.ParentRequirementId).ToString();
                        ViewData["Name"] = "ParentRequirementId";
                        ViewData["Type"] = "select";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-sitemap";
                        ViewData["Options"] = "<option value=\"\">No parent requirement</option>";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("ParentRequirementId");
                        ViewData["UseSelect2"] = true;
                        ViewData["Select2Options"] = "{\"placeholder\": \"Select Parent Requirement\", \"allowClear\": true, \"theme\": \"tailwind\"}";
                        ViewData["Id"] = "parentRequirementSelect";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>

                <!-- Target Date -->
                <div>
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.TargetDate).ToString();
                        ViewData["Name"] = "TargetDate";
                        ViewData["Type"] = "date";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-calendar";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("TargetDate");
                        ViewData["HelpText"] = "Target completion date";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    @{
        ViewData["SubmitText"] = "Create Requirement";
        ViewData["SubmitIcon"] = "fas fa-plus";
        ViewData["CancelUrl"] = Url.Action("Index");
        ViewData["AdditionalButtons"] = "<button type='button' onclick='saveDraft()' class='inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed btn-outline-custom px-4 py-2.5 text-sm rounded-lg'><i class='fas fa-save mr-2'></i><span>Save as Draft</span></button>";
    }
    <partial name="Components/_FormActions" view-data="ViewData" />
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            loadProjects();
            loadUsers();
            loadParentRequirements();
            setupFormValidation();
        });

        function loadProjects() {
            $.get('@Url.Action("GetProjects", "Projects")')
                .done(function(data) {
                    const select = $('#projectSelect');
                    if (data && Array.isArray(data)) {
                        data.forEach(function(project) {
                            const selected = project.id == @(ViewBag.ProjectId ?? 0) ? 'selected' : '';
                            select.append(`<option value="${project.id}" ${selected}>${project.name}</option>`);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function loadUsers() {
            $.get('/api/UserLookup/Users')
                .done(function(data) {
                    if (data && Array.isArray(data)) {
                        const stakeholderSelect = $('#stakeholderSelect');
                        const analystSelect = $('#analystSelect');
                        const developerSelect = $('#developerSelect');
                        const testerSelect = $('#testerSelect');

                        data.forEach(function(user) {
                            const option = `<option value="${user.id}">${user.fullName || user.userName} (${user.email})</option>`;
                            stakeholderSelect.append(option);
                            analystSelect.append(option);
                            developerSelect.append(option);
                            testerSelect.append(option);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load users');
                });
        }

        function loadParentRequirements() {
            const projectId = $('#projectSelect').val();
            if (projectId) {
                $.get('@Url.Action("GetProjectRequirements", "Requirement")', { projectId: projectId })
                    .done(function(data) {
                        const select = $('#parentRequirementSelect');
                        select.empty().append('<option value="">No parent requirement</option>');

                        if (data && Array.isArray(data)) {
                            data.forEach(function(req) {
                                select.append(`<option value="${req.id}">${req.requirementId} - ${req.title}</option>`);
                            });
                        }
                    })
                    .fail(function() {
                        console.error('Failed to load parent requirements');
                    });
            }
        }

        function setupFormValidation() {
            $('#projectSelect').on('change', loadParentRequirements);

            $('#requirementForm').on('submit', function(e) {
                // Additional client-side validation can be added here
            });
        }

        function saveDraft() {
            $('#Status').val('Draft');
            $('#requirementForm').submit();
        }
    </script>
}
