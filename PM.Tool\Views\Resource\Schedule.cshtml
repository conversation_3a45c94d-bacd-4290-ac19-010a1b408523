@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model IEnumerable<PM.Tool.Core.Entities.Resource>
@{
    ViewData["Title"] = "Resource Schedule";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Resource Management", Href = Url.Action("Index", "Resource"), Icon = "fas fa-users-cog" },
        new { Text = "Schedule", Href = "", Icon = "fas fa-calendar" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Standardized Page Header -->
@{
    ViewData["Title"] = "Resource Schedule";
    ViewData["Description"] = "View and manage resource allocations and availability";
    ViewData["Icon"] = "fas fa-calendar";

    ViewData["Actions"] = new[] {
        new { Text = "Add Allocation", Href = "#", Icon = "fas fa-plus", Variant = "primary", OnClick = "openAllocationModal()" },
        new { Text = "Calendar View", Href = "#", Icon = "fas fa-calendar-alt", Variant = "outline", OnClick = "toggleCalendarView()" },
        new { Text = "Back to Resources", Href = Url.Action("Index"), Icon = "fas fa-arrow-left", Variant = "secondary", OnClick = (string?)null }
    };

    ViewData["Stats"] = new[] {
        new { Label = "Total Allocations", Value = "0", Icon = "fas fa-calendar-check", Color = "blue" },
        new { Label = "Available Today", Value = "0", Icon = "fas fa-user-check", Color = "green" },
        new { Label = "Overallocated", Value = "0", Icon = "fas fa-exclamation-triangle", Color = "red" },
        new { Label = "Utilization", Value = "0%", Icon = "fas fa-chart-bar", Color = "purple" }
    };
}
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Calendar Controls -->
@{
    ViewData["Title"] = "Calendar Controls";
    ViewData["Icon"] = "fas fa-cog";
    ViewData["BodyContent"] = @"
        <div class='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div>
                <label for='resourceFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Resource</label>
                <select id='resourceFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Resources</option>
                </select>
            </div>
            <div>
                <label for='typeFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Type</label>
                <select id='typeFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Types</option>
                    <option value='Human'>Human</option>
                    <option value='Equipment'>Equipment</option>
                    <option value='Material'>Material</option>
                    <option value='Facility'>Facility</option>
                </select>
            </div>
            <div>
                <label for='viewType' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>View</label>
                <select id='viewType' class='select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value='dayGridMonth'>Month</option>
                    <option value='timeGridWeek' selected>Week</option>
                    <option value='timeGridDay'>Day</option>
                    <option value='resourceTimeGridWeek'>Resource Week</option>
                </select>
            </div>
            <div class='flex items-end'>
                <button id='todayBtn' class='w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors'>
                    <i class='fas fa-calendar-day mr-2'></i>
                    Today
                </button>
            </div>
        </div>
    ";
}
<div class="mb-6">
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Calendar -->
@{
    ViewData["Title"] = "Resource Calendar";
    ViewData["Icon"] = "fas fa-calendar-alt";
    ViewData["BodyContent"] = @"
        <div id='calendar' class='bg-white dark:bg-dark-800 rounded-lg'></div>
    ";
}
<partial name="Components/_Card" view-data="ViewData" />

<!-- Allocation Modal -->
<div id="allocationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-surface-dark rounded-xl max-w-2xl w-full max-h-96 overflow-y-auto">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-plus mr-2 text-primary-600 dark:text-primary-400"></i>
                    Add Resource Allocation
                </h3>
                <button onclick="closeAllocationModal()" class="text-neutral-400 hover:text-neutral-600 dark:hover:text-dark-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="allocationForm" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="modalResourceId" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Resource *</label>
                        <select id="modalResourceId" name="resourceId" required class="select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Select Resource</option>
                        </select>
                    </div>
                    <div>
                        <label for="modalProjectId" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Project</label>
                        <select id="modalProjectId" name="projectId" class="select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Select Project</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="modalStartDate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Start Date *</label>
                        <input type="date" id="modalStartDate" name="startDate" required class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label for="modalEndDate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">End Date *</label>
                        <input type="date" id="modalEndDate" name="endDate" required class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="modalAllocatedHours" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Allocated Hours *</label>
                        <input type="number" id="modalAllocatedHours" name="allocatedHours" min="0" step="0.5" required class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label for="modalAllocationPercentage" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Allocation %</label>
                        <input type="number" id="modalAllocationPercentage" name="allocationPercentage" min="0" max="100" value="100" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>

                <div>
                    <label for="modalNotes" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Notes</label>
                    <textarea id="modalNotes" name="notes" rows="3" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500" placeholder="Additional notes..."></textarea>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-neutral-200 dark:border-dark-600">
                    <button type="button" onclick="closeAllocationModal()" class="px-4 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg text-sm font-medium text-neutral-700 dark:text-dark-300 bg-white dark:bg-dark-800 hover:bg-neutral-50 dark:hover:bg-dark-700">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg text-sm font-medium">
                        <i class="fas fa-save mr-2"></i>
                        Save Allocation
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <!-- FullCalendar CSS and JS -->
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar-scheduler@6.1.8/index.global.min.js'></script>

    <script>
        let calendar;

        $(document).ready(function() {
            initializeCalendar();
            loadResources();
            loadProjects();
            setupEventHandlers();
        });

        function initializeCalendar() {
            const calendarEl = document.getElementById('calendar');

            calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridWeek',
                headerToolbar: {
                    left: 'prev,next',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay,resourceTimeGridWeek'
                },
                height: 'auto',
                slotMinTime: '08:00:00',
                slotMaxTime: '18:00:00',
                businessHours: {
                    daysOfWeek: [1, 2, 3, 4, 5],
                    startTime: '09:00',
                    endTime: '17:00'
                },
                selectable: true,
                selectMirror: true,
                editable: true,
                dayMaxEvents: true,
                weekends: true,
                events: function(fetchInfo, successCallback, failureCallback) {
                    loadCalendarEvents(fetchInfo, successCallback, failureCallback);
                },
                select: function(selectionInfo) {
                    openAllocationModal(selectionInfo.start, selectionInfo.end);
                },
                eventClick: function(clickInfo) {
                    showAllocationDetails(clickInfo.event);
                },
                eventDrop: function(dropInfo) {
                    updateAllocation(dropInfo.event);
                },
                eventResize: function(resizeInfo) {
                    updateAllocation(resizeInfo.event);
                },
                resources: function(fetchInfo, successCallback, failureCallback) {
                    loadCalendarResources(fetchInfo, successCallback, failureCallback);
                }
            });

            calendar.render();
        }

        function loadCalendarEvents(fetchInfo, successCallback, failureCallback) {
            const resourceFilter = $('#resourceFilter').val();
            const typeFilter = $('#typeFilter').val();

            $.get('@Url.Action("GetCalendarEvents", "Resource")', {
                start: fetchInfo.startStr,
                end: fetchInfo.endStr,
                resourceId: resourceFilter,
                resourceType: typeFilter
            })
            .done(function(data) {
                successCallback(data);
            })
            .fail(function() {
                failureCallback();
                console.error('Failed to load calendar events');
            });
        }

        function loadCalendarResources(fetchInfo, successCallback, failureCallback) {
            const typeFilter = $('#typeFilter').val();

            $.get('@Url.Action("GetCalendarResources", "Resource")', {
                resourceType: typeFilter
            })
            .done(function(data) {
                successCallback(data);
            })
            .fail(function() {
                failureCallback();
                console.error('Failed to load calendar resources');
            });
        }

        function loadResources() {
            $.get('@Url.Action("GetActiveResources", "Resource")')
                .done(function(data) {
                    const resourceSelect = $('#resourceFilter');
                    const modalResourceSelect = $('#modalResourceId');

                    if (data && Array.isArray(data)) {
                        data.forEach(function(resource) {
                            const option = `<option value="${resource.id}">${resource.name} (${resource.type})</option>`;
                            resourceSelect.append(option);
                            modalResourceSelect.append(option);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load resources');
                });
        }

        function loadProjects() {
            $.get('@Url.Action("GetActiveProjects", "Project")')
                .done(function(data) {
                    const select = $('#modalProjectId');
                    if (data && Array.isArray(data)) {
                        data.forEach(function(project) {
                            select.append(`<option value="${project.id}">${project.name}</option>`);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function setupEventHandlers() {
            $('#resourceFilter, #typeFilter').on('change', function() {
                calendar.refetchEvents();
                if ($('#typeFilter').val() && calendar.getOption('initialView') === 'resourceTimeGridWeek') {
                    calendar.refetchResources();
                }
            });

            $('#viewType').on('change', function() {
                calendar.changeView($(this).val());
            });

            $('#todayBtn').on('click', function() {
                calendar.today();
            });

            $('#allocationForm').on('submit', function(e) {
                e.preventDefault();
                saveAllocation();
            });
        }

        function openAllocationModal(start = null, end = null) {
            if (start) {
                $('#modalStartDate').val(start.toISOString().split('T')[0]);
            }
            if (end) {
                $('#modalEndDate').val(end.toISOString().split('T')[0]);
            }

            $('#allocationModal').removeClass('hidden');
        }

        function closeAllocationModal() {
            $('#allocationModal').addClass('hidden');
            $('#allocationForm')[0].reset();
        }

        function saveAllocation() {
            const formData = {
                resourceId: $('#modalResourceId').val(),
                projectId: $('#modalProjectId').val() || null,
                startDate: $('#modalStartDate').val(),
                endDate: $('#modalEndDate').val(),
                allocatedHours: $('#modalAllocatedHours').val(),
                allocationPercentage: $('#modalAllocationPercentage').val(),
                notes: $('#modalNotes').val(),
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            };

            $.post('@Url.Action("CreateAllocation", "Resource")', formData)
                .done(function(response) {
                    if (response.success) {
                        closeAllocationModal();
                        calendar.refetchEvents();
                        // Show success message
                    } else {
                        alert(response.message || 'Failed to create allocation');
                    }
                })
                .fail(function() {
                    alert('Failed to create allocation. Please try again.');
                });
        }

        function showAllocationDetails(event) {
            // Implementation for showing allocation details
            alert(`Allocation Details:\nResource: ${event.title}\nStart: ${event.start}\nEnd: ${event.end}`);
        }

        function updateAllocation(event) {
            const allocationData = {
                id: event.id,
                startDate: event.start.toISOString(),
                endDate: event.end.toISOString(),
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            };

            $.post('@Url.Action("UpdateAllocation", "Resource")', allocationData)
                .done(function(response) {
                    if (!response.success) {
                        alert(response.message || 'Failed to update allocation');
                        calendar.refetchEvents(); // Revert changes
                    }
                })
                .fail(function() {
                    alert('Failed to update allocation. Please try again.');
                    calendar.refetchEvents(); // Revert changes
                });
        }
    </script>
}
