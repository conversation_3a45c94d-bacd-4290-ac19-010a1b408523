using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Application.Services;
using PM.Tool.Core.Entities;
using PM.Tool.Data;
using PM.Tool.Extensions;
using PM.Tool.Infrastructure.Interceptors;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class EncryptionDemoController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IDataEncryptionService _encryptionService;
        private readonly ILogger<EncryptionDemoController> _logger;

        public EncryptionDemoController(
            ApplicationDbContext context,
            IDataEncryptionService encryptionService,
            ILogger<EncryptionDemoController> logger)
        {
            _context = context;
            _encryptionService = encryptionService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                // Get some sample encrypted data
                var users = await _context.Users
                    .Take(5)
                    .ToListAsync();

                // Decrypt the data for display
                foreach (var user in users)
                {
                    user.DecryptEntity(_encryptionService);
                }

                var comments = await _context.TaskComments
                    .Include(c => c.Task)
                    .Take(5)
                    .ToListAsync();

                foreach (var comment in comments)
                {
                    comment.DecryptEntity(_encryptionService);
                }

                var people = await _context.People
                    .Take(5)
                    .ToListAsync();

                foreach (var person in people)
                {
                    person.DecryptEntity(_encryptionService);
                }

                ViewBag.Users = users;
                ViewBag.Comments = comments;
                ViewBag.People = people;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading encryption demo data");
                TempData["Error"] = "Error loading demo data: " + ex.Message;
                return View();
            }
        }

        [HttpPost]
        public async Task<IActionResult> TestEncryption(string testData)
        {
            try
            {
                if (string.IsNullOrEmpty(testData))
                {
                    TempData["Error"] = "Please provide test data to encrypt";
                    return RedirectToAction(nameof(Index));
                }

                // Test string encryption
                var encrypted = _encryptionService.EncryptString(testData);
                var decrypted = _encryptionService.DecryptString(encrypted);

                // Test hash generation
                var hash = _encryptionService.HashForSearch(testData);
                var hashVerified = _encryptionService.VerifyHash(testData, hash);

                // Test object encryption
                var testObject = new { Message = testData, Timestamp = DateTime.Now };
                var encryptedObject = _encryptionService.EncryptObject(testObject);
                var decryptedObject = _encryptionService.DecryptObject<dynamic>(encryptedObject);

                ViewBag.TestResults = new
                {
                    OriginalData = testData,
                    EncryptedData = encrypted,
                    DecryptedData = decrypted,
                    Hash = hash,
                    HashVerified = hashVerified,
                    EncryptedObject = encryptedObject,
                    DecryptedObject = decryptedObject,
                    EncryptionWorking = testData == decrypted
                };

                TempData["Success"] = "Encryption test completed successfully!";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing encryption");
                TempData["Error"] = "Encryption test failed: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateTestPerson(string firstName, string lastName, string email, string bio)
        {
            try
            {
                if (string.IsNullOrEmpty(firstName) || string.IsNullOrEmpty(lastName) || string.IsNullOrEmpty(email))
                {
                    TempData["Error"] = "Please provide first name, last name, and email";
                    return RedirectToAction(nameof(Index));
                }

                var person = new Person
                {
                    FirstName = firstName,
                    LastName = lastName,
                    Email = email,
                    Bio = bio,
                    Type = PersonType.External,
                    CreatedAt = DateTime.UtcNow
                };

                // The encryption will happen automatically via the interceptor
                _context.People.Add(person);
                await _context.SaveChangesAsync();

                TempData["Success"] = $"Test person '{firstName} {lastName}' created successfully with automatic encryption!";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating test person");
                TempData["Error"] = "Failed to create test person: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        public async Task<IActionResult> SearchEncryptedData(string searchTerm)
        {
            try
            {
                if (string.IsNullOrEmpty(searchTerm))
                {
                    TempData["Error"] = "Please provide a search term";
                    return RedirectToAction(nameof(Index));
                }

                // Generate hash for searching encrypted data
                var searchHash = _encryptionService.HashForSearch(searchTerm);

                // Search by hash (this is how we can search encrypted data)
                var matchingPeople = await _context.People
                    .Where(p => p.FirstNameHash == searchHash || 
                               p.LastNameHash == searchHash || 
                               p.EmailHash == searchHash)
                    .ToListAsync();

                // Decrypt results for display
                foreach (var person in matchingPeople)
                {
                    person.DecryptEntity(_encryptionService);
                }

                ViewBag.SearchResults = matchingPeople;
                ViewBag.SearchTerm = searchTerm;
                ViewBag.SearchHash = searchHash;

                TempData["Info"] = $"Found {matchingPeople.Count} matching records for '{searchTerm}'";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching encrypted data");
                TempData["Error"] = "Search failed: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpGet]
        public async Task<IActionResult> ViewRawData(int personId)
        {
            try
            {
                // Get raw encrypted data from database
                var person = await _context.People
                    .AsNoTracking()
                    .FirstOrDefaultAsync(p => p.Id == personId);

                if (person == null)
                {
                    TempData["Error"] = "Person not found";
                    return RedirectToAction(nameof(Index));
                }

                // Show both encrypted and decrypted versions
                var decryptedPerson = await _context.People
                    .FirstOrDefaultAsync(p => p.Id == personId);

                if (decryptedPerson != null)
                {
                    decryptedPerson.DecryptEntity(_encryptionService);
                }

                ViewBag.EncryptedPerson = person;
                ViewBag.DecryptedPerson = decryptedPerson;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error viewing raw data");
                TempData["Error"] = "Failed to view raw data: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        public async Task<IActionResult> TestFileEncryption(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    TempData["Error"] = "Please select a file to encrypt";
                    return RedirectToAction(nameof(Index));
                }

                using var memoryStream = new MemoryStream();
                await file.CopyToAsync(memoryStream);
                var originalBytes = memoryStream.ToArray();

                // Encrypt the file data
                var encryptedBytes = _encryptionService.EncryptBytes(originalBytes);
                var decryptedBytes = _encryptionService.DecryptBytes(encryptedBytes);

                // Verify the encryption/decryption worked
                var isValid = originalBytes.SequenceEqual(decryptedBytes);

                ViewBag.FileTestResults = new
                {
                    FileName = file.FileName,
                    OriginalSize = originalBytes.Length,
                    EncryptedSize = encryptedBytes.Length,
                    DecryptedSize = decryptedBytes.Length,
                    IsValid = isValid,
                    CompressionRatio = (double)encryptedBytes.Length / originalBytes.Length
                };

                TempData["Success"] = $"File encryption test completed! Encryption working: {isValid}";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing file encryption");
                TempData["Error"] = "File encryption test failed: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        public IActionResult GenerateSecureKey(int length = 32)
        {
            try
            {
                var key = _encryptionService.GenerateSecureKey(length);
                ViewBag.GeneratedKey = key;
                TempData["Success"] = $"Generated secure key of length {length}";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating secure key");
                TempData["Error"] = "Key generation failed: " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }
    }
}
