@using Microsoft.AspNetCore.Identity
@using PM.Tool.Core.Entities
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

<!-- Topbar -->
<header class="fixed top-0 right-0 left-0 lg:left-64 z-30 bg-white dark:bg-surface-dark border-b border-neutral-200 dark:border-dark-200 h-16">
    <div class="flex items-center justify-between h-full px-6">
        <!-- Left Section -->
        <div class="flex items-center space-x-4">
            <!-- Mobile Menu Button -->
            <button id="sidebar-toggle" class="lg:hidden p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                <i class="fas fa-bars text-neutral-600 dark:text-dark-300"></i>
            </button>

            <!-- Page Title -->
            <div class="hidden sm:block">
                <h1 class="text-xl font-semibold text-neutral-900 dark:text-dark-100">
                    @ViewData["Title"]
                </h1>
                @if (ViewData["Subtitle"] != null)
                {
                    <p class="text-sm text-neutral-500 dark:text-dark-400">@ViewData["Subtitle"]</p>
                }
            </div>
        </div>

        <!-- Center Section - Search -->
        <div class="hidden md:flex flex-1 max-w-lg mx-8">
            <div class="relative w-full">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-neutral-400 dark:text-dark-500"></i>
                </div>
                <input type="text"
                       id="global-search"
                       class="block w-full pl-10 pr-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg bg-white dark:bg-surface-dark text-neutral-900 dark:text-dark-100 placeholder-neutral-500 dark:placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                       placeholder="Search projects, tasks, or documents..."
                       autocomplete="off">

                <!-- Search Results Dropdown -->
                <div id="search-results" class="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-surface-dark rounded-lg shadow-strong border border-neutral-200 dark:border-dark-200 py-2 hidden z-50 max-h-96 overflow-y-auto">
                    <!-- Loading State -->
                    <div id="search-loading" class="px-4 py-3 text-center text-neutral-500 dark:text-dark-400 hidden">
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        Searching...
                    </div>

                    <!-- No Results -->
                    <div id="search-no-results" class="px-4 py-3 text-center text-neutral-500 dark:text-dark-400 hidden">
                        <i class="fas fa-search mr-2"></i>
                        No results found
                    </div>

                    <!-- Results Container -->
                    <div id="search-results-container">
                        <!-- Results will be populated here -->
                    </div>

                    <!-- View All Results -->
                    <div id="search-view-all" class="border-t border-neutral-200 dark:border-dark-200 p-3 hidden">
                        <a href="#" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                            View all results
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Section -->
        <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <div class="relative">
                <button id="notifications-toggle" class="p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors relative">
                    <i class="fas fa-bell text-neutral-600 dark:text-dark-300"></i>
                    <!-- Notification Badge -->
                    <span id="notification-badge" class="absolute -top-1 -right-1 w-3 h-3 bg-danger-500 rounded-full text-xs text-white font-bold flex items-center justify-center min-w-[12px] h-3 hidden"></span>
                </button>

                <!-- Notifications Dropdown -->
                <div id="notifications-menu" class="absolute right-0 mt-2 w-80 bg-white dark:bg-surface-dark rounded-lg shadow-strong border border-neutral-200 dark:border-dark-200 py-2 hidden z-50 max-h-96 overflow-hidden">
                    <!-- Header -->
                    <div class="px-4 py-3 border-b border-neutral-200 dark:border-dark-200 flex items-center justify-between">
                        <h3 class="text-sm font-semibold text-neutral-900 dark:text-white">Notifications</h3>
                        <button id="mark-all-read" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                            Mark all as read
                        </button>
                    </div>

                    <!-- Loading State -->
                    <div id="notifications-loading" class="px-4 py-6 text-center text-neutral-500 dark:text-dark-400">
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        Loading notifications...
                    </div>

                    <!-- No Notifications -->
                    <div id="notifications-empty" class="px-4 py-6 text-center text-neutral-500 dark:text-dark-400 hidden">
                        <i class="fas fa-bell-slash text-2xl mb-2"></i>
                        <p class="text-sm">No notifications</p>
                    </div>

                    <!-- Notifications List -->
                    <div id="notifications-list" class="max-h-80 overflow-y-auto">
                        <!-- Notifications will be populated here -->
                    </div>

                    <!-- Footer -->
                    <div class="border-t border-neutral-200 dark:border-dark-200 p-3">
                        <a href="#" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                            View all notifications
                        </a>
                    </div>
                </div>
            </div>

            <!-- Language Selector -->
            <div class="relative">
                <button id="language-toggle"
                        class="p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors"
                        title="Change language">
                    <i class="fas fa-globe text-neutral-600 dark:text-dark-300"></i>
                    <span class="sr-only">Current: @System.Globalization.CultureInfo.CurrentCulture.DisplayName</span>
                </button>

                <!-- Language Dropdown -->
                <div id="language-menu"
                     class="absolute right-0 mt-2 w-64 bg-white dark:bg-surface-dark rounded-lg shadow-strong border border-neutral-200 dark:border-dark-200 py-2 hidden max-h-80 overflow-y-auto">

                    <!-- Current Language -->
                    <div class="px-3 py-2 text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide border-b border-neutral-200 dark:border-dark-200">
                        Current: @System.Globalization.CultureInfo.CurrentCulture.DisplayName
                    </div>

                    <!-- Popular Languages -->
                    <div class="px-3 py-2 text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide border-b border-neutral-200 dark:border-dark-200 mt-2">
                        Popular Languages
                    </div>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "en-US", returnUrl = Context.Request.Path })"
                       class="flex items-center justify-between px-4 py-3 text-sm font-medium text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors @(System.Globalization.CultureInfo.CurrentCulture.Name == "en-US" ? "bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300" : "")">
                        <div class="flex items-center gap-3">
                            <span class="text-lg leading-none">🇺🇸</span>
                            <span class="text-neutral-700 dark:text-white">English (US)</span>
                        </div>
                        @if (System.Globalization.CultureInfo.CurrentCulture.Name == "en-US")
                        {
                            <i class="fas fa-check text-primary-600 dark:text-primary-400 text-sm"></i>
                        }
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "es-ES", returnUrl = Context.Request.Path })"
                       class="flex items-center justify-between px-4 py-3 text-sm font-medium text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors @(System.Globalization.CultureInfo.CurrentCulture.Name == "es-ES" ? "bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300" : "")">
                        <div class="flex items-center gap-3">
                            <span class="text-lg leading-none">🇪🇸</span>
                            <span class="text-neutral-700 dark:text-white">Español (España)</span>
                        </div>
                        @if (System.Globalization.CultureInfo.CurrentCulture.Name == "es-ES")
                        {
                            <i class="fas fa-check text-primary-600 dark:text-primary-400 text-sm"></i>
                        }
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "fr-FR", returnUrl = Context.Request.Path })"
                       class="flex items-center justify-between px-4 py-3 text-sm font-medium text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors @(System.Globalization.CultureInfo.CurrentCulture.Name == "fr-FR" ? "bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300" : "")">
                        <div class="flex items-center gap-3">
                            <span class="text-lg leading-none">🇫🇷</span>
                            <span class="text-neutral-700 dark:text-white">Français (France)</span>
                        </div>
                        @if (System.Globalization.CultureInfo.CurrentCulture.Name == "fr-FR")
                        {
                            <i class="fas fa-check text-primary-600 dark:text-primary-400 text-sm"></i>
                        }
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "de-DE", returnUrl = Context.Request.Path })"
                       class="flex items-center justify-between px-4 py-3 text-sm font-medium text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors @(System.Globalization.CultureInfo.CurrentCulture.Name == "de-DE" ? "bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300" : "")">
                        <div class="flex items-center gap-3">
                            <span class="text-lg leading-none">🇩🇪</span>
                            <span class="text-neutral-700 dark:text-white">Deutsch (Deutschland)</span>
                        </div>
                        @if (System.Globalization.CultureInfo.CurrentCulture.Name == "de-DE")
                        {
                            <i class="fas fa-check text-primary-600 dark:text-primary-400 text-sm"></i>
                        }
                    </a>

                    <!-- European Languages -->
                    <div class="px-3 py-2 text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide border-b border-neutral-200 dark:border-dark-200 mt-2">
                        European Languages
                    </div>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "en-GB", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇬🇧</span>
                        English (UK)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "it-IT", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇮🇹</span>
                        Italiano (Italia)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "pt-BR", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇧🇷</span>
                        Português (Brasil)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "nl-NL", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇳🇱</span>
                        Nederlands (Nederland)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "ru-RU", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇷🇺</span>
                        Русский (Россия)
                    </a>

                    <!-- Asian Languages -->
                    <div class="px-3 py-2 text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide border-b border-neutral-200 dark:border-dark-200 mt-2">
                        Asian Languages
                    </div>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "zh-CN", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇨🇳</span>
                        中文 (简体)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "ja-JP", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇯🇵</span>
                        日本語 (日本)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "ko-KR", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇰🇷</span>
                        한국어 (대한민국)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "hi-IN", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇮🇳</span>
                        हिन्दी (भारत)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "bn-BD", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇧🇩</span>
                        বাংলা (বাংলাদেশ)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "th-TH", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇹🇭</span>
                        ไทย (ประเทศไทย)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "vi-VN", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇻🇳</span>
                        Tiếng Việt (Việt Nam)
                    </a>

                    <!-- Middle Eastern Languages -->
                    <div class="px-3 py-2 text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide border-b border-neutral-200 dark:border-dark-200 mt-2">
                        Middle Eastern Languages
                    </div>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "ar-SA", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇸🇦</span>
                        العربية (السعودية)
                    </a>

                    <a href="@Url.Action("SetLanguage", "Home", new { culture = "he-IL", returnUrl = Context.Request.Path })"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <span class="w-6 h-4 mr-3 text-lg">🇮🇱</span>
                        עברית (ישראל)
                    </a>
                </div>
            </div>

            <!-- Theme Toggle -->
            <button id="theme-toggle"
                    class="p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors"
                    title="Toggle theme">
                <i id="theme-icon" class="fas fa-moon text-neutral-600 dark:text-dark-300"></i>
            </button>

            <!-- Quick Actions -->
            <div class="relative">
                <button id="quick-actions-toggle"
                        class="p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                    <i class="fas fa-plus text-neutral-600 dark:text-dark-300"></i>
                </button>

                <!-- Quick Actions Dropdown -->
                <div id="quick-actions-menu"
                     class="absolute right-0 mt-2 w-56 bg-white dark:bg-surface-dark rounded-lg shadow-strong border border-neutral-200 dark:border-dark-200 py-2 hidden">
                    @if (SignInManager.IsSignedIn(User) && (User.IsInRole("Admin") || User.IsInRole("Manager")))
                    {
                        <a href="@Url.Action("Create", "Projects")"
                           class="flex items-center px-4 py-3 text-sm font-medium text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors">
                            <i class="fas fa-folder-plus w-4 h-4 mr-3 text-primary-500 dark:text-primary-400"></i>
                            <span class="text-neutral-700 dark:text-white">New Project</span>
                        </a>
                    }

                    <a href="@Url.Action("Create", "Tasks")"
                       class="flex items-center px-4 py-3 text-sm font-medium text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-plus-square w-4 h-4 mr-3 text-success-500 dark:text-success-400"></i>
                        <span class="text-neutral-700 dark:text-white">New Task</span>
                    </a>

                    <a href="@Url.Action("Create", "Meeting")"
                       class="flex items-center px-4 py-3 text-sm font-medium text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-video w-4 h-4 mr-3 text-info-500 dark:text-info-400"></i>
                        <span class="text-neutral-700 dark:text-white">Schedule Meeting</span>
                    </a>

                    <div class="border-t border-neutral-200 dark:border-dark-200 my-2"></div>

                    <a href="@Url.Action("Create", "Document")"
                       class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <i class="fas fa-file-plus w-4 h-4 mr-3 text-warning-500"></i>
                        New Document
                    </a>
                </div>
            </div>

            <!-- User Menu -->
            @if (SignInManager.IsSignedIn(User))
            {
                <div class="relative">
                    <button id="user-menu-toggle"
                            class="flex items-center space-x-3 p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div class="hidden sm:block text-left">
                            <p class="text-sm font-medium text-neutral-900 dark:text-dark-100">
                                @(User.Identity?.Name ?? "User")
                            </p>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">
                                @if (User.IsInRole("Admin"))
                                {
                                    <span>Administrator</span>
                                }
                                else if (User.IsInRole("Manager"))
                                {
                                    <span>Project Manager</span>
                                }
                                else
                                {
                                    <span>Team Member</span>
                                }
                            </p>
                        </div>
                        <i class="fas fa-chevron-down text-neutral-400 dark:text-dark-500 text-xs"></i>
                    </button>

                    <!-- User Dropdown -->
                    <div id="user-menu"
                         class="absolute right-0 mt-2 w-56 bg-white dark:bg-surface-dark rounded-lg shadow-strong border border-neutral-200 dark:border-dark-200 py-2 hidden">
                        <div class="px-4 py-3 border-b border-neutral-200 dark:border-gray-600">
                            <p class="text-sm font-medium text-neutral-900 dark:text-white">
                                @(User.Identity?.Name ?? "User")
                            </p>
                            <p class="text-xs text-neutral-500 dark:text-gray-300">
                                @(User.FindFirst(System.Security.Claims.ClaimTypes.Email)?.Value ?? "")
                            </p>
                        </div>

                        <a href="@Url.Action("Index", "Manage", new { area = "Identity" })"
                           class="flex items-center px-4 py-3 text-sm font-medium text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors">
                            <i class="fas fa-user-cog w-4 h-4 mr-3 text-neutral-500 dark:text-gray-300"></i>
                            <span class="text-neutral-700 dark:text-white">Profile Settings</span>
                        </a>

                        <a href="#"
                           class="flex items-center px-4 py-3 text-sm font-medium text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors">
                            <i class="fas fa-bell w-4 h-4 mr-3 text-neutral-500 dark:text-gray-300"></i>
                            <span class="text-neutral-700 dark:text-white">Notifications</span>
                        </a>

                        <a href="#"
                           class="flex items-center px-4 py-3 text-sm font-medium text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors">
                            <i class="fas fa-question-circle w-4 h-4 mr-3 text-neutral-500 dark:text-gray-300"></i>
                            <span class="text-neutral-700 dark:text-white">Help & Support</span>
                        </a>

                        <div class="border-t border-neutral-200 dark:border-dark-200 my-2"></div>

                        <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home")" method="post" class="block">
                            <button type="submit"
                                    class="flex items-center w-full px-4 py-2 text-sm text-danger-600 dark:text-danger-400 hover:bg-danger-50 dark:hover:bg-danger-900/20 transition-colors">
                                <i class="fas fa-sign-out-alt w-4 h-4 mr-3"></i>
                                Sign Out
                            </button>
                        </form>
                    </div>
                </div>
            }
            else
            {
                <div class="flex items-center space-x-3">
                    <a href="@Url.Action("Login", "Account", new { area = "Identity" })"
                       class="btn-outline-custom">
                        Sign In
                    </a>
                    <a href="@Url.Action("Register", "Account", new { area = "Identity" })"
                       class="btn-primary-custom">
                        Sign Up
                    </a>
                </div>
            }
        </div>
    </div>
</header>
