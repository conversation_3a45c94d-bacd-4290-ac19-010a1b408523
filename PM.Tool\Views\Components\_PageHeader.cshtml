@{
    var pageTitle = ViewData["PageTitle"]?.ToString() ?? "";
    var pageSubtitle = ViewData["PageSubtitle"]?.ToString() ?? "";
    var pageIcon = ViewData["PageIcon"]?.ToString() ?? "";
    var breadcrumbs = ViewData["Breadcrumbs"] as IEnumerable<object> ?? new List<object>();
    var headerActions = ViewData["HeaderActions"] as IEnumerable<object> ?? new List<object>();
    var headerBadges = ViewData["HeaderBadges"] as IEnumerable<object> ?? new List<object>();
}

<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="flex-1 min-w-0">
            <!-- Breadcrumbs -->
            @if (breadcrumbs.Any())
            {
                <nav class="flex mb-4" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        @foreach (dynamic crumb in breadcrumbs)
                        {
                            <li class="@(crumb.IsActive == true ? "" : "inline-flex items-center")">
                                @if (crumb.IsActive == true)
                                {
                                    <div class="flex items-center">
                                        <i class="fas fa-chevron-right text-neutral-400 dark:text-neutral-500 mx-2"></i>
                                        <span class="text-neutral-700 dark:text-neutral-300">@crumb.Text</span>
                                    </div>
                                }
                                else
                                {
                                    @if (breadcrumbs.ToList().IndexOf(crumb) > 0)
                                    {
                                        <i class="fas fa-chevron-right text-neutral-400 dark:text-neutral-500 mx-2"></i>
                                    }
                                    <a href="@crumb.Href" class="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 flex items-center">
                                        @if (!string.IsNullOrEmpty(crumb.Icon))
                                        {
                                            <i class="@crumb.Icon mr-2"></i>
                                        }
                                        @crumb.Text
                                    </a>
                                }
                            </li>
                        }
                    </ol>
                </nav>
            }

            <!-- Page Title and Badges -->
            <div class="flex items-center flex-wrap gap-3 mb-2">
                @if (!string.IsNullOrEmpty(pageIcon))
                {
                    <i class="@pageIcon text-neutral-600 dark:text-neutral-400"></i>
                }
                <h1 class="text-2xl font-bold text-neutral-900 dark:text-neutral-100">@pageTitle</h1>
                
                @foreach (dynamic badge in headerBadges)
                {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @badge.Classes">
                        @badge.Text
                    </span>
                }
            </div>

            <!-- Page Subtitle -->
            @if (!string.IsNullOrEmpty(pageSubtitle))
            {
                <p class="text-sm text-neutral-500 dark:text-neutral-400">@pageSubtitle</p>
            }
        </div>

        <!-- Header Actions -->
        @if (headerActions.Any())
        {
            <div class="mt-4 sm:mt-0 flex flex-wrap gap-3">
                @foreach (dynamic action in headerActions)
                {
                    ViewData["Text"] = action.Text;
                    ViewData["Variant"] = action.Variant;
                    ViewData["Icon"] = action.Icon;
                    ViewData["Href"] = action.Href;
                    ViewData["Size"] = action.Size ?? "default";
                    <partial name="Components/_Button" view-data="ViewData" />
                }
            </div>
        }
    </div>
</div>
