﻿@model PM.Tool.Models.ViewModels.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
    ViewData["Description"] = "Welcome back! Here's what's happening with your projects.";
    ViewData["Icon"] = "fas fa-tachometer-alt";
    ViewData["IconColor"] = "text-primary-600 dark:text-primary-400";

    ViewData["Actions"] = new object[] {
        new { Text = "New Project", Variant = "primary", Icon = "fas fa-plus", Href = Url.Action("Create", "Projects") },
        new { Text = "New Task", Variant = "outline", Icon = "fas fa-tasks", Href = Url.Action("Create", "Tasks") }
    };

    var statsArray = new List<object>
    {
        new { Label = "Total Projects", Value = Model.Stats.TotalProjects.ToString(), Icon = "fas fa-project-diagram", Color = "blue" },
        new { Label = "Active Projects", Value = Model.Stats.ActiveProjects.ToString(), Icon = "fas fa-play", Color = "green" },
        new { Label = "My Tasks", Value = Model.Stats.MyTasks.ToString(), Icon = "fas fa-tasks", Color = "indigo" },
        new { Label = "Overdue Tasks", Value = Model.Stats.OverdueTasks.ToString(), Icon = "fas fa-exclamation-triangle", Color = "red" }
    };

    ViewData["Stats"] = statsArray.ToArray();
}
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Main Content Grid -->
<div class="grid grid-cols-1 xl:grid-cols-2 gap-8 mb-8">
    <!-- Recent Projects -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-folder-open text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">@Localizer["Nav.Projects"]</h3>
                        <p class="text-sm text-neutral-500 dark:text-neutral-400">Latest project updates</p>
                    </div>
                </div>
                @{
                    ViewData["Text"] = "View All";
                    ViewData["Variant"] = "outline";
                    ViewData["Size"] = "sm";
                    ViewData["Href"] = Url.Action("Index", "Projects");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
        <div class="card-body-custom">
            @if (Model.RecentProjects.Any())
            {
                <div class="space-y-4">
                    @foreach (var project in Model.RecentProjects)
                    {
                        <div class="flex items-center justify-between p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-600 transition-colors">
                            <div class="flex-1">
                                <h4 class="text-sm font-semibold text-neutral-900 dark:text-white mb-2">
                                    <a href="@Url.Action("Details", "Projects", new { id = project.Id })"
                                       class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                        @project.Name
                                    </a>
                                </h4>
                                <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2 mb-2">
                                    @{
                                        var progressClass = project.ProgressPercentage >= 80 ? "bg-success-500" :
                                                          project.ProgressPercentage >= 60 ? "bg-info-500" :
                                                          project.ProgressPercentage >= 30 ? "bg-warning-500" : "bg-danger-500";
                                    }
                                    <div class="@progressClass h-2 rounded-full transition-all duration-300" style="width: @project.ProgressPercentage%"></div>
                                </div>
                                <p class="text-xs text-neutral-500 dark:text-neutral-400">@project.ProgressPercentage.ToString("F1")% Complete</p>
                            </div>
                            <div class="ml-4">
                                @{
                                    var statusClass = project.Status == PM.Tool.Core.Enums.ProjectStatus.Active ? "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200" :
                                                    project.Status == PM.Tool.Core.Enums.ProjectStatus.Completed ? "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200" :
                                                    project.Status == PM.Tool.Core.Enums.ProjectStatus.OnHold ? "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200" :
                                                    "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-neutral-200";
                                }
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                                    @project.Status
                                </span>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-folder-open text-2xl text-neutral-400 dark:text-neutral-500"></i>
                    </div>
                    <p class="text-neutral-500 dark:text-neutral-400">No projects found.</p>
                </div>
            }
        </div>
    </div>

    <!-- My Tasks -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-info-100 dark:bg-info-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tasks text-info-600 dark:text-info-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">@Localizer["Nav.Tasks"]</h3>
                        <p class="text-sm text-neutral-500 dark:text-neutral-400">Your assigned tasks</p>
                    </div>
                </div>
                @{
                    ViewData["Text"] = "View All";
                    ViewData["Variant"] = "outline";
                    ViewData["Size"] = "sm";
                    ViewData["Href"] = Url.Action("Index", "Tasks");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
        <div class="card-body-custom">
            @if (Model.MyTasks.Any())
            {
                <div class="space-y-4">
                    @foreach (var task in Model.MyTasks.Take(5))
                    {
                        <div class="flex items-center justify-between p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-600 transition-colors">
                            <div class="flex-1">
                                <h4 class="text-sm font-semibold text-neutral-900 dark:text-white mb-1">
                                    <a href="@Url.Action("Details", "Tasks", new { id = task.Id })"
                                       class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                        @task.Title
                                    </a>
                                </h4>
                                <div class="flex items-center space-x-2 text-xs text-neutral-500 dark:text-neutral-400">
                                    <span>@task.ProjectName</span>
                                    @if (task.DueDate.HasValue)
                                    {
                                        <span>•</span>
                                        <span>Due: @task.DueDate.Value.ToString("MMM dd")</span>
                                    }
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                @{
                                    var priorityClass = task.Priority == PM.Tool.Core.Enums.TaskPriority.Critical ? "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200" :
                                                      task.Priority == PM.Tool.Core.Enums.TaskPriority.High ? "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200" :
                                                      task.Priority == PM.Tool.Core.Enums.TaskPriority.Medium ? "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200" :
                                                      "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-neutral-200";

                                    var taskStatusClass = task.Status == PM.Tool.Core.Enums.TaskStatus.Done ? "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200" :
                                                        task.Status == PM.Tool.Core.Enums.TaskStatus.InProgress ? "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200" :
                                                        task.Status == PM.Tool.Core.Enums.TaskStatus.InReview ? "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200" :
                                                        "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-neutral-200";
                                }
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium @priorityClass">
                                    @task.Priority
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium @taskStatusClass">
                                    @task.Status
                                </span>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-tasks text-2xl text-neutral-400 dark:text-neutral-500"></i>
                    </div>
                    <p class="text-neutral-500 dark:text-neutral-400">No tasks assigned to you.</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Alert Tasks Section -->
@if (Model.OverdueTasks.Any() || Model.UpcomingTasks.Any())
{
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-8 mb-8">
        <!-- Overdue Tasks -->
        @if (Model.OverdueTasks.Any())
        {
            <div class="card-custom border-l-4 border-danger-500">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-danger-100 dark:bg-danger-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-danger-700 dark:text-danger-400">Overdue Tasks</h3>
                            <p class="text-sm text-danger-600 dark:text-danger-500">Tasks that need immediate attention</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-3">
                        @foreach (var task in Model.OverdueTasks)
                        {
                            <div class="flex items-center justify-between p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg border border-danger-200 dark:border-danger-800">
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold text-neutral-900 dark:text-white mb-1">
                                        <a href="@Url.Action("Details", "Tasks", new { id = task.Id })"
                                           class="hover:text-danger-600 dark:hover:text-danger-400 transition-colors">
                                            @task.Title
                                        </a>
                                    </h4>
                                    <p class="text-xs text-danger-600 dark:text-danger-400">
                                        Due: @task.DueDate?.ToString("MMM dd, yyyy")
                                    </p>
                                </div>
                                <div class="ml-3">
                                    <i class="fas fa-clock text-danger-500 dark:text-danger-400"></i>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }

        <!-- Upcoming Tasks -->
        @if (Model.UpcomingTasks.Any())
        {
            <div class="card-custom border-l-4 border-info-500">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-info-100 dark:bg-info-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-alt text-info-600 dark:text-info-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-info-700 dark:text-info-400">Upcoming Tasks</h3>
                            <p class="text-sm text-info-600 dark:text-info-500">Tasks due soon</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-3">
                        @foreach (var task in Model.UpcomingTasks)
                        {
                            <div class="flex items-center justify-between p-3 bg-info-50 dark:bg-info-900/20 rounded-lg border border-info-200 dark:border-info-800">
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold text-neutral-900 dark:text-white mb-1">
                                        <a href="@Url.Action("Details", "Tasks", new { id = task.Id })"
                                           class="hover:text-info-600 dark:hover:text-info-400 transition-colors">
                                            @task.Title
                                        </a>
                                    </h4>
                                    <p class="text-xs text-info-600 dark:text-info-400">
                                        Due: @task.DueDate?.ToString("MMM dd, yyyy")
                                    </p>
                                </div>
                                <div class="ml-3">
                                    <i class="fas fa-calendar text-info-500 dark:text-info-400"></i>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
}

<!-- Recent Notifications -->
@if (Model.RecentNotifications.Any())
{
    <div class="card-custom mb-8">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-bell text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Recent Notifications</h3>
                    <p class="text-sm text-neutral-500 dark:text-neutral-400">Latest updates and alerts</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="space-y-4">
                @foreach (var notification in Model.RecentNotifications)
                {
                    <div class="flex items-start justify-between p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg @(notification.IsRead ? "" : "border-l-4 border-primary-500")">
                        <div class="flex-1">
                            <h4 class="text-sm font-semibold text-neutral-900 dark:text-white mb-1 @(notification.IsRead ? "" : "font-bold")">
                                @notification.Title
                            </h4>
                            <p class="text-sm text-neutral-600 dark:text-neutral-300 mb-2">
                                @notification.Message
                            </p>
                            <p class="text-xs text-neutral-500 dark:text-neutral-400">
                                @notification.CreatedAt.ToString("MMM dd, yyyy HH:mm")
                            </p>
                        </div>
                        @if (!notification.IsRead)
                        {
                            <div class="ml-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200">
                                    New
                                </span>
                            </div>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
}

@section Scripts {
    <script>
        // Add any dashboard-specific JavaScript here
    </script>
}
