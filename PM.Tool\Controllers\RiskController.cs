using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Services;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    public class RiskController : SecureBaseController
    {
        private readonly IRiskService _riskService;
        private readonly IProjectService _projectService;
        private readonly IFormHelperService _formHelper;
        private readonly ILogger<RiskController> _logger;

        public RiskController(
            IRiskService riskService,
            IProjectService projectService,
            IFormHelperService formHelper,
            IAuditService auditService,
            ILogger<RiskController> logger) : base(auditService)
        {
            _riskService = riskService;
            _projectService = projectService;
            _formHelper = formHelper;
            _logger = logger;
        }

        // GET: Risk
        public async Task<IActionResult> Index(int? projectId)
        {
            try
            {
                IEnumerable<Risk> risks;

                if (projectId.HasValue)
                {
                    risks = await _riskService.GetProjectRisksAsync(projectId.Value);
                    var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                    ViewBag.ProjectName = project?.Name;
                    ViewBag.ProjectId = projectId.Value;
                }
                else
                {
                    risks = await _riskService.GetAllRisksAsync();
                }

                return View(risks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading risks");
                TempData["Error"] = $"Error loading risks: {ex.Message}";
                return View(new List<Risk>());
            }
        }

        // GET: Risk/Test - Test database connectivity (Allow anonymous for testing)
        [AllowAnonymous]
        public async Task<IActionResult> Test()
        {
            try
            {
                var risks = await _riskService.GetAllRisksAsync();
                var riskCount = risks.Count();

                return Json(new {
                    success = true,
                    message = $"Database connection successful. Found {riskCount} risks.",
                    risks = risks.Select(r => new { r.Id, r.Title, r.Status, r.RiskLevel }).ToList()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database test failed");
                return Json(new {
                    success = false,
                    message = $"Database test failed: {ex.Message}",
                    details = ex.ToString()
                });
            }
        }

        // GET: Risk/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var risk = await _riskService.GetRiskByIdAsync(id);
                if (risk == null) return NotFound();

                var mitigationActions = await _riskService.GetRiskMitigationActionsAsync(id);
                ViewBag.MitigationActions = mitigationActions;

                return View(risk);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading risk details.";
                return RedirectToAction("Index");
            }
        }

        // GET: Risk/Create
        public async Task<IActionResult> Create(int? projectId)
        {
            try
            {
                var viewModel = new RiskCreateViewModel();

                if (projectId.HasValue)
                {
                    viewModel.ProjectId = projectId.Value;
                    var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                    ViewBag.ProjectName = project?.Name;
                }

                await PopulateDropdowns();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading risk creation form.";
                return RedirectToAction("Index");
            }
        }

        // POST: Risk/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(RiskCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<RiskCreateViewModel, Risk>(
                viewModel,
                async (risk) => {
                    risk.CreatedByUserId = _formHelper.GetCurrentUserId(User);
                    var createdRisk = await _riskService.CreateRiskAsync(risk);
                    await LogAuditAsync(AuditAction.Create, "Risk", createdRisk.Id);
                    return createdRisk;
                },
                risk => risk.Id,
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Risk"
            );
        }

        // GET: Risk/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var risk = await _riskService.GetRiskByIdAsync(id);
                if (risk == null) return NotFound();

                var viewModel = RiskEditViewModel.FromEntity(risk);
                await PopulateDropdowns();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading risk for editing.";
                return RedirectToAction("Index");
            }
        }

        // POST: Risk/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, RiskEditViewModel viewModel)
        {
            if (id != viewModel.Id) return NotFound();

            return await this.HandleUpdateAsync<RiskEditViewModel, Risk>(
                id,
                viewModel,
                async (riskId) => await _riskService.GetRiskByIdAsync(riskId),
                async (risk) => {
                    var updatedRisk = await _riskService.UpdateRiskAsync(risk);
                    await LogAuditAsync(AuditAction.Update, "Risk", id);
                    return updatedRisk;
                },
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Risk"
            );
        }

        // POST: Risk/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var success = await _riskService.DeleteRiskAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Delete, "Risk", id);
                    TempData["Success"] = "Risk deleted successfully.";
                }
                else
                {
                    TempData["Error"] = "Failed to delete risk.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error deleting risk.";
            }

            return RedirectToAction("Index");
        }

        // POST: Risk/UpdateStatus
        [HttpPost]
        public async Task<IActionResult> UpdateStatus(int riskId, RiskStatus status)
        {
            try
            {
                var success = await _riskService.UpdateRiskStatusAsync(riskId, status);

                if (success)
                {
                    await LogAuditAsync(AuditAction.ChangeStatus, "Risk", riskId);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to update risk status." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error updating risk status." });
            }
        }

        // GET: Risk/CreateMitigationAction/5
        public async Task<IActionResult> CreateMitigationAction(int riskId)
        {
            try
            {
                var risk = await _riskService.GetRiskByIdAsync(riskId);
                if (risk == null) return NotFound();

                var action = new RiskMitigationAction
                {
                    RiskId = riskId,
                    DueDate = DateTime.Today.AddDays(7)
                };

                ViewBag.Risk = risk;
                await PopulateMitigationActionDropdowns();

                return View(action);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading mitigation action form.";
                return RedirectToAction("Details", new { id = riskId });
            }
        }

        // POST: Risk/CreateMitigationAction
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateMitigationAction(RiskMitigationAction action)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var createdAction = await _riskService.CreateMitigationActionAsync(action);
                    await LogAuditAsync(AuditAction.Create, "RiskMitigationAction", createdAction.Id);

                    TempData["Success"] = "Mitigation action created successfully.";
                    return RedirectToAction("Details", new { id = action.RiskId });
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error creating mitigation action.";
            }

            var risk = await _riskService.GetRiskByIdAsync(action.RiskId);
            ViewBag.Risk = risk;
            await PopulateMitigationActionDropdowns();

            return View(action);
        }

        // POST: Risk/CompleteMitigationAction/5
        [HttpPost]
        public async Task<IActionResult> CompleteMitigationAction(int id)
        {
            try
            {
                var success = await _riskService.CompleteMitigationActionAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "RiskMitigationAction", id);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to complete mitigation action." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error completing mitigation action." });
            }
        }

        // GET: Risk/Matrix/5
        public async Task<IActionResult> Matrix(int? projectId)
        {
            try
            {
                IEnumerable<Risk> risks;

                if (projectId.HasValue && projectId.Value > 0)
                {
                    risks = await _riskService.GetProjectRisksAsync(projectId.Value);
                    var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                    ViewBag.ProjectName = project?.Name;
                    ViewBag.ProjectId = projectId.Value;
                }
                else
                {
                    risks = await _riskService.GetAllRisksAsync();
                    ViewBag.ProjectName = "All Projects";
                    ViewBag.ProjectId = 0;
                }

                return View(risks);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading risk matrix.";
                return RedirectToAction("Index", new { projectId });
            }
        }

        // GET: Risk/Analytics/5
        public async Task<IActionResult> Analytics(int? projectId)
        {
            try
            {
                IEnumerable<Risk> risks;

                if (projectId.HasValue && projectId.Value > 0)
                {
                    risks = await _riskService.GetProjectRisksAsync(projectId.Value);
                    var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                    ViewBag.ProjectName = project?.Name;
                    ViewBag.ProjectId = projectId.Value;

                    var categoryDistribution = await _riskService.GetRiskDistributionByCategoryAsync(projectId.Value);
                    var levelDistribution = await _riskService.GetRiskDistributionByLevelAsync(projectId.Value);
                    var riskScore = await _riskService.GetProjectRiskScoreAsync(projectId.Value);
                    var topRisks = await _riskService.GetTopRisksByScoreAsync(projectId.Value, 5);

                    // Convert to string dictionaries for the view
                    var categoryDict = categoryDistribution.ToDictionary(kvp => kvp.Key.ToString(), kvp => kvp.Value);
                    var levelDict = levelDistribution.ToDictionary(kvp => kvp.Key.ToString(), kvp => kvp.Value);

                    ViewBag.CategoryDistribution = categoryDict;
                    ViewBag.LevelDistribution = levelDict;
                    ViewBag.RiskScore = riskScore;
                    ViewBag.TopRisks = topRisks;
                }
                else
                {
                    risks = await _riskService.GetAllRisksAsync();
                    ViewBag.ProjectName = "All Projects";
                    ViewBag.ProjectId = 0;
                    ViewBag.CategoryDistribution = new Dictionary<string, int>();
                    ViewBag.LevelDistribution = new Dictionary<string, int>();
                    ViewBag.RiskScore = 0;
                    ViewBag.TopRisks = new List<Risk>();
                }

                return View(risks);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading risk analytics.";
                return RedirectToAction("Index", new { projectId });
            }
        }

        // API Endpoints for Risk Analytics
        [HttpGet]
        public async Task<IActionResult> GetRiskAnalytics(int? projectId)
        {
            try
            {
                if (projectId.HasValue && projectId.Value > 0)
                {
                    var categoryDistribution = await _riskService.GetRiskDistributionByCategoryAsync(projectId.Value);
                    var levelDistribution = await _riskService.GetRiskDistributionByLevelAsync(projectId.Value);
                    var riskScore = await _riskService.GetProjectRiskScoreAsync(projectId.Value);
                    var topRisks = await _riskService.GetTopRisksByScoreAsync(projectId.Value, 5);
                    var allRisks = await _riskService.GetProjectRisksAsync(projectId.Value);

                    var analytics = new
                    {
                        CategoryDistribution = categoryDistribution.ToDictionary(kv => kv.Key.ToString(), kv => kv.Value),
                        LevelDistribution = levelDistribution.ToDictionary(kv => kv.Key.ToString(), kv => kv.Value),
                        ProjectRiskScore = riskScore,
                        TopRisks = topRisks.Select(r => new {
                            r.Id,
                            r.Title,
                            r.Description,
                            Probability = (int)r.Probability,
                            Impact = (int)r.Impact,
                            RiskScore = (int)r.Probability * (int)r.Impact,
                            Status = r.Status.ToString(),
                            Category = r.Category.ToString(),
                            RiskLevel = r.RiskLevel.ToString()
                        }),
                        AllRisks = allRisks.Select(r => new {
                            r.Id,
                            r.Title,
                            r.Description,
                            Probability = (int)r.Probability,
                            Impact = (int)r.Impact,
                            RiskScore = (int)r.Probability * (int)r.Impact,
                            Status = r.Status.ToString(),
                            Category = r.Category.ToString(),
                            ProjectId = r.ProjectId,
                            RiskLevel = r.RiskLevel.ToString(),
                            CreatedAt = r.CreatedAt.ToString("yyyy-MM-dd"),
                            TargetResolutionDate = r.TargetResolutionDate?.ToString("yyyy-MM-dd")
                        }),
                        Summary = new {
                            TotalRisks = allRisks.Count(),
                            HighCriticalRisks = allRisks.Count(r => r.RiskLevel == RiskLevel.High || r.RiskLevel == RiskLevel.Critical),
                            OpenRisks = allRisks.Count(r => r.Status != RiskStatus.Resolved),
                            AverageRiskScore = allRisks.Any() ? Math.Round(allRisks.Average(r => (int)r.Probability * (int)r.Impact), 1) : 0
                        }
                    };

                    return Json(analytics);
                }
                else
                {
                    // All projects
                    var allRisks = await _riskService.GetAllRisksAsync();

                    var analytics = new
                    {
                        CategoryDistribution = allRisks.GroupBy(r => r.Category.ToString()).ToDictionary(g => g.Key, g => g.Count()),
                        LevelDistribution = allRisks.GroupBy(r => r.RiskLevel.ToString()).ToDictionary(g => g.Key, g => g.Count()),
                        ProjectRiskScore = 0m, // Not applicable for all projects
                        TopRisks = allRisks.OrderByDescending(r => (int)r.Probability * (int)r.Impact).Take(5).Select(r => new {
                            r.Id,
                            r.Title,
                            r.Description,
                            Probability = (int)r.Probability,
                            Impact = (int)r.Impact,
                            RiskScore = (int)r.Probability * (int)r.Impact,
                            Status = r.Status.ToString(),
                            Category = r.Category.ToString(),
                            RiskLevel = r.RiskLevel.ToString()
                        }),
                        AllRisks = allRisks.Select(r => new {
                            r.Id,
                            r.Title,
                            r.Description,
                            Probability = (int)r.Probability,
                            Impact = (int)r.Impact,
                            RiskScore = (int)r.Probability * (int)r.Impact,
                            Status = r.Status.ToString(),
                            Category = r.Category.ToString(),
                            ProjectId = r.ProjectId,
                            RiskLevel = r.RiskLevel.ToString(),
                            CreatedAt = r.CreatedAt.ToString("yyyy-MM-dd"),
                            TargetResolutionDate = r.TargetResolutionDate?.ToString("yyyy-MM-dd")
                        }),
                        Summary = new {
                            TotalRisks = allRisks.Count(),
                            HighCriticalRisks = allRisks.Count(r => r.RiskLevel == RiskLevel.High || r.RiskLevel == RiskLevel.Critical),
                            OpenRisks = allRisks.Count(r => r.Status != RiskStatus.Resolved),
                            AverageRiskScore = allRisks.Any() ? Math.Round(allRisks.Average(r => (int)r.Probability * (int)r.Impact), 1) : 0
                        }
                    };

                    return Json(analytics);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting risk analytics for project {ProjectId}", projectId);
                return Json(new { error = "Error loading risk analytics" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetRiskTrendData(int? projectId, int days = 30)
        {
            try
            {
                var endDate = DateTime.UtcNow.Date;
                var startDate = endDate.AddDays(-days);

                IEnumerable<Risk> risks;
                if (projectId.HasValue && projectId.Value > 0)
                {
                    risks = await _riskService.GetProjectRisksAsync(projectId.Value);
                }
                else
                {
                    risks = await _riskService.GetAllRisksAsync();
                }

                var trendData = new List<object>();
                for (var date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var risksCreated = risks.Count(r => r.CreatedAt.Date == date);
                    var risksResolved = risks.Count(r => r.Status == RiskStatus.Resolved &&
                                                        r.UpdatedAt.HasValue && r.UpdatedAt.Value.Date == date);

                    trendData.Add(new {
                        Date = date.ToString("yyyy-MM-dd"),
                        Created = risksCreated,
                        Resolved = risksResolved,
                        Net = risksCreated - risksResolved
                    });
                }

                return Json(trendData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting risk trend data for project {ProjectId}", projectId);
                return Json(new { error = "Error loading risk trend data" });
            }
        }

        private async Task PopulateDropdowns()
        {
            ViewBag.RiskCategories = Enum.GetValues<RiskCategory>()
                .Select(rc => new SelectListItem
                {
                    Value = ((int)rc).ToString(),
                    Text = rc.ToString()
                });

            ViewBag.RiskProbabilities = Enum.GetValues<RiskProbability>()
                .Select(rp => new SelectListItem
                {
                    Value = ((int)rp).ToString(),
                    Text = rp.ToString()
                });

            ViewBag.RiskImpacts = Enum.GetValues<RiskImpact>()
                .Select(ri => new SelectListItem
                {
                    Value = ((int)ri).ToString(),
                    Text = ri.ToString()
                });

            ViewBag.RiskStatuses = Enum.GetValues<RiskStatus>()
                .Select(rs => new SelectListItem
                {
                    Value = ((int)rs).ToString(),
                    Text = rs.ToString()
                });
        }

        private async Task PopulateMitigationActionDropdowns()
        {
            ViewBag.ActionStatuses = Enum.GetValues<ActionStatus>()
                .Select(status => new SelectListItem
                {
                    Value = ((int)status).ToString(),
                    Text = status.ToString()
                });
        }
    }
}
