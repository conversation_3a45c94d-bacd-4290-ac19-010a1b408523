@{
    ViewData["Title"] = "Raw Encrypted Data View";
    ViewData["Breadcrumbs"] = new List<object>
    {
        new { Text = "Home", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Encryption Demo", Href = Url.Action("Index", "EncryptionDemo"), Icon = "fas fa-shield-alt" },
        new { Text = "Raw Data", Href = "#", Icon = "fas fa-database" }
    };
}

<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-100 mb-2">
            <i class="fas fa-database text-primary-600 mr-3"></i>
            Raw Encrypted Data View
        </h1>
        <p class="text-neutral-600 dark:text-dark-300">
            Compare encrypted data stored in the database with decrypted data for display
        </p>
    </div>

    <!-- Back Button -->
    <div class="mb-6">
        <a asp-action="Index" 
           class="inline-flex items-center px-4 py-2 bg-neutral-100 hover:bg-neutral-200 dark:bg-dark-400 dark:hover:bg-dark-300 text-neutral-700 dark:text-dark-200 rounded-lg transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Encryption Demo
        </a>
    </div>

    @if (ViewBag.EncryptedPerson != null && ViewBag.DecryptedPerson != null)
    {
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Encrypted Data (Raw from Database) -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-lock text-red-600 mr-2"></i>
                    Encrypted Data (Raw Database Storage)
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">ID:</label>
                        <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border text-sm">
                            @ViewBag.EncryptedPerson.Id
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">First Name (Encrypted):</label>
                        <div class="p-3 bg-red-50 dark:bg-red-900/20 rounded border text-sm font-mono break-all">
                            @ViewBag.EncryptedPerson.FirstName
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Last Name (Encrypted):</label>
                        <div class="p-3 bg-red-50 dark:bg-red-900/20 rounded border text-sm font-mono break-all">
                            @ViewBag.EncryptedPerson.LastName
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Email (Encrypted):</label>
                        <div class="p-3 bg-red-50 dark:bg-red-900/20 rounded border text-sm font-mono break-all">
                            @ViewBag.EncryptedPerson.Email
                        </div>
                    </div>
                    @if (!string.IsNullOrEmpty(ViewBag.EncryptedPerson.Bio))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Bio (Encrypted):</label>
                            <div class="p-3 bg-red-50 dark:bg-red-900/20 rounded border text-sm font-mono break-all">
                                @ViewBag.EncryptedPerson.Bio
                            </div>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(ViewBag.EncryptedPerson.Phone))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Phone (Encrypted):</label>
                            <div class="p-3 bg-red-50 dark:bg-red-900/20 rounded border text-sm font-mono break-all">
                                @ViewBag.EncryptedPerson.Phone
                            </div>
                        </div>
                    }
                    
                    <!-- Hash Fields -->
                    <div class="border-t border-neutral-200 dark:border-dark-300 pt-4">
                        <h4 class="text-md font-medium text-neutral-800 dark:text-dark-100 mb-3">
                            <i class="fas fa-hashtag text-yellow-600 mr-2"></i>
                            Search Hash Fields
                        </h4>
                        @if (!string.IsNullOrEmpty(ViewBag.EncryptedPerson.FirstNameHash))
                        {
                            <div class="mb-3">
                                <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">First Name Hash:</label>
                                <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border text-sm font-mono break-all">
                                    @ViewBag.EncryptedPerson.FirstNameHash
                                </div>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(ViewBag.EncryptedPerson.LastNameHash))
                        {
                            <div class="mb-3">
                                <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Last Name Hash:</label>
                                <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border text-sm font-mono break-all">
                                    @ViewBag.EncryptedPerson.LastNameHash
                                </div>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(ViewBag.EncryptedPerson.EmailHash))
                        {
                            <div class="mb-3">
                                <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Email Hash:</label>
                                <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border text-sm font-mono break-all">
                                    @ViewBag.EncryptedPerson.EmailHash
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Decrypted Data (For Display) -->
            <div class="bg-white dark:bg-surface-dark rounded-lg shadow-sm border border-neutral-200 dark:border-dark-300 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">
                    <i class="fas fa-unlock text-green-600 mr-2"></i>
                    Decrypted Data (For Display)
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">ID:</label>
                        <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border text-sm">
                            @ViewBag.DecryptedPerson.Id
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">First Name (Decrypted):</label>
                        <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded border text-sm">
                            @ViewBag.DecryptedPerson.FirstName
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Last Name (Decrypted):</label>
                        <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded border text-sm">
                            @ViewBag.DecryptedPerson.LastName
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Email (Decrypted):</label>
                        <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded border text-sm">
                            @ViewBag.DecryptedPerson.Email
                        </div>
                    </div>
                    @if (!string.IsNullOrEmpty(ViewBag.DecryptedPerson.Bio))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Bio (Decrypted):</label>
                            <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded border text-sm">
                                @ViewBag.DecryptedPerson.Bio
                            </div>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(ViewBag.DecryptedPerson.Phone))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Phone (Decrypted):</label>
                            <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded border text-sm">
                                @ViewBag.DecryptedPerson.Phone
                            </div>
                        </div>
                    }

                    <!-- Metadata -->
                    <div class="border-t border-neutral-200 dark:border-dark-300 pt-4">
                        <h4 class="text-md font-medium text-neutral-800 dark:text-dark-100 mb-3">
                            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                            Metadata
                        </h4>
                        <div class="grid grid-cols-1 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Person Type:</label>
                                <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border text-sm">
                                    @ViewBag.DecryptedPerson.Type
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-1">Created At:</label>
                                <div class="p-3 bg-neutral-50 dark:bg-dark-400 rounded border text-sm">
                                    @ViewBag.DecryptedPerson.CreatedAt.ToString("MMM dd, yyyy HH:mm:ss UTC")
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Information -->
        <div class="mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
                <i class="fas fa-shield-alt mr-2"></i>
                Security Information
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">Encryption Details:</h4>
                    <ul class="space-y-1 text-blue-700 dark:text-blue-300">
                        <li><i class="fas fa-check mr-2"></i>AES-256 encryption for sensitive fields</li>
                        <li><i class="fas fa-check mr-2"></i>ASP.NET Core Data Protection for key management</li>
                        <li><i class="fas fa-check mr-2"></i>SHA-256 hashing for searchable fields</li>
                        <li><i class="fas fa-check mr-2"></i>Automatic encryption via Entity Framework interceptors</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">Security Features:</h4>
                    <ul class="space-y-1 text-blue-700 dark:text-blue-300">
                        <li><i class="fas fa-check mr-2"></i>Data encrypted at rest in database</li>
                        <li><i class="fas fa-check mr-2"></i>Searchable hash fields for encrypted data</li>
                        <li><i class="fas fa-check mr-2"></i>Transparent encryption/decryption</li>
                        <li><i class="fas fa-check mr-2"></i>Secure key generation and storage</li>
                    </ul>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-yellow-600 mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Data Found</h3>
                    <p class="text-yellow-700 dark:text-yellow-300">The requested person record could not be found.</p>
                </div>
            </div>
        </div>
    }
</div>
