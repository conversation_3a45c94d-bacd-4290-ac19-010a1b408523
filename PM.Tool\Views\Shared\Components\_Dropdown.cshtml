@*
    Standardized Dropdown Component - Usage Examples:

    1. Basic dropdown:
    @{
        ViewData["DropdownId"] = "exampleDropdown";
        ViewData["ButtonText"] = "Options";
        ViewData["ButtonIcon"] = "fas fa-cog";
        ViewData["ButtonVariant"] = "secondary"; // primary, secondary, success, warning, danger
        ViewData["Items"] = new List<object> {
            new { Text = "Edit", Icon = "fas fa-edit", OnClick = "editItem()" },
            new { Text = "Delete", Icon = "fas fa-trash", OnClick = "deleteItem()", IsDanger = true },
            new { IsDivider = true },
            new { Text = "Settings", Icon = "fas fa-cog", Href = "/settings" }
        };
    }
    <partial name="Components/_Dropdown" view-data="ViewData" />

    2. Action dropdown:
    @{
        ViewData["DropdownId"] = "actionDropdown";
        ViewData["ButtonText"] = "Actions";
        ViewData["ButtonIcon"] = "fas fa-ellipsis-v";
        ViewData["ButtonVariant"] = "ghost";
        ViewData["Position"] = "right"; // left, right
        ViewData["Items"] = new List<object> {
            new { Text = "View Details", Icon = "fas fa-eye", Href = "/details/1" },
            new { Text = "Edit", Icon = "fas fa-edit", OnClick = "showEditModal()" },
            new { IsDivider = true },
            new { Text = "Archive", Icon = "fas fa-archive", OnClick = "archiveItem()" }
        };
    }
    <partial name="Components/_Dropdown" view-data="ViewData" />
*@

@model dynamic

@{
    var dropdownId = ViewData["DropdownId"]?.ToString() ?? "dropdown";
    var buttonText = ViewData["ButtonText"]?.ToString() ?? "Options";
    var buttonIcon = ViewData["ButtonIcon"]?.ToString();
    var buttonVariant = ViewData["ButtonVariant"]?.ToString() ?? "secondary";
    var position = ViewData["Position"]?.ToString() ?? "left"; // left, right
    var items = ViewData["Items"] as IEnumerable<object> ?? new List<object>();
    var disabled = ViewData["Disabled"] as bool? ?? false;
    var fullWidth = ViewData["FullWidth"] as bool? ?? false;

    var buttonClasses = buttonVariant switch {
        "primary" => "bg-primary-600 hover:bg-primary-700 text-white",
        "secondary" => "bg-neutral-100 hover:bg-neutral-200 dark:bg-dark-700 dark:hover:bg-dark-600 text-neutral-700 dark:text-dark-300",
        "success" => "bg-success-600 hover:bg-success-700 text-white",
        "warning" => "bg-warning-600 hover:bg-warning-700 text-white",
        "danger" => "bg-danger-600 hover:bg-danger-700 text-white",
        "ghost" => "hover:bg-neutral-100 dark:hover:bg-dark-700 text-neutral-600 dark:text-dark-400",
        _ => "bg-neutral-100 hover:bg-neutral-200 dark:bg-dark-700 dark:hover:bg-dark-600 text-neutral-700 dark:text-dark-300"
    };

    var positionClasses = position == "right" ? "right-0" : "left-0";
    var widthClasses = fullWidth ? "w-full" : "";
}

<!-- Standardized Dropdown -->
<div class="relative inline-block text-left @widthClasses">
    <!-- Dropdown Button -->
    <button type="button"
            id="@(dropdownId)Btn"
            onclick="toggleDropdown('@dropdownId')"
            class="inline-flex items-center justify-center px-4 py-2 @buttonClasses font-medium rounded-lg transition-colors duration-200 shadow-sm @(disabled ? "opacity-50 cursor-not-allowed" : "") @widthClasses"
            @(disabled ? "disabled" : "")
            aria-expanded="false"
            aria-haspopup="true">
        @if (!string.IsNullOrEmpty(buttonIcon))
        {
            <i class="@buttonIcon @(!string.IsNullOrEmpty(buttonText) ? "mr-2" : "") text-sm"></i>
        }
        @if (!string.IsNullOrEmpty(buttonText))
        {
            <span>@buttonText</span>
        }
        <i class="fas fa-chevron-down ml-2 text-xs transition-transform duration-200" id="@(dropdownId)Chevron"></i>
    </button>

    <!-- Dropdown Menu -->
    <div id="@dropdownId"
         class="absolute @positionClasses mt-2 w-56 bg-white dark:bg-dark-800 rounded-lg shadow-lg border border-neutral-200 dark:border-dark-600 z-50 hidden"
         role="menu"
         aria-orientation="vertical"
         aria-labelledby="@(dropdownId)Btn">
        <div class="py-1">
            @foreach (var item in items)
            {
                var itemDict = item as IDictionary<string, object> ?? 
                              item.GetType().GetProperties().ToDictionary(p => p.Name, p => p.GetValue(item));
                
                var isDivider = itemDict.ContainsKey("IsDivider") && (bool)itemDict["IsDivider"];
                
                if (isDivider)
                {
                    <div class="border-t border-neutral-200 dark:border-dark-600 my-1"></div>
                }
                else
                {
                    var text = itemDict.ContainsKey("Text") ? itemDict["Text"]?.ToString() : "";
                    var icon = itemDict.ContainsKey("Icon") ? itemDict["Icon"]?.ToString() : "";
                    var onClick = itemDict.ContainsKey("OnClick") ? itemDict["OnClick"]?.ToString() : "";
                    var href = itemDict.ContainsKey("Href") ? itemDict["Href"]?.ToString() : "";
                    var isDanger = itemDict.ContainsKey("IsDanger") && (bool)itemDict["IsDanger"];
                    var isDisabled = itemDict.ContainsKey("IsDisabled") && (bool)itemDict["IsDisabled"];

                    var itemClasses = isDanger 
                        ? "text-danger-600 dark:text-danger-400 hover:bg-danger-50 dark:hover:bg-danger-900" 
                        : "text-neutral-700 dark:text-dark-300 hover:bg-neutral-100 dark:hover:bg-dark-700";

                    if (isDisabled)
                    {
                        itemClasses += " opacity-50 cursor-not-allowed";
                    }

                    if (!string.IsNullOrEmpty(href))
                    {
                        <a href="@href"
                           class="w-full text-left px-4 py-2 text-sm @itemClasses flex items-center @(isDisabled ? "" : "hover:bg-neutral-100 dark:hover:bg-dark-700")"
                           role="menuitem"
                           onclick="hideDropdown('@dropdownId')">
                            @if (!string.IsNullOrEmpty(icon))
                            {
                                <i class="@icon mr-3 text-sm"></i>
                            }
                            @text
                        </a>
                    }
                    else
                    {
                        <button type="button"
                                class="w-full text-left px-4 py-2 text-sm @itemClasses flex items-center @(isDisabled ? "" : "hover:bg-neutral-100 dark:hover:bg-dark-700")"
                                role="menuitem"
                                @(isDisabled ? "disabled" : "")
                                @(!string.IsNullOrEmpty(onClick) ? $"onclick=\"{onClick}; hideDropdown('{dropdownId}')\"" : $"onclick=\"hideDropdown('{dropdownId}')\"")>
                            @if (!string.IsNullOrEmpty(icon))
                            {
                                <i class="@icon mr-3 text-sm"></i>
                            }
                            @text
                        </button>
                    }
                }
            }
        </div>
    </div>
</div>

<script>
    // Standardized Dropdown System
    window.DropdownSystem = window.DropdownSystem || {
        openDropdowns: new Set(),
        
        toggle: function(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const button = document.getElementById(dropdownId + 'Btn');
            const chevron = document.getElementById(dropdownId + 'Chevron');
            
            if (dropdown && button) {
                const isOpen = !dropdown.classList.contains('hidden');
                
                if (isOpen) {
                    this.close(dropdownId);
                } else {
                    // Close other dropdowns first
                    this.closeAll();
                    this.open(dropdownId);
                }
            }
        },
        
        open: function(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const button = document.getElementById(dropdownId + 'Btn');
            const chevron = document.getElementById(dropdownId + 'Chevron');
            
            if (dropdown && button) {
                this.openDropdowns.add(dropdownId);
                dropdown.classList.remove('hidden');
                button.setAttribute('aria-expanded', 'true');
                
                if (chevron) {
                    chevron.classList.add('rotate-180');
                }
                
                // Dispatch custom event
                dropdown.dispatchEvent(new CustomEvent('dropdown:opened', { detail: { dropdownId } }));
            }
        },
        
        close: function(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const button = document.getElementById(dropdownId + 'Btn');
            const chevron = document.getElementById(dropdownId + 'Chevron');
            
            if (dropdown && button) {
                this.openDropdowns.delete(dropdownId);
                dropdown.classList.add('hidden');
                button.setAttribute('aria-expanded', 'false');
                
                if (chevron) {
                    chevron.classList.remove('rotate-180');
                }
                
                // Dispatch custom event
                dropdown.dispatchEvent(new CustomEvent('dropdown:closed', { detail: { dropdownId } }));
            }
        },
        
        closeAll: function() {
            this.openDropdowns.forEach(dropdownId => this.close(dropdownId));
        }
    };

    // Global functions for backward compatibility
    function toggleDropdown(dropdownId) {
        window.DropdownSystem.toggle(dropdownId);
    }

    function hideDropdown(dropdownId) {
        window.DropdownSystem.close(dropdownId);
    }

    function showDropdown(dropdownId) {
        window.DropdownSystem.open(dropdownId);
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        const isDropdownButton = e.target.closest('[id$="Btn"]');
        const isDropdownContent = e.target.closest('[id^="dropdown"], [id$="Dropdown"]');
        
        if (!isDropdownButton && !isDropdownContent) {
            window.DropdownSystem.closeAll();
        }
    });

    // Close dropdowns on Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && window.DropdownSystem.openDropdowns.size > 0) {
            window.DropdownSystem.closeAll();
        }
    });
</script>
