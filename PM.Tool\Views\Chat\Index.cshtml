@model ChatViewModel
@{
    ViewData["Title"] = "Team Chat";
    ViewData["Breadcrumbs"] = new List<object>
    {
        new { Text = "Home", Href = "/", Icon = "fas fa-home" },
        new { Text = "Team Chat", Href = (string?)null, Icon = "fas fa-comments" }
    };
}

<div class="min-h-screen bg-neutral-50 dark:bg-dark-950 transition-colors duration-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-50 flex items-center">
                    <i class="fas fa-comments text-primary-600 dark:text-primary-400 mr-3"></i>
                    Team Chat
                </h1>
                <p class="text-neutral-600 dark:text-dark-300 mt-1">Real-time team communication and collaboration</p>
            </div>

            <!-- Controls -->
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                <!-- Project Filter -->
                <div class="w-full sm:w-auto">
                    <select id="projectFilter" class="form-select-custom w-full sm:w-64">
                        <option value="">All Projects</option>
                        @foreach (var project in Model.UserProjects)
                        {
                            <option value="@project.Id" selected="@(project.Id == Model.ProjectId)">
                                @project.Name
                            </option>
                        }
                    </select>
                </div>

                <!-- Connection Status -->
                <div class="flex items-center">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200" id="chatConnectionStatus">
                        <span class="w-2 h-2 rounded-full mr-2 animate-pulse"></span>
                        Connecting...
                    </span>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Chat Area -->
            <div class="lg:col-span-2">
                <div class="card-custom h-full flex flex-col">
                    <!-- Chat Header -->
                    <div class="card-header-custom">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <h2 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center mb-3 sm:mb-0">
                                <i class="fas fa-comments text-primary-600 dark:text-primary-400 mr-2"></i>
                                <span id="chatTitle">Project Chat</span>
                            </h2>
                            <div class="flex gap-2">
                                <button class="btn-outline-custom text-sm" id="clearChat">
                                    <i class="fas fa-broom mr-1"></i>
                                    Clear
                                </button>
                                <button class="btn-secondary-custom text-sm" id="chatSettings">
                                    <i class="fas fa-cog mr-1"></i>
                                    Settings
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Body -->
                    <div class="flex-1 flex flex-col min-h-0">
                        <!-- Messages Area -->
                        <div id="messagesContainer" class="flex-1 p-6 overflow-y-auto bg-neutral-50 dark:bg-dark-900/50">
                            <!-- Loading State -->
                            <div id="messagesLoading" class="flex flex-col items-center justify-center py-12">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 dark:border-primary-400"></div>
                                <p class="text-neutral-600 dark:text-dark-300 mt-3">Loading messages...</p>
                            </div>

                            <!-- Messages List -->
                            <div id="messagesList" class="space-y-4" style="display: none;">
                                @foreach (var message in Model.Messages)
                                {
                                    <div class="message-item @(message.Type == "system" ? "system-message" : "user-message")" data-message-id="@message.Id">
                                        @if (message.Type == "system")
                                        {
                                            <div class="flex justify-center">
                                                <div class="bg-neutral-200 dark:bg-dark-700 text-neutral-700 dark:text-dark-200 px-4 py-2 rounded-full text-sm">
                                                    @message.Content
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="flex items-start space-x-3">
                                                <div class="flex-shrink-0">
                                                    <div class="w-8 h-8 rounded-full bg-primary-600 dark:bg-primary-500 flex items-center justify-center text-white text-sm font-medium">
                                                        @message.AuthorName.Substring(0, 1).ToUpper()
                                                    </div>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center space-x-2 mb-1">
                                                        <span class="text-sm font-medium text-neutral-900 dark:text-dark-50">@message.AuthorName</span>
                                                        <span class="text-xs text-neutral-500 dark:text-dark-400">@GetRelativeTime(message.Timestamp)</span>
                                                    </div>
                                                    <div class="bg-white dark:bg-dark-800 rounded-lg px-4 py-2 shadow-sm border border-neutral-200 dark:border-dark-700">
                                                        <p class="text-sm text-neutral-900 dark:text-dark-50">@message.Content</p>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>

                            <!-- Empty State -->
                            <div id="emptyMessages" class="flex flex-col items-center justify-center py-16" style="display: none;">
                                <div class="w-16 h-16 rounded-full bg-neutral-100 dark:bg-dark-800 flex items-center justify-center mb-4">
                                    <i class="fas fa-comments text-2xl text-neutral-400 dark:text-dark-500"></i>
                                </div>
                                <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-50 mb-2">No messages yet</h3>
                                <p class="text-neutral-600 dark:text-dark-300 text-center max-w-sm">
                                    Start the conversation by sending a message below.
                                </p>
                            </div>
                        </div>

                        <!-- Message Input -->
                        <div class="border-t border-neutral-200 dark:border-dark-700 p-4 bg-white dark:bg-dark-800">
                            <div class="flex space-x-3">
                                <div class="flex-1">
                                    <input type="text" id="messageInput"
                                           class="form-input-custom w-full"
                                           placeholder="Type your message..."
                                           maxlength="1000">
                                </div>
                                <button class="btn-primary-custom px-6" id="sendMessage" type="button">
                                    <i class="fas fa-paper-plane mr-2"></i>
                                    Send
                                </button>
                            </div>
                            <div class="mt-2">
                                <span id="typingIndicator" class="text-sm text-neutral-500 dark:text-dark-400 flex items-center" style="display: none;">
                                    <i class="fas fa-ellipsis-h mr-2 animate-pulse"></i>
                                    Someone is typing...
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Online Users -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-users text-success-600 dark:text-success-400 mr-2"></i>
                                Online Team Members
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-300" id="onlineCount">0</span>
                        </h3>
                    </div>
                    <div class="card-body-custom">
                        <div id="onlineUsersList" class="space-y-3">
                            <!-- Online users will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Chat Statistics -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                            <i class="fas fa-chart-bar text-info-600 dark:text-info-400 mr-2"></i>
                            Chat Statistics
                        </h3>
                    </div>
                    <div class="card-body-custom">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-neutral-900 dark:text-dark-50" id="todayMessages">-</div>
                                <div class="text-sm text-neutral-600 dark:text-dark-300">Today's Messages</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-neutral-900 dark:text-dark-50" id="activeUsers">-</div>
                                <div class="text-sm text-neutral-600 dark:text-dark-300">Active Users</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-50 flex items-center">
                            <i class="fas fa-bolt text-warning-600 dark:text-warning-400 mr-2"></i>
                            Quick Actions
                        </h3>
                    </div>
                    <div class="card-body-custom">
                        <div class="space-y-3">
                            <button class="btn-outline-custom w-full justify-start" id="shareScreen">
                                <i class="fas fa-desktop mr-2"></i>
                                Share Screen
                            </button>
                            <button class="btn-outline-custom w-full justify-start" id="startCall">
                                <i class="fas fa-video mr-2"></i>
                                Start Video Call
                            </button>
                            <button class="btn-outline-custom w-full justify-start" id="shareFile">
                                <i class="fas fa-file-upload mr-2"></i>
                                Share File
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden data for JavaScript -->
<div data-project-id="@Model.ProjectId" style="display: none;"></div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize chat manager
            const chatManager = new ChatManager();
            
            // Project filter change
            $('#projectFilter').on('change', function() {
                const projectId = $(this).val();
                chatManager.switchProject(projectId);
            });
            
            // Send message
            $('#sendMessage').on('click', function() {
                chatManager.sendMessage();
            });
            
            // Enter key to send message
            $('#messageInput').on('keypress', function(e) {
                if (e.which === 13 && !e.shiftKey) {
                    e.preventDefault();
                    chatManager.sendMessage();
                }
            });
            
            // Typing indicator
            let typingTimer;
            $('#messageInput').on('input', function() {
                clearTimeout(typingTimer);
                chatManager.showTyping();
                typingTimer = setTimeout(() => {
                    chatManager.hideTyping();
                }, 1000);
            });
            
            // Quick actions
            $('#clearChat').on('click', function() {
                chatManager.clearMessages();
            });
            
            $('#shareScreen').on('click', function() {
                alert('Screen sharing feature coming soon!');
            });
            
            $('#startCall').on('click', function() {
                alert('Video call feature coming soon!');
            });
            
            $('#shareFile').on('click', function() {
                alert('File sharing feature coming soon!');
            });
        });
        
        // Chat Manager Class
        class ChatManager {
            constructor() {
                this.currentProjectId = this.getCurrentProjectId();
                this.isConnected = false;
                this.messages = [];
                
                this.initializeConnection();
                this.loadInitialMessages();
            }
            
            initializeConnection() {
                // Connect to SignalR collaboration hub
                if (window.collaboration) {
                    this.isConnected = window.collaboration.isConnected;
                    this.updateConnectionStatus();
                    
                    // Listen for real-time messages
                    window.collaboration.on('MessageReceived', (data) => {
                        this.addMessage(data);
                    });
                    
                    window.collaboration.on('userPresenceChanged', (data) => {
                        this.updateOnlineUsers();
                    });
                } else {
                    console.warn('Collaboration system not available');
                }
            }
            
            async loadInitialMessages() {
                this.showLoading();
                
                try {
                    const response = await fetch(`/Chat/GetMessages?projectId=${this.currentProjectId || ''}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        this.messages = data.messages;
                        this.renderMessages();
                    }
                } catch (error) {
                    console.error('Error loading messages:', error);
                } finally {
                    this.hideLoading();
                }
                
                this.updateOnlineUsers();
                this.updateStatistics();
            }
            
            async sendMessage() {
                const input = $('#messageInput');
                const content = input.val().trim();
                
                if (!content) return;
                
                try {
                    const response = await fetch('/Chat/SendMessage', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                        },
                        body: JSON.stringify({
                            content: content,
                            projectId: this.currentProjectId,
                            type: 'text'
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        input.val('');
                        // Message will be added via SignalR broadcast
                    } else {
                        alert('Failed to send message');
                    }
                } catch (error) {
                    console.error('Error sending message:', error);
                    alert('Error sending message');
                }
            }
            
            addMessage(message) {
                this.messages.unshift(message);
                this.renderMessages();
                this.scrollToBottom();
                this.updateStatistics();
            }
            
            renderMessages() {
                const container = $('#messagesList');
                container.empty();
                
                if (this.messages.length === 0) {
                    $('#emptyMessages').show();
                    container.hide();
                    return;
                }
                
                $('#emptyMessages').hide();
                container.show();
                
                this.messages.forEach(message => {
                    container.append(this.createMessageElement(message));
                });
                
                this.scrollToBottom();
            }
            
            createMessageElement(message) {
                const isSystem = message.type === 'system';

                if (isSystem) {
                    return `
                        <div class="message-item system-message mb-4" data-message-id="${message.id}">
                            <div class="flex justify-center">
                                <div class="bg-neutral-200 dark:bg-dark-700 text-neutral-700 dark:text-dark-200 px-4 py-2 rounded-full text-sm">
                                    ${message.content}
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    return `
                        <div class="message-item user-message mb-4" data-message-id="${message.id}">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 rounded-full bg-primary-600 dark:bg-primary-500 flex items-center justify-center text-white text-sm font-medium">
                                        ${message.authorName.charAt(0).toUpperCase()}
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span class="text-sm font-medium text-neutral-900 dark:text-dark-50">${message.authorName}</span>
                                        <span class="text-xs text-neutral-500 dark:text-dark-400">${message.relativeTime}</span>
                                    </div>
                                    <div class="bg-white dark:bg-dark-800 rounded-lg px-4 py-2 shadow-sm border border-neutral-200 dark:border-dark-700">
                                        <p class="text-sm text-neutral-900 dark:text-dark-50">${message.content}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }
            
            switchProject(projectId) {
                this.currentProjectId = projectId;
                this.loadInitialMessages();
                this.updateChatTitle();
            }
            
            updateChatTitle() {
                const select = $('#projectFilter');
                const selectedText = select.find('option:selected').text();
                $('#chatTitle').text(selectedText === 'All Projects' ? 'General Chat' : `${selectedText} Chat`);
            }
            
            clearMessages() {
                if (confirm('Are you sure you want to clear the chat? This action cannot be undone.')) {
                    this.messages = [];
                    this.renderMessages();
                }
            }
            
            showTyping() {
                $('#typingIndicator').show();
            }
            
            hideTyping() {
                $('#typingIndicator').hide();
            }
            
            scrollToBottom() {
                const container = $('#messagesContainer');
                container.scrollTop(container[0].scrollHeight);
            }
            
            updateConnectionStatus() {
                const statusElement = $('#chatConnectionStatus');
                if (this.isConnected) {
                    statusElement.removeClass('bg-neutral-500 bg-danger-500 text-neutral-100 text-danger-100')
                               .addClass('bg-success-500 text-success-100');
                    statusElement.html('<span class="w-2 h-2 rounded-full bg-success-300 mr-2"></span>Connected');
                } else {
                    statusElement.removeClass('bg-success-500 bg-neutral-500 text-success-100 text-neutral-100')
                               .addClass('bg-danger-500 text-danger-100');
                    statusElement.html('<span class="w-2 h-2 rounded-full bg-danger-300 mr-2 animate-pulse"></span>Disconnected');
                }
            }

            updateOnlineUsers() {
                const container = $('#onlineUsersList');

                if (window.collaboration) {
                    const onlineUsers = window.collaboration.getOnlineUsers();
                    container.empty();

                    $('#onlineCount').text(onlineUsers.length);

                    if (onlineUsers.length === 0) {
                        container.html(`
                            <div class="text-center py-4">
                                <div class="w-12 h-12 rounded-full bg-neutral-100 dark:bg-dark-800 flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-user-slash text-neutral-400 dark:text-dark-500"></i>
                                </div>
                                <p class="text-sm text-neutral-600 dark:text-dark-300">No team members online</p>
                            </div>
                        `);
                        return;
                    }

                    onlineUsers.forEach(user => {
                        container.append(`
                            <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-neutral-50 dark:hover:bg-dark-800 transition-colors duration-200">
                                <div class="relative">
                                    <div class="w-8 h-8 rounded-full bg-primary-600 dark:bg-primary-500 flex items-center justify-center text-white text-sm font-medium">
                                        ${user.userName.charAt(0).toUpperCase()}
                                    </div>
                                    <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full bg-success-500 border-2 border-white dark:border-dark-800"></div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-neutral-900 dark:text-dark-50 truncate">${user.userName}</p>
                                    <p class="text-xs text-neutral-600 dark:text-dark-300 truncate">${user.currentActivity || 'Active in chat'}</p>
                                </div>
                            </div>
                        `);
                    });
                } else {
                    container.html(`
                        <div class="text-center py-4">
                            <div class="w-12 h-12 rounded-full bg-neutral-100 dark:bg-dark-800 flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-wifi-slash text-neutral-400 dark:text-dark-500"></i>
                            </div>
                            <p class="text-sm text-neutral-600 dark:text-dark-300">Connection unavailable</p>
                        </div>
                    `);
                }
            }
            
            updateStatistics() {
                // Update chat statistics
                const todayMessages = this.messages.filter(m => {
                    const messageDate = new Date(m.timestamp);
                    const today = new Date();
                    return messageDate.toDateString() === today.toDateString();
                }).length;
                
                $('#todayMessages').text(todayMessages);
                $('#activeUsers').text($('#onlineCount').text());
            }
            
            getCurrentProjectId() {
                const projectIdElement = document.querySelector('[data-project-id]');
                return projectIdElement ? projectIdElement.dataset.projectId : null;
            }
            
            showLoading() {
                $('#messagesLoading').show();
                $('#messagesList').hide();
                $('#emptyMessages').hide();
            }
            
            hideLoading() {
                $('#messagesLoading').hide();
            }
        }
    </script>
}

@functions {
    private string GetRelativeTime(DateTime timestamp)
    {
        var timeSpan = DateTime.UtcNow - timestamp;
        
        if (timeSpan.TotalMinutes < 1)
            return "Just now";
        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes} minutes ago";
        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours} hours ago";
        if (timeSpan.TotalDays < 7)
            return $"{(int)timeSpan.TotalDays} days ago";
            
        return timestamp.ToString("MMM dd, yyyy");
    }
}
