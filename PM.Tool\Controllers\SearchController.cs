using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Data;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class SearchController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SearchController> _logger;

        public SearchController(
            ApplicationDbContext context,
            ILogger<SearchController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: /Search
        public async Task<IActionResult> Index(string q, int page = 1, int pageSize = 20)
        {
            ViewData["Title"] = "Search Results";
            ViewData["Description"] = !string.IsNullOrWhiteSpace(q) ? $"Search results for \"{q}\"" : "Search across all content";
            ViewData["Icon"] = "fas fa-search";
            ViewData["IconColor"] = "text-primary-600 dark:text-primary-400";

            var viewModel = new SearchResultsViewModel
            {
                Query = q ?? string.Empty,
                CurrentPage = page,
                PageSize = pageSize
            };

            if (string.IsNullOrWhiteSpace(q) || q.Length < 2)
            {
                return View(viewModel);
            }

            try
            {
                var searchTerm = q.ToLower();

                // Search Projects
                var projects = await _context.Projects
                    .Where(p => p.Name.ToLower().Contains(searchTerm) || 
                               p.Description.ToLower().Contains(searchTerm))
                    .Select(p => new SearchResultItemViewModel
                    {
                        Id = p.Id,
                        Title = p.Name,
                        Description = p.Description,
                        Type = "Project",
                        Icon = "fas fa-folder",
                        Url = Url.Action("Details", "Projects", new { id = p.Id }),
                        Metadata = new Dictionary<string, string>
                        {
                            { "Status", p.Status.ToString() },
                            { "Progress", $"{p.ProgressPercentage:F0}%" },
                            { "Created", p.CreatedAt.ToString("MMM dd, yyyy") }
                        }
                    })
                    .ToListAsync();

                // Search Tasks
                var tasks = await _context.Tasks
                    .Include(t => t.Project)
                    .Where(t => t.Title.ToLower().Contains(searchTerm) || 
                               t.Description.ToLower().Contains(searchTerm))
                    .Select(t => new SearchResultItemViewModel
                    {
                        Id = t.Id,
                        Title = t.Title,
                        Description = t.Description,
                        Type = "Task",
                        Icon = "fas fa-tasks",
                        Url = Url.Action("Details", "Tasks", new { id = t.Id }),
                        Metadata = new Dictionary<string, string>
                        {
                            { "Project", t.Project.Name },
                            { "Status", t.Status.ToString() },
                            { "Priority", t.Priority.ToString() },
                            { "Due Date", t.DueDate.HasValue ? t.DueDate.Value.ToString("MMM dd, yyyy") : "No due date" }
                        }
                    })
                    .ToListAsync();

                // Search People
                var people = await _context.People
                    .Where(p => p.FirstName.ToLower().Contains(searchTerm) ||
                               p.LastName.ToLower().Contains(searchTerm) ||
                               p.Email.ToLower().Contains(searchTerm) ||
                               (p.Organization != null && p.Organization.ToLower().Contains(searchTerm)))
                    .Select(p => new SearchResultItemViewModel
                    {
                        Id = p.Id,
                        Title = $"{p.FirstName} {p.LastName}",
                        Description = $"{p.Title} - {p.Organization}",
                        Type = "Person",
                        Icon = "fas fa-user",
                        Url = Url.Action("Details", "Person", new { id = p.Id }),
                        Metadata = new Dictionary<string, string>
                        {
                            { "Email", p.Email },
                            { "Organization", p.Organization ?? "N/A" },
                            { "Department", p.Department ?? "N/A" }
                        }
                    })
                    .ToListAsync();

                // Search Meetings
                var meetings = await _context.Meetings
                    .Where(m => m.Title.ToLower().Contains(searchTerm) ||
                               m.Description.ToLower().Contains(searchTerm))
                    .Select(m => new SearchResultItemViewModel
                    {
                        Id = m.Id,
                        Title = m.Title,
                        Description = m.Description,
                        Type = "Meeting",
                        Icon = "fas fa-video",
                        Url = Url.Action("Details", "Meeting", new { id = m.Id }),
                        Metadata = new Dictionary<string, string>
                        {
                            { "Date", m.ScheduledDate.ToString("MMM dd, yyyy") },
                            { "Time", $"{m.ScheduledDate:HH:mm} - {m.EndTime:HH:mm}" },
                            { "Status", m.Status.ToString() }
                        }
                    })
                    .ToListAsync();

                // Search Requirements
                var requirements = await _context.Requirements
                    .Include(r => r.Project)
                    .Where(r => r.Title.ToLower().Contains(searchTerm) ||
                               r.Description.ToLower().Contains(searchTerm))
                    .Select(r => new SearchResultItemViewModel
                    {
                        Id = r.Id,
                        Title = r.Title,
                        Description = r.Description,
                        Type = "Requirement",
                        Icon = "fas fa-list-check",
                        Url = Url.Action("Details", "Requirement", new { id = r.Id }),
                        Metadata = new Dictionary<string, string>
                        {
                            { "Project", r.Project.Name },
                            { "Status", r.Status.ToString() },
                            { "Priority", r.Priority.ToString() }
                        }
                    })
                    .ToListAsync();

                // Combine all results
                var allResults = new List<SearchResultItemViewModel>();
                allResults.AddRange(projects);
                allResults.AddRange(tasks);
                allResults.AddRange(people);
                allResults.AddRange(meetings);
                allResults.AddRange(requirements);

                // Apply pagination
                var totalResults = allResults.Count;
                var pagedResults = allResults
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                viewModel.Results = pagedResults;
                viewModel.TotalResults = totalResults;
                viewModel.TotalPages = (int)Math.Ceiling((double)totalResults / pageSize);

                // Group results by type for display
                viewModel.ResultsByType = allResults
                    .GroupBy(r => r.Type)
                    .ToDictionary(g => g.Key, g => g.Count());

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing search for query: {Query}", q);
                ViewBag.ErrorMessage = "An error occurred while searching. Please try again.";
                return View(viewModel);
            }
        }
    }
}
