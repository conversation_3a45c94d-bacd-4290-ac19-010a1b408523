using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Localization;
using PM.Tool.Services;
using PM.Tool.Models.ViewModels;
using PM.Tool.Core.Entities;
using System.Text.Json;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Data;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class AgileController : SecureBaseController
    {
        private readonly IAgileService _agileService;
        private readonly IProjectService _projectService;
        private readonly ILocalizationService _localizationService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<AgileController> _logger;
        private readonly ApplicationDbContext _context;
        private readonly IFormHelperService _formHelper;

        public AgileController(
            IAgileService agileService,
            IProjectService projectService,
            ILocalizationService localizationService,
            UserManager<ApplicationUser> userManager,
            ILogger<AgileController> logger,
            IFormHelperService formHelper,
            ApplicationDbContext context,
            IAuditService auditService) : base(auditService)
        {
            _agileService = agileService;
            _projectService = projectService;
            _localizationService = localizationService;
            _userManager = userManager;
            _logger = logger;
            _formHelper = formHelper;
            _context = context;
        }

        #region Dashboard and Overview

        // GET: Agile
        public async Task<IActionResult> Index()
        {
            var user = await _userManager.GetUserAsync(User);
            try
            {
                if (user == null) return RedirectToAction("Login", "Account");

                // Get all projects the user has access to
                var userProjects = await _projectService.GetUserProjectsAsync(user.Id);

                // Get all epics from user's projects
                var allEpics = new List<Epic>();
                foreach (var project in userProjects)
                {
                    var projectEpics = await _agileService.GetProjectEpicsAsync(project.Id);
                    allEpics.AddRange(projectEpics);
                }

                // If user has no projects, show empty state
                if (!userProjects.Any())
                {
                    ViewBag.HasProjects = false;
                    return View(new List<Epic>());
                }

                ViewBag.HasProjects = true;
                ViewBag.ProjectCount = userProjects.Count();
                ViewBag.UserProjects = userProjects;

                return View(allEpics.OrderByDescending(e => e.CreatedAt));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading agile dashboard for user {UserId}", user?.Id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return View(new List<Epic>());
            }
        }

        #endregion

        #region Epic Management

        // GET: Agile/Backlog/5
        public async Task<IActionResult> Backlog(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var epics = await _agileService.GetProjectEpicsAsync(projectId);
                var userStories = await _agileService.GetBacklogUserStoriesAsync(projectId);
                var sprints = await _agileService.GetProjectSprintsAsync(projectId);

                ViewBag.Project = project;
                ViewBag.Epics = epics;
                ViewBag.Sprints = sprints;
                ViewBag.ProjectId = projectId;

                return View(userStories);
            }
            catch (Exception ex)
            {
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        // GET: Agile/Kanban/5
        public async Task<IActionResult> Kanban(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var kanbanBoard = await _agileService.GetKanbanBoardAsync(projectId);
                var sprints = await _agileService.GetProjectSprintsAsync(projectId);
                var activeSprint = await _agileService.GetActiveSprintAsync(projectId);

                ViewBag.Project = project;
                ViewBag.Sprints = sprints;
                ViewBag.ActiveSprint = activeSprint;
                ViewBag.ProjectId = projectId;

                return View(kanbanBoard);
            }
            catch (Exception ex)
            {
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        // GET: Agile/Epics/5
        public async Task<IActionResult> Epics(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var epics = await _agileService.GetProjectEpicsAsync(projectId);

                // Get analytics for the project
                var analytics = await _agileService.GetAgileAnalyticsAsync(projectId);
                ViewBag.Analytics = analytics;

                ViewBag.Project = project;
                ViewBag.ProjectId = projectId;

                return View(epics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading epics for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        // GET: Agile/EpicDetails/5
        public async Task<IActionResult> EpicDetails(int id)
        {
            try
            {
                var epic = await _agileService.GetEpicByIdAsync(id);
                if (epic == null) return NotFound();

                var userStories = await _agileService.GetEpicUserStoriesAsync(id);
                ViewBag.UserStories = userStories;
                ViewBag.Project = epic.Project;

                // Calculate epic metrics
                var totalStoryPoints = userStories.Sum(us => us.StoryPoints);
                var completedStoryPoints = userStories.Where(us => us.IsCompleted).Sum(us => us.StoryPoints);
                ViewBag.TotalStoryPoints = totalStoryPoints;
                ViewBag.CompletedStoryPoints = completedStoryPoints;
                ViewBag.ProgressPercentage = totalStoryPoints > 0 ? (completedStoryPoints / totalStoryPoints) * 100 : 0;

                return View(epic);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading epic details for epic {EpicId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // GET: Agile/EditEpic/5
        public async Task<IActionResult> EditEpic(int id)
        {
            try
            {
                var epic = await _agileService.GetEpicByIdAsync(id);
                if (epic == null) return NotFound();

                ViewBag.Project = epic.Project;
                await PopulateEpicDropdowns();

                return View(epic);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit epic form for epic {EpicId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("EpicDetails", new { id });
            }
        }

        // POST: Agile/EditEpic/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditEpic(int id, Epic epic)
        {
            if (id != epic.Id) return NotFound();

            try
            {
                if (ModelState.IsValid)
                {
                    await _agileService.UpdateEpicAsync(epic);
                    await LogAuditAsync(AuditAction.Update, "Epic", epic.Id);

                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                    return RedirectToAction("EpicDetails", new { id = epic.Id });
                }

                var project = await _projectService.GetProjectByIdAsync(epic.ProjectId);
                ViewBag.Project = project;
                await PopulateEpicDropdowns();
                return View(epic);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating epic {EpicId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("EpicDetails", new { id });
            }
        }

        // POST: Agile/DeleteEpic/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteEpic(int id)
        {
            try
            {
                var epic = await _agileService.GetEpicByIdAsync(id);
                if (epic == null) return NotFound();

                var projectId = epic.ProjectId;
                await _agileService.DeleteEpicAsync(id);
                await LogAuditAsync(AuditAction.Delete, "Epic", id);

                TempData["Success"] = _localizationService.GetString("Message.DeleteSuccess");
                return RedirectToAction("Epics", new { projectId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting epic {EpicId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("EpicDetails", new { id });
            }
        }

        // GET: Agile/Sprints/5
        public async Task<IActionResult> Sprints(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var sprints = await _agileService.GetProjectSprintsAsync(projectId);
                var activeSprint = await _agileService.GetActiveSprintAsync(projectId);

                ViewBag.Project = project;
                ViewBag.ActiveSprint = activeSprint;
                ViewBag.ProjectId = projectId;

                return View(sprints);
            }
            catch (Exception ex)
            {
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        // GET: Agile/CreateEpic
        public async Task<IActionResult> CreateEpic(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var viewModel = new EpicCreateViewModel { ProjectId = projectId };

                ViewBag.Project = project;
                await PopulateEpicDropdowns();

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create epic form for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Epics", new { projectId });
            }
        }

        // POST: Agile/CreateEpic
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateEpic(EpicCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<EpicCreateViewModel, Epic>(
                viewModel,
                async (epic) => {
                    var user = await _userManager.GetUserAsync(User);
                    epic.OwnerId = user?.Id;

                    // Generate epic key
                    var existingEpics = await _agileService.GetProjectEpicsAsync(epic.ProjectId);
                    epic.EpicKey = $"EP-{existingEpics.Count() + 1:D3}";

                    var createdEpic = await _agileService.CreateEpicAsync(epic);
                    await LogAuditAsync(AuditAction.Create, "Epic", createdEpic.Id);
                    return createdEpic;
                },
                epic => epic.Id,
                PopulateEpicDropdowns,
                _formHelper,
                _logger,
                "Epic",
                "EpicDetails"
            );
        }

        // GET: Agile/CreateUserStory
        public async Task<IActionResult> CreateUserStory(int projectId, int? epicId, int? sprintId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var viewModel = new UserStoryCreateViewModel
                {
                    ProjectId = projectId,
                    EpicId = epicId,
                    SprintId = sprintId
                };

                ViewBag.Project = project;
                await PopulateUserStoryDropdowns(projectId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Backlog", new { projectId });
            }
        }

        // POST: Agile/CreateUserStory
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateUserStory(UserStoryCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<UserStoryCreateViewModel, UserStory>(
                viewModel,
                async (userStory) => {
                    var user = await _userManager.GetUserAsync(User);
                    if (string.IsNullOrEmpty(userStory.AssignedToUserId))
                    {
                        userStory.AssignedToUserId = user?.Id;
                    }

                    // Generate user story key
                    var existingUserStories = await _agileService.GetProjectUserStoriesAsync(userStory.ProjectId);
                    userStory.StoryKey = $"US-{existingUserStories.Count() + 1:D3}";

                    var createdUserStory = await _agileService.CreateUserStoryAsync(userStory);
                    await LogAuditAsync(AuditAction.Create, "UserStory", createdUserStory.Id);
                    return createdUserStory;
                },
                userStory => userStory.Id,
                async () => await PopulateUserStoryDropdowns(viewModel.ProjectId),
                _formHelper,
                _logger,
                "UserStory",
                "UserStoryDetails"
            );
        }

        // GET: Agile/UserStoryDetails/5
        public async Task<IActionResult> UserStoryDetails(int id)
        {
            try
            {
                var userStory = await _agileService.GetUserStoryByIdAsync(id);
                if (userStory == null) return NotFound();

                var comments = await _agileService.GetUserStoryCommentsAsync(id);
                ViewBag.Comments = comments;
                ViewBag.Project = userStory.Project;

                return View(userStory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading user story details for story {UserStoryId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // GET: Agile/EditUserStory/5
        public async Task<IActionResult> EditUserStory(int id)
        {
            try
            {
                var userStory = await _agileService.GetUserStoryByIdAsync(id);
                if (userStory == null) return NotFound();

                ViewBag.Project = userStory.Project;
                await PopulateUserStoryDropdowns(userStory.ProjectId);

                return View(userStory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit user story form for story {UserStoryId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("UserStoryDetails", new { id });
            }
        }

        // POST: Agile/EditUserStory/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditUserStory(int id, UserStory userStory)
        {
            if (id != userStory.Id) return NotFound();

            try
            {
                if (ModelState.IsValid)
                {
                    await _agileService.UpdateUserStoryAsync(userStory);
                    await LogAuditAsync(AuditAction.Update, "UserStory", userStory.Id);

                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                    return RedirectToAction("UserStoryDetails", new { id = userStory.Id });
                }

                var project = await _projectService.GetProjectByIdAsync(userStory.ProjectId);
                ViewBag.Project = project;
                await PopulateUserStoryDropdowns(userStory.ProjectId);
                return View(userStory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user story {UserStoryId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("UserStoryDetails", new { id });
            }
        }

        // POST: Agile/DeleteUserStory/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteUserStory(int id)
        {
            try
            {
                var userStory = await _agileService.GetUserStoryByIdAsync(id);
                if (userStory == null) return NotFound();

                var projectId = userStory.ProjectId;
                await _agileService.DeleteUserStoryAsync(id);
                await LogAuditAsync(AuditAction.Delete, "UserStory", id);

                TempData["Success"] = _localizationService.GetString("Message.DeleteSuccess");
                return RedirectToAction("Backlog", new { projectId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user story {UserStoryId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("UserStoryDetails", new { id });
            }
        }

        #endregion

        #region Feature Management

        // GET: Agile/Features/5
        public async Task<IActionResult> Features(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var features = await _agileService.GetProjectFeaturesAsync(projectId);
                var epics = await _agileService.GetProjectEpicsAsync(projectId);

                ViewBag.Project = project;
                ViewBag.Epics = epics;
                ViewBag.ProjectId = projectId;

                return View(features);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading features for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        // GET: Agile/FeatureDetails/5
        public async Task<IActionResult> FeatureDetails(int id)
        {
            try
            {
                var feature = await _agileService.GetFeatureByIdAsync(id);
                if (feature == null) return NotFound();

                var userStories = await _agileService.GetProjectUserStoriesAsync(feature.ProjectId);
                var featureUserStories = userStories.Where(us => us.FeatureId == id);

                ViewBag.UserStories = featureUserStories;
                ViewBag.ProjectId = feature.ProjectId;

                return View(feature);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading feature details for feature {FeatureId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // GET: Agile/CreateFeature
        public async Task<IActionResult> CreateFeature(int projectId, int? epicId = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var feature = new Feature
                {
                    ProjectId = projectId,
                    EpicId = epicId ?? 0
                };

                await PopulateFeatureDropdowns(projectId);
                ViewBag.Project = project;

                return View(feature);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create feature form for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Features", new { projectId });
            }
        }

        // POST: Agile/CreateFeature
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateFeature(Feature feature)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var user = await _userManager.GetUserAsync(User);
                    feature.OwnerId = user?.Id;

                    var createdFeature = await _agileService.CreateFeatureAsync(feature);
                    await LogAuditAsync(AuditAction.Create, "Feature", createdFeature.Id);

                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                    return RedirectToAction("FeatureDetails", new { id = createdFeature.Id });
                }

                await PopulateFeatureDropdowns(feature.ProjectId);
                var project = await _projectService.GetProjectByIdAsync(feature.ProjectId);
                ViewBag.Project = project;
                return View(feature);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating feature for project {ProjectId}", feature.ProjectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Features", new { projectId = feature.ProjectId });
            }
        }

        // GET: Agile/EditFeature/5
        public async Task<IActionResult> EditFeature(int id)
        {
            try
            {
                var feature = await _agileService.GetFeatureByIdAsync(id);
                if (feature == null) return NotFound();

                await PopulateFeatureDropdowns(feature.ProjectId);
                ViewBag.Project = await _projectService.GetProjectByIdAsync(feature.ProjectId);

                return View(feature);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit feature form for feature {FeatureId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // POST: Agile/EditFeature/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditFeature(int id, Feature feature)
        {
            if (id != feature.Id) return NotFound();

            try
            {
                if (ModelState.IsValid)
                {
                    await _agileService.UpdateFeatureAsync(feature);
                    await LogAuditAsync(AuditAction.Update, "Feature", feature.Id);

                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                    return RedirectToAction("FeatureDetails", new { id = feature.Id });
                }

                await PopulateFeatureDropdowns(feature.ProjectId);
                ViewBag.Project = await _projectService.GetProjectByIdAsync(feature.ProjectId);
                return View(feature);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating feature {FeatureId}", feature.Id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("FeatureDetails", new { id = feature.Id });
            }
        }

        // POST: Agile/DeleteFeature/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteFeature(int id)
        {
            try
            {
                var feature = await _agileService.GetFeatureByIdAsync(id);
                if (feature == null) return NotFound();

                var projectId = feature.ProjectId;
                var success = await _agileService.DeleteFeatureAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Delete, "Feature", id);
                    TempData["Success"] = _localizationService.GetString("Message.DeleteSuccess");
                }
                else
                {
                    TempData["Error"] = _localizationService.GetString("Message.DeleteError");
                }

                return RedirectToAction("Features", new { projectId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting feature {FeatureId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        #endregion

        #region Bug Management

        // GET: Agile/Bugs/5
        public async Task<IActionResult> Bugs(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var bugs = await _agileService.GetProjectBugsAsync(projectId);
                var features = await _agileService.GetProjectFeaturesAsync(projectId);
                var userStories = await _agileService.GetProjectUserStoriesAsync(projectId);

                ViewBag.Project = project;
                ViewBag.Features = features;
                ViewBag.UserStories = userStories;
                ViewBag.ProjectId = projectId;

                return View(bugs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading bugs for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        // GET: Agile/BugDetails/5
        public async Task<IActionResult> BugDetails(int id)
        {
            try
            {
                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null) return NotFound();

                var comments = await _agileService.GetBugCommentsAsync(id);
                ViewBag.Comments = comments;
                ViewBag.ProjectId = bug.ProjectId;

                return View(bug);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading bug details for bug {BugId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // GET: Agile/CreateBug
        public async Task<IActionResult> CreateBug(int projectId, int? featureId = null, int? userStoryId = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var bug = new Bug
                {
                    ProjectId = projectId,
                    FeatureId = featureId,
                    UserStoryId = userStoryId,
                    Severity = BugSeverity.Medium,
                    Priority = BugPriority.Medium,
                    Status = BugStatus.New
                };

                await PopulateBugDropdowns(projectId);
                ViewBag.Project = project;

                return View(bug);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create bug form for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Bugs", new { projectId });
            }
        }

        // POST: Agile/CreateBug
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateBug(Bug bug)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var user = await _userManager.GetUserAsync(User);
                    bug.ReportedByUserId = user?.Id;

                    var createdBug = await _agileService.CreateBugAsync(bug);
                    await LogAuditAsync(AuditAction.Create, "Bug", createdBug.Id);

                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                    return RedirectToAction("BugDetails", new { id = createdBug.Id });
                }

                await PopulateBugDropdowns(bug.ProjectId);
                var project = await _projectService.GetProjectByIdAsync(bug.ProjectId);
                ViewBag.Project = project;
                return View(bug);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating bug for project {ProjectId}", bug.ProjectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Bugs", new { projectId = bug.ProjectId });
            }
        }

        // GET: Agile/EditBug/5
        public async Task<IActionResult> EditBug(int id)
        {
            try
            {
                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null) return NotFound();

                await PopulateBugDropdowns(bug.ProjectId);
                ViewBag.Project = await _projectService.GetProjectByIdAsync(bug.ProjectId);

                return View(bug);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit bug form for bug {BugId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // POST: Agile/EditBug/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditBug(int id, Bug bug)
        {
            if (id != bug.Id) return NotFound();

            try
            {
                if (ModelState.IsValid)
                {
                    await _agileService.UpdateBugAsync(bug);
                    await LogAuditAsync(AuditAction.Update, "Bug", bug.Id);

                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                    return RedirectToAction("BugDetails", new { id = bug.Id });
                }

                await PopulateBugDropdowns(bug.ProjectId);
                ViewBag.Project = await _projectService.GetProjectByIdAsync(bug.ProjectId);
                return View(bug);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating bug {BugId}", bug.Id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("BugDetails", new { id = bug.Id });
            }
        }

        // POST: Agile/DeleteBug/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteBug(int id)
        {
            try
            {
                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null) return NotFound();

                var projectId = bug.ProjectId;
                var success = await _agileService.DeleteBugAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Delete, "Bug", id);
                    TempData["Success"] = _localizationService.GetString("Message.DeleteSuccess");
                }
                else
                {
                    TempData["Error"] = _localizationService.GetString("Message.DeleteError");
                }

                return RedirectToAction("Bugs", new { projectId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting bug {BugId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // POST: Agile/AssignBug/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AssignBug(int id, string userId)
        {
            try
            {
                var success = await _agileService.AssignBugAsync(id, userId);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Bug", id);
                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                }
                else
                {
                    TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                }

                return RedirectToAction("BugDetails", new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning bug {BugId} to user {UserId}", id, userId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("BugDetails", new { id });
            }
        }

        // POST: Agile/ResolveBug/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResolveBug(int id, string resolution)
        {
            try
            {
                var success = await _agileService.ResolveBugAsync(id, resolution);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Bug", id);
                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                }
                else
                {
                    TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                }

                return RedirectToAction("BugDetails", new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving bug {BugId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("BugDetails", new { id });
            }
        }

        #endregion

        #region Test Case Management

        // GET: Agile/TestCases/5
        public async Task<IActionResult> TestCases(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var testCases = await _agileService.GetProjectTestCasesAsync(projectId);
                var features = await _agileService.GetProjectFeaturesAsync(projectId);
                var userStories = await _agileService.GetProjectUserStoriesAsync(projectId);

                ViewBag.Project = project;
                ViewBag.Features = features;
                ViewBag.UserStories = userStories;
                ViewBag.ProjectId = projectId;

                return View(testCases);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading test cases for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        // GET: Agile/TestCaseDetails/5
        public async Task<IActionResult> TestCaseDetails(int id)
        {
            try
            {
                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null) return NotFound();

                var executions = await _agileService.GetTestCaseExecutionsAsync(id);
                ViewBag.Executions = executions;
                ViewBag.ProjectId = testCase.ProjectId;

                return View(testCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading test case details for test case {TestCaseId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // GET: Agile/CreateTestCase
        public async Task<IActionResult> CreateTestCase(int projectId, int? featureId = null, int? userStoryId = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var testCase = new TestCase
                {
                    ProjectId = projectId,
                    FeatureId = featureId,
                    UserStoryId = userStoryId,
                    Type = TestCaseType.Functional,
                    Priority = TestCasePriority.Medium,
                    Status = TestCaseStatus.Draft
                };

                await PopulateTestCaseDropdowns(projectId);
                ViewBag.Project = project;

                return View(testCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create test case form for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("TestCases", new { projectId });
            }
        }

        // POST: Agile/CreateTestCase
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateTestCase(TestCase testCase)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var user = await _userManager.GetUserAsync(User);
                    testCase.CreatedByUserId = user?.Id;

                    var createdTestCase = await _agileService.CreateTestCaseAsync(testCase);
                    await LogAuditAsync(AuditAction.Create, "TestCase", createdTestCase.Id);

                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                    return RedirectToAction("TestCaseDetails", new { id = createdTestCase.Id });
                }

                await PopulateTestCaseDropdowns(testCase.ProjectId);
                var project = await _projectService.GetProjectByIdAsync(testCase.ProjectId);
                ViewBag.Project = project;
                return View(testCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating test case for project {ProjectId}", testCase.ProjectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("TestCases", new { projectId = testCase.ProjectId });
            }
        }

        // GET: Agile/EditTestCase/5
        public async Task<IActionResult> EditTestCase(int id)
        {
            try
            {
                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null) return NotFound();

                await PopulateTestCaseDropdowns(testCase.ProjectId);
                ViewBag.Project = await _projectService.GetProjectByIdAsync(testCase.ProjectId);

                return View(testCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit test case form for test case {TestCaseId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // POST: Agile/EditTestCase/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditTestCase(int id, TestCase testCase)
        {
            if (id != testCase.Id) return NotFound();

            try
            {
                if (ModelState.IsValid)
                {
                    await _agileService.UpdateTestCaseAsync(testCase);
                    await LogAuditAsync(AuditAction.Update, "TestCase", testCase.Id);

                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                    return RedirectToAction("TestCaseDetails", new { id = testCase.Id });
                }

                await PopulateTestCaseDropdowns(testCase.ProjectId);
                ViewBag.Project = await _projectService.GetProjectByIdAsync(testCase.ProjectId);
                return View(testCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating test case {TestCaseId}", testCase.Id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("TestCaseDetails", new { id = testCase.Id });
            }
        }

        // POST: Agile/DeleteTestCase/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteTestCase(int id)
        {
            try
            {
                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null) return NotFound();

                var projectId = testCase.ProjectId;
                var success = await _agileService.DeleteTestCaseAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Delete, "TestCase", id);
                    TempData["Success"] = _localizationService.GetString("Message.DeleteSuccess");
                }
                else
                {
                    TempData["Error"] = _localizationService.GetString("Message.DeleteError");
                }

                return RedirectToAction("TestCases", new { projectId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting test case {TestCaseId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // POST: Agile/ExecuteTestCase/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExecuteTestCase(int id, TestExecutionResult result, string actualResult, string notes = "")
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                var execution = new TestExecution
                {
                    TestCaseId = id,
                    Result = result,
                    ActualResult = actualResult,
                    Notes = notes,
                    ExecutedByUserId = user?.Id
                };

                var createdExecution = await _agileService.CreateTestExecutionAsync(execution);
                await LogAuditAsync(AuditAction.Create, "TestExecution", createdExecution.Id);

                TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                return RedirectToAction("TestCaseDetails", new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing test case {TestCaseId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("TestCaseDetails", new { id });
            }
        }

        #endregion

        #region Sprint Planning

        // GET: Agile/SprintPlanning/5
        public async Task<IActionResult> SprintPlanning(int projectId, int? sprintId = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                // Get backlog user stories (not assigned to any sprint)
                var backlogStories = await _agileService.GetBacklogUserStoriesAsync(projectId);

                // Get all project sprints
                var sprints = await _agileService.GetProjectSprintsAsync(projectId);

                // Get current sprint or create a new one for planning
                Sprint currentSprint = null;
                if (sprintId.HasValue)
                {
                    currentSprint = await _agileService.GetSprintByIdAsync(sprintId.Value);
                }
                else
                {
                    // Get the most recent active or planning sprint
                    currentSprint = sprints.FirstOrDefault(s => s.Status == SprintStatus.Active || s.Status == SprintStatus.Planning);
                }

                // Get sprint user stories if we have a current sprint
                var sprintStories = currentSprint != null
                    ? await _agileService.GetSprintUserStoriesAsync(currentSprint.Id)
                    : new List<UserStory>();

                var viewModel = new SprintPlanningViewModel
                {
                    ProjectId = projectId,
                    Project = project,
                    BacklogStories = backlogStories.Where(s => s.SprintId == null).ToList(),
                    CurrentSprint = currentSprint,
                    SprintStories = sprintStories.ToList(),
                    AllSprints = sprints.ToList()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading sprint planning for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Sprints", new { projectId });
            }
        }

        #endregion

        #region Kanban Board

        // GET: Agile/Board/5 - Alias for Kanban with sprint support
        public async Task<IActionResult> Board(int projectId, int? sprintId = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                // If sprintId is provided, set it in ViewBag for the board to filter by sprint
                if (sprintId.HasValue)
                {
                    var sprint = await _agileService.GetSprintByIdAsync(sprintId.Value);
                    ViewBag.SelectedSprint = sprint;
                    ViewBag.SprintId = sprintId.Value;
                }

                // Redirect to existing Kanban action with additional parameters
                return RedirectToAction("Kanban", new { projectId, sprintId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading kanban board for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        #endregion

        #region Burndown Charts

        // GET: Agile/Burndown/5 - Dedicated burndown charts view
        public async Task<IActionResult> Burndown(int projectId, int? sprintId = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var sprints = await _agileService.GetProjectSprintsAsync(projectId);
                var activeSprint = await _agileService.GetActiveSprintAsync(projectId);

                // Use provided sprintId or default to active sprint
                var selectedSprint = sprintId.HasValue
                    ? sprints.FirstOrDefault(s => s.Id == sprintId.Value)
                    : activeSprint;

                ViewBag.Project = project;
                ViewBag.Sprints = sprints;
                ViewBag.SelectedSprint = selectedSprint;
                ViewBag.ProjectId = projectId;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading burndown charts for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        #endregion

        #region Sprint Management

        // GET: Agile/CreateSprint
        public async Task<IActionResult> CreateSprint(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var sprint = new Sprint
                {
                    ProjectId = projectId,
                    StartDate = DateTime.Today,
                    EndDate = DateTime.Today.AddDays(14) // Default 2-week sprint
                };

                ViewBag.Project = project;

                return View(sprint);
            }
            catch (Exception ex)
            {
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Sprints", new { projectId });
            }
        }

        // POST: Agile/CreateSprint
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateSprint(Sprint sprint)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var user = await _userManager.GetUserAsync(User);
                    sprint.ScrumMasterId = user?.Id;

                    // Generate sprint key
                    var existingSprints = await _agileService.GetProjectSprintsAsync(sprint.ProjectId);
                    sprint.SprintKey = $"SP-{existingSprints.Count() + 1:D3}";

                    var createdSprint = await _agileService.CreateSprintAsync(sprint);
                    await LogAuditAsync(AuditAction.Create, "Sprint", createdSprint.Id);

                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                    return RedirectToAction("SprintDetails", new { id = createdSprint.Id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating sprint for project {ProjectId}", sprint.ProjectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
            }

            var project = await _projectService.GetProjectByIdAsync(sprint.ProjectId);
            ViewBag.Project = project;
            return View(sprint);
        }

        // GET: Agile/SprintDetails/5
        public async Task<IActionResult> SprintDetails(int id)
        {
            try
            {
                var sprint = await _agileService.GetSprintByIdAsync(id);
                if (sprint == null) return NotFound();

                var userStories = await _agileService.GetSprintUserStoriesAsync(id);
                var sprintMetrics = await _agileService.GetSprintMetricsAsync(id);
                var burndownData = await _agileService.GetBurndownDataAsync(id);

                ViewBag.UserStories = userStories;
                ViewBag.SprintMetrics = sprintMetrics;
                ViewBag.BurndownData = burndownData;
                ViewBag.Project = sprint.Project;

                return View(sprint);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading sprint details for sprint {SprintId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Index");
            }
        }

        // GET: Agile/EditSprint/5
        public async Task<IActionResult> EditSprint(int id)
        {
            try
            {
                var sprint = await _agileService.GetSprintByIdAsync(id);
                if (sprint == null) return NotFound();

                ViewBag.Project = sprint.Project;
                await PopulateSprintDropdowns();

                return View(sprint);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit sprint form for sprint {SprintId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("SprintDetails", new { id });
            }
        }

        // POST: Agile/EditSprint/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditSprint(int id, Sprint sprint)
        {
            if (id != sprint.Id) return NotFound();

            try
            {
                if (ModelState.IsValid)
                {
                    await _agileService.UpdateSprintAsync(sprint);
                    await LogAuditAsync(AuditAction.Update, "Sprint", sprint.Id);

                    TempData["Success"] = _localizationService.GetString("Message.SaveSuccess");
                    return RedirectToAction("SprintDetails", new { id = sprint.Id });
                }

                var project = await _projectService.GetProjectByIdAsync(sprint.ProjectId);
                ViewBag.Project = project;
                await PopulateSprintDropdowns();
                return View(sprint);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating sprint {SprintId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("SprintDetails", new { id });
            }
        }

        // POST: Agile/DeleteSprint/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteSprint(int id)
        {
            try
            {
                var sprint = await _agileService.GetSprintByIdAsync(id);
                if (sprint == null) return NotFound();

                var projectId = sprint.ProjectId;
                await _agileService.DeleteSprintAsync(id);
                await LogAuditAsync(AuditAction.Delete, "Sprint", id);

                TempData["Success"] = _localizationService.GetString("Message.DeleteSuccess");
                return RedirectToAction("Sprints", new { projectId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting sprint {SprintId}", id);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("SprintDetails", new { id });
            }
        }

        // POST: Agile/StartSprint/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> StartSprint(int id)
        {
            try
            {
                var success = await _agileService.StartSprintAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.ChangeStatus, "Sprint", id);
                    TempData["Success"] = _localizationService.GetString("Agile.SprintStarted");
                }
                else
                {
                    TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
            }

            return RedirectToAction("Sprints");
        }

        // POST: Agile/CompleteSprint/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CompleteSprint(int id)
        {
            try
            {
                var success = await _agileService.CompleteSprintAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.ChangeStatus, "Sprint", id);
                    TempData["Success"] = _localizationService.GetString("Agile.SprintCompleted");
                }
                else
                {
                    TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
            }

            return RedirectToAction("Sprints");
        }

        // GET: Agile/Analytics/5
        public async Task<IActionResult> Analytics(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var analytics = await _agileService.GetAgileAnalyticsAsync(projectId);
                var velocityData = await _agileService.GetVelocityDataAsync(projectId);
                var epicProgress = await _agileService.GetEpicProgressReportAsync(projectId);
                var teamPerformance = await _agileService.GetTeamPerformanceMetricsAsync(projectId);
                var qualityMetrics = await _agileService.GetQualityMetricsAsync(projectId);

                ViewBag.Project = project;
                ViewBag.Analytics = analytics;
                ViewBag.VelocityData = velocityData;
                ViewBag.EpicProgress = epicProgress;
                ViewBag.TeamPerformance = teamPerformance;
                ViewBag.QualityMetrics = qualityMetrics;
                ViewBag.ProjectId = projectId;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading agile analytics for project {ProjectId}", projectId);
                TempData["Error"] = _localizationService.GetString("Message.ErrorOccurred");
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        // API Endpoints for Analytics Data
        [HttpGet]
        public async Task<IActionResult> GetSprintAnalytics(int sprintId)
        {
            try
            {
                var analytics = await _agileService.GetSprintAnalyticsAsync(sprintId);
                return Json(analytics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sprint analytics for sprint {SprintId}", sprintId);
                return Json(new { error = "Error loading sprint analytics" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetBurnupData(int sprintId)
        {
            try
            {
                var burnupData = await _agileService.GetBurnupDataAsync(sprintId);
                return Json(burnupData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting burnup data for sprint {SprintId}", sprintId);
                return Json(new { error = "Error loading burnup data" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetTeamPerformanceData(int projectId)
        {
            try
            {
                var teamData = await _agileService.GetTeamPerformanceMetricsAsync(projectId);
                return Json(teamData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting team performance for project {ProjectId}", projectId);
                return Json(new { error = "Error loading team performance data" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetQualityData(int projectId)
        {
            try
            {
                var qualityData = await _agileService.GetQualityMetricsAsync(projectId);
                return Json(qualityData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting quality metrics for project {ProjectId}", projectId);
                return Json(new { error = "Error loading quality data" });
            }
        }

        // Export Analytics
        [HttpGet]
        public async Task<IActionResult> ExportAnalytics(int projectId, string format = "pdf")
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var analytics = await _agileService.GetAgileAnalyticsAsync(projectId);
                var velocityData = await _agileService.GetVelocityDataAsync(projectId);
                var teamPerformance = await _agileService.GetTeamPerformanceMetricsAsync(projectId);
                var qualityMetrics = await _agileService.GetQualityMetricsAsync(projectId);

                var exportData = new
                {
                    Project = project,
                    Analytics = analytics,
                    VelocityData = velocityData,
                    TeamPerformance = teamPerformance,
                    QualityMetrics = qualityMetrics,
                    ExportDate = DateTime.UtcNow
                };

                if (format.ToLower() == "excel")
                {
                    var excelBytes = await GenerateExcelReport(exportData);
                    return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                               $"Analytics_{project.Name}_{DateTime.UtcNow:yyyyMMdd}.xlsx");
                }
                else
                {
                    var pdfBytes = await GeneratePdfReport(exportData);
                    return File(pdfBytes, "application/pdf",
                               $"Analytics_{project.Name}_{DateTime.UtcNow:yyyyMMdd}.pdf");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting analytics for project {ProjectId}", projectId);
                TempData["Error"] = "Error exporting analytics.";
                return RedirectToAction("Analytics", new { projectId });
            }
        }

        // Helper methods for export functionality
        private async Task<byte[]> GenerateExcelReport(object exportData)
        {
            // For now, return a simple CSV-like format
            // In a full implementation, you would use a library like EPPlus or ClosedXML
            var csv = "Analytics Report\n";
            csv += $"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}\n\n";
            csv += "This is a placeholder for Excel export functionality.\n";
            csv += "In production, this would generate a comprehensive Excel report with charts and data.";

            return System.Text.Encoding.UTF8.GetBytes(csv);
        }

        private async Task<byte[]> GeneratePdfReport(object exportData)
        {
            // For now, return a simple text format
            // In a full implementation, you would use a library like iTextSharp or PdfSharp
            var content = "Analytics Report\n";
            content += $"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}\n\n";
            content += "This is a placeholder for PDF export functionality.\n";
            content += "In production, this would generate a comprehensive PDF report with charts and data.";

            return System.Text.Encoding.UTF8.GetBytes(content);
        }

        // API Endpoints for AJAX calls

        // POST: Agile/MoveCard
        [HttpPost]
        public async Task<IActionResult> MoveCard([FromBody] MoveCardRequest request)
        {
            try
            {
                var success = await _agileService.MoveUserStoryToColumnAsync(request.CardId, request.ColumnId);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "UserStory", request.CardId);

                    // Send real-time notification
                    var realTimeService = HttpContext.RequestServices.GetService<IRealTimeNotificationService>();
                    if (realTimeService != null)
                    {
                        var task = await _context.Tasks.FirstOrDefaultAsync(t => t.Id == request.CardId);
                        if (task != null)
                        {
                            // Since KanbanColumns is not in DbContext yet, use a simple column name mapping
                            var columnName = GetColumnName(request.ColumnId);
                            await realTimeService.NotifyTaskUpdatedAsync(
                                request.CardId,
                                "moved",
                                new { ToColumn = columnName },
                                User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "");
                        }
                    }

                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to move card" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error moving card" });
            }
        }

        // GET: Agile/GetKanbanData/5
        [HttpGet]
        public async Task<IActionResult> GetKanbanData(int projectId)
        {
            try
            {
                var kanbanBoard = await _agileService.GetKanbanBoardAsync(projectId);

                // Flatten the dictionary to a list of user stories for the frontend
                var allStories = new List<object>();

                if (kanbanBoard != null)
                {
                    foreach (var column in kanbanBoard)
                    {
                        foreach (var story in column.Value)
                        {
                            allStories.Add(new
                            {
                                id = story.Id,
                                title = story.Title,
                                description = story.Description,
                                status = story.Status.ToString(),
                                priority = story.Priority.ToString(),
                                storyPoints = story.StoryPoints,
                                sprintId = story.SprintId,
                                epic = story.Epic != null ? new { title = story.Epic.Title, epicKey = story.Epic.EpicKey } : null,
                                assignedTo = story.AssignedTo != null ? new { userName = story.AssignedTo.UserName } : null
                            });
                        }
                    }
                }

                return Json(allStories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading kanban data for project {ProjectId}", projectId);
                return Json(new { error = "Failed to load kanban data" });
            }
        }

        // GET: Agile/GetActiveSprints - For AJAX loading active sprints
        [HttpGet]
        public async Task<IActionResult> GetActiveSprints(int? projectId = null)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return Json(new { error = "User not found" });

                List<Sprint> sprints;

                if (projectId.HasValue)
                {
                    // Get sprints for specific project
                    sprints = (await _agileService.GetProjectSprintsAsync(projectId.Value)).ToList();
                }
                else
                {
                    // Get sprints from all user projects
                    var userProjects = await _projectService.GetUserProjectsAsync(user.Id);
                    sprints = new List<Sprint>();

                    foreach (var project in userProjects)
                    {
                        var projectSprints = await _agileService.GetProjectSprintsAsync(project.Id);
                        sprints.AddRange(projectSprints);
                    }
                }

                var sprintData = sprints.Select(s => new
                {
                    id = s.Id,
                    name = s.Name,
                    status = s.Status.ToString(),
                    startDate = s.StartDate.ToString("yyyy-MM-dd"),
                    endDate = s.EndDate.ToString("yyyy-MM-dd")
                }).ToList();

                return Json(sprintData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading active sprints for project {ProjectId}", projectId);
                return Json(new { error = "Failed to load sprints" });
            }
        }

        // GET: Agile/GetSprintUserStories - For AJAX loading sprint user stories
        [HttpGet]
        public async Task<IActionResult> GetSprintUserStories(int sprintId)
        {
            try
            {
                var userStories = await _agileService.GetSprintUserStoriesAsync(sprintId);

                var storyData = userStories.Select(s => new
                {
                    id = s.Id,
                    title = s.Title,
                    description = s.Description,
                    status = s.Status.ToString(),
                    priority = s.Priority.ToString(),
                    storyPoints = s.StoryPoints,
                    sprintId = s.SprintId,
                    epic = s.Epic != null ? new { title = s.Epic.Title, epicKey = s.Epic.EpicKey } : null,
                    assignedTo = s.AssignedTo != null ? new { userName = s.AssignedTo.UserName } : null
                }).ToList();

                return Json(storyData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading user stories for sprint {SprintId}", sprintId);
                return Json(new { error = "Failed to load user stories" });
            }
        }

        // GET: Agile/GetSprints - For AJAX loading in Index view
        [HttpGet]
        public async Task<IActionResult> GetSprints()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return Json(new { error = "User not found" });

                var userProjects = await _projectService.GetUserProjectsAsync(user.Id);
                var allSprints = new List<Sprint>();

                foreach (var project in userProjects)
                {
                    var projectSprints = await _agileService.GetProjectSprintsAsync(project.Id);
                    allSprints.AddRange(projectSprints);
                }

                var sprintsHtml = await RenderPartialViewToStringAsync("_SprintsList", allSprints.OrderByDescending(s => s.CreatedAt));
                return Json(new { html = sprintsHtml });
            }
            catch (Exception ex)
            {
                return Json(new { error = "Failed to load sprints" });
            }
        }

        // GET: Agile/GetBacklog - For AJAX loading in Index view
        [HttpGet]
        public async Task<IActionResult> GetBacklog()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return Json(new { error = "User not found" });

                var userProjects = await _projectService.GetUserProjectsAsync(user.Id);
                var allUserStories = new List<UserStory>();

                foreach (var project in userProjects)
                {
                    var projectUserStories = await _agileService.GetBacklogUserStoriesAsync(project.Id);
                    allUserStories.AddRange(projectUserStories);
                }

                var backlogHtml = await RenderPartialViewToStringAsync("_BacklogList", allUserStories.OrderBy(us => us.Priority));
                return Json(new { html = backlogHtml });
            }
            catch (Exception ex)
            {
                return Json(new { error = "Failed to load backlog" });
            }
        }

        // GET: Agile/GetBurndownData - For AJAX loading in Index view
        [HttpGet]
        public async Task<IActionResult> GetBurndownData(int? sprintId = null)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return Json(new { error = "User not found" });

                if (sprintId.HasValue)
                {
                    // Get actual burndown data for specific sprint
                    var burndownData = await _agileService.GetBurndownDataAsync(sprintId.Value);
                    return Json(new {
                        labels = burndownData.Cast<dynamic>().Select(d => ((DateTime)d.Date).ToString("MMM dd")).ToArray(),
                        datasets = new[]
                        {
                            new
                            {
                                label = "Ideal Burndown",
                                data = burndownData.Cast<dynamic>().Select(d => (decimal)d.IdealBurndown).ToArray(),
                                borderColor = "rgb(75, 192, 192)",
                                backgroundColor = "rgba(75, 192, 192, 0.1)",
                                tension = 0.1
                            },
                            new
                            {
                                label = "Actual Burndown",
                                data = burndownData.Cast<dynamic>().Select(d => (decimal)d.RemainingStoryPoints).ToArray(),
                                borderColor = "rgb(255, 99, 132)",
                                backgroundColor = "rgba(255, 99, 132, 0.1)",
                                tension = 0.1
                            }
                        }
                    });
                }

                // Return sample data for overview
                var sampleData = new
                {
                    labels = new[] { "Day 1", "Day 2", "Day 3", "Day 4", "Day 5", "Day 6", "Day 7" },
                    datasets = new[]
                    {
                        new
                        {
                            label = "Ideal Burndown",
                            data = new[] { 100, 85, 70, 55, 40, 25, 0 },
                            borderColor = "rgb(75, 192, 192)",
                            backgroundColor = "rgba(75, 192, 192, 0.1)",
                            tension = 0.1
                        },
                        new
                        {
                            label = "Actual Burndown",
                            data = new[] { 100, 90, 75, 65, 50, 30, 10 },
                            borderColor = "rgb(255, 99, 132)",
                            backgroundColor = "rgba(255, 99, 132, 0.1)",
                            tension = 0.1
                        }
                    }
                };

                return Json(sampleData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading burndown data for sprint {SprintId}", sprintId);
                return Json(new { error = "Failed to load burndown data" });
            }
        }

        // GET: Agile/GetSprintMetrics - Get sprint metrics for progress tracking
        [HttpGet]
        public async Task<IActionResult> GetSprintMetrics(int sprintId)
        {
            try
            {
                var metrics = await _agileService.GetSprintMetricsAsync(sprintId);
                return Json(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading sprint metrics for sprint {SprintId}", sprintId);
                return Json(new { error = "Failed to load sprint metrics" });
            }
        }

        // GET: Agile/GetCumulativeFlowData - Get cumulative flow diagram data
        [HttpGet]
        public async Task<IActionResult> GetCumulativeFlowData(int projectId, int days = 30)
        {
            try
            {
                var cfdData = await _agileService.GetCumulativeFlowDataAsync(projectId, days);
                return Json(cfdData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading cumulative flow data for project {ProjectId}", projectId);
                return Json(new { error = "Failed to load cumulative flow data" });
            }
        }

        // GET: Agile/GetVelocityData/5
        [HttpGet]
        public async Task<IActionResult> GetVelocityData(int projectId, int numberOfSprints = 6)
        {
            try
            {
                var velocityData = await _agileService.GetVelocityDataAsync(projectId, numberOfSprints);

                return Json(new {
                    labels = velocityData.Cast<dynamic>().Select(v => (string)v.SprintName).ToArray(),
                    datasets = new[]
                    {
                        new
                        {
                            label = "Velocity (Story Points)",
                            data = velocityData.Cast<dynamic>().Select(v => (decimal)v.CompletedStoryPoints).ToArray(),
                            backgroundColor = "rgba(54, 162, 235, 0.6)",
                            borderColor = "rgba(54, 162, 235, 1)",
                            borderWidth = 1
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading velocity data for project {ProjectId}", projectId);
                return Json(new { error = "Failed to load velocity data" });
            }
        }

        // POST: Agile/AddUserStoryToSprint
        [HttpPost]
        public async Task<IActionResult> AddUserStoryToSprint([FromBody] AddToSprintRequest request)
        {
            try
            {
                var success = await _agileService.AddUserStoryToSprintAsync(request.UserStoryId, request.SprintId);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "UserStory", request.UserStoryId);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to add user story to sprint" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding user story {UserStoryId} to sprint {SprintId}", request.UserStoryId, request.SprintId);
                return Json(new { success = false, message = "Error adding user story to sprint" });
            }
        }

        // POST: Agile/RemoveUserStoryFromSprint
        [HttpPost]
        public async Task<IActionResult> RemoveUserStoryFromSprint([FromBody] RemoveFromSprintRequest request)
        {
            try
            {
                var success = await _agileService.RemoveUserStoryFromSprintAsync(request.UserStoryId);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "UserStory", request.UserStoryId);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to remove user story from sprint" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing user story {UserStoryId} from sprint", request.UserStoryId);
                return Json(new { success = false, message = "Error removing user story from sprint" });
            }
        }

        // POST: Agile/EstimateUserStory
        [HttpPost]
        public async Task<IActionResult> EstimateUserStory([FromBody] EstimateRequest request)
        {
            try
            {
                var success = await _agileService.EstimateUserStoryAsync(request.UserStoryId, request.StoryPoints);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "UserStory", request.UserStoryId);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to estimate user story" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error estimating user story {UserStoryId}", request.UserStoryId);
                return Json(new { success = false, message = "Error estimating user story" });
            }
        }

        // POST: Agile/AddComment
        [HttpPost]
        public async Task<IActionResult> AddComment([FromBody] AddCommentRequest request)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return Json(new { success = false, message = "User not found" });

                var comment = await _agileService.AddCommentAsync(request.UserStoryId, user.Id, request.Content, request.Type);

                await LogAuditAsync(AuditAction.Create, "UserStoryComment", comment.Id);

                return Json(new {
                    success = true,
                    comment = new {
                        id = comment.Id,
                        content = comment.Content,
                        authorName = user.UserName,
                        createdAt = comment.CreatedAt.ToString("yyyy-MM-dd HH:mm"),
                        type = comment.Type.ToString()
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding comment to user story {UserStoryId}", request.UserStoryId);
                return Json(new { success = false, message = "Error adding comment" });
            }
        }

        // POST: Agile/ReorderBacklog
        [HttpPost]
        public async Task<IActionResult> ReorderBacklog([FromBody] ReorderRequest request)
        {
            try
            {
                var success = await _agileService.ReorderBacklogAsync(request.ProjectId, request.ItemIds);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Backlog", request.ProjectId);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to reorder backlog" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error reordering backlog" });
            }
        }

        #endregion

        #region Helper Methods

        private string GetColumnName(int columnId)
        {
            return columnId switch
            {
                1 => "Backlog",
                2 => "To Do",
                3 => "In Progress",
                4 => "Review",
                5 => "Done",
                _ => "Unknown"
            };
        }

        private async Task PopulateEpicDropdowns()
        {
            ViewBag.EpicStatuses = _localizationService.GetEnumDisplayNames<EpicStatus>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });

            ViewBag.EpicPriorities = _localizationService.GetEnumDisplayNames<EpicPriority>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });
        }

        private async Task PopulateSprintDropdowns()
        {
            ViewBag.SprintStatuses = _localizationService.GetEnumDisplayNames<SprintStatus>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });
        }

        private async Task PopulateUserStoryDropdowns(int projectId)
        {
            var epics = await _agileService.GetProjectEpicsAsync(projectId);
            var sprints = await _agileService.GetProjectSprintsAsync(projectId);

            ViewBag.Epics = epics.Select(e => new SelectListItem
            {
                Value = e.Id.ToString(),
                Text = e.Title
            });

            ViewBag.Sprints = sprints.Select(s => new SelectListItem
            {
                Value = s.Id.ToString(),
                Text = s.Name
            });

            ViewBag.UserStoryStatuses = _localizationService.GetEnumDisplayNames<UserStoryStatus>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });

            ViewBag.UserStoryPriorities = _localizationService.GetEnumDisplayNames<UserStoryPriority>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });
        }

        private async Task PopulateFeatureDropdowns(int projectId)
        {
            var epics = await _agileService.GetProjectEpicsAsync(projectId);

            ViewBag.Epics = epics.Select(e => new SelectListItem
            {
                Value = e.Id.ToString(),
                Text = e.Title
            });

            ViewBag.FeatureStatuses = _localizationService.GetEnumDisplayNames<FeatureStatus>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });

            ViewBag.FeaturePriorities = _localizationService.GetEnumDisplayNames<FeaturePriority>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });
        }

        private async Task PopulateBugDropdowns(int projectId)
        {
            var features = await _agileService.GetProjectFeaturesAsync(projectId);
            var userStories = await _agileService.GetProjectUserStoriesAsync(projectId);

            ViewBag.Features = features.Select(f => new SelectListItem
            {
                Value = f.Id.ToString(),
                Text = f.Title
            });

            ViewBag.UserStories = userStories.Select(us => new SelectListItem
            {
                Value = us.Id.ToString(),
                Text = us.Title
            });

            ViewBag.BugSeverities = _localizationService.GetEnumDisplayNames<BugSeverity>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });

            ViewBag.BugPriorities = _localizationService.GetEnumDisplayNames<BugPriority>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });

            ViewBag.BugStatuses = _localizationService.GetEnumDisplayNames<BugStatus>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });
        }

        private async Task PopulateTestCaseDropdowns(int projectId)
        {
            var features = await _agileService.GetProjectFeaturesAsync(projectId);
            var userStories = await _agileService.GetProjectUserStoriesAsync(projectId);

            ViewBag.Features = features.Select(f => new SelectListItem
            {
                Value = f.Id.ToString(),
                Text = f.Title
            });

            ViewBag.UserStories = userStories.Select(us => new SelectListItem
            {
                Value = us.Id.ToString(),
                Text = us.Title
            });

            ViewBag.TestCaseTypes = _localizationService.GetEnumDisplayNames<TestCaseType>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });

            ViewBag.TestCasePriorities = _localizationService.GetEnumDisplayNames<TestCasePriority>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });

            ViewBag.TestCaseStatuses = _localizationService.GetEnumDisplayNames<TestCaseStatus>()
                .Select(kvp => new SelectListItem
                {
                    Value = ((int)kvp.Key).ToString(),
                    Text = kvp.Value
                });
        }

        private async Task<string> RenderPartialViewToStringAsync(string viewName, object model)
        {
            // For now, return a simple HTML representation
            // In a full implementation, you would use IViewRenderService or similar
            if (viewName == "_SprintsList")
            {
                var sprints = model as IEnumerable<Sprint>;
                if (sprints?.Any() == true)
                {
                    return $"<div class='alert alert-info'>Found {sprints.Count()} sprints across your projects.</div>";
                }
                return "<div class='alert alert-warning'>No sprints found.</div>";
            }
            else if (viewName == "_BacklogList")
            {
                var userStories = model as IEnumerable<UserStory>;
                if (userStories?.Any() == true)
                {
                    return $"<div class='alert alert-info'>Found {userStories.Count()} user stories in backlog.</div>";
                }
                return "<div class='alert alert-warning'>No user stories found in backlog.</div>";
            }

            return "<div class='alert alert-secondary'>Content loaded successfully.</div>";
        }
    }

    public class MoveCardRequest
    {
        public int CardId { get; set; }
        public int ColumnId { get; set; }
    }

    public class ReorderRequest
    {
        public int ProjectId { get; set; }
        public List<int> ItemIds { get; set; } = new List<int>();
    }

    public class AddToSprintRequest
    {
        public int UserStoryId { get; set; }
        public int SprintId { get; set; }
    }

    public class RemoveFromSprintRequest
    {
        public int UserStoryId { get; set; }
    }

    public class EstimateRequest
    {
        public int UserStoryId { get; set; }
        public decimal StoryPoints { get; set; }
    }

    public class AddCommentRequest
    {
        public int UserStoryId { get; set; }
        public string Content { get; set; } = string.Empty;
        public PM.Tool.Core.Entities.Agile.CommentType Type { get; set; } = PM.Tool.Core.Entities.Agile.CommentType.General;
    }

    #endregion
}
