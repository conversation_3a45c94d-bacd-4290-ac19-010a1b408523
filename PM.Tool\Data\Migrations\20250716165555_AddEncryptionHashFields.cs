﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PM.Tool.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddEncryptionHashFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "EmailHash",
                table: "People",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FirstNameHash",
                table: "People",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastNameHash",
                table: "People",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FirstNameHash",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastNameHash",
                table: "AspNetUsers",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EmailHash",
                table: "People");

            migrationBuilder.DropColumn(
                name: "FirstNameHash",
                table: "People");

            migrationBuilder.DropColumn(
                name: "LastNameHash",
                table: "People");

            migrationBuilder.DropColumn(
                name: "FirstNameHash",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "LastNameHash",
                table: "AspNetUsers");
        }
    }
}
