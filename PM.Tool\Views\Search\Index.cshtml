@model PM.Tool.Models.ViewModels.SearchResultsViewModel
@{
    ViewData["Title"] = "Search Results";
    ViewData["Description"] = !string.IsNullOrWhiteSpace(Model.Query) ? $"Search results for \"{Model.Query}\"" : "Search across all content";
    ViewData["Icon"] = "fas fa-search";
    ViewData["IconColor"] = "text-primary-600 dark:text-primary-400";
    
    ViewData["Actions"] = new object[] {
        new { Text = "Advanced Search", Variant = "outline", Icon = "fas fa-filter", Href = "#" }
    };

    var statsArray = new List<object>
    {
        new { Label = "Total Results", Value = Model.TotalResults.ToString(), Icon = "fas fa-search", Color = "blue" },
        new { Label = "Projects", Value = Model.ResultsByType.GetValueOrDefault("Project", 0).ToString(), Icon = "fas fa-folder", Color = "green" },
        new { Label = "Tasks", Value = Model.ResultsByType.GetValueOrDefault("Task", 0).ToString(), Icon = "fas fa-tasks", Color = "indigo" },
        new { Label = "People", Value = Model.ResultsByType.GetValueOrDefault("Person", 0).ToString(), Icon = "fas fa-user", Color = "purple" }
    };

    ViewData["Stats"] = statsArray.ToArray();
}
<partial name="Components/_PageHeader" view-data="ViewData" />

<!-- Search Form -->
<div class="card-custom mb-8">
    <div class="card-body-custom">
        <form method="get" class="flex items-center space-x-4">
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-dark-500"></i>
                    </div>
                    <input type="text" 
                           name="q" 
                           value="@Model.Query"
                           class="form-input-custom pl-10 pr-4 py-3 w-full rounded-xl border-neutral-300 dark:border-neutral-600 focus:border-primary-500 focus:ring-primary-500"
                           placeholder="Search projects, tasks, people, and more..."
                           autofocus>
                </div>
            </div>
            @{
                ViewData["Text"] = "Search";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-search";
                ViewData["Type"] = "submit";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </form>
    </div>
</div>

@if (!string.IsNullOrWhiteSpace(Model.Query))
{
    @if (Model.TotalResults > 0)
    {
        <!-- Results Summary -->
        <div class="mb-6">
            <p class="text-neutral-600 dark:text-neutral-400">
                Showing @<EMAIL> of @Model.TotalResults results for 
                <strong class="text-neutral-900 dark:text-white">"@Model.Query"</strong>
            </p>
        </div>

        <!-- Filter Tabs -->
        @if (Model.ResultsByType.Any())
        {
            <div class="mb-6">
                <div class="border-b border-neutral-200 dark:border-dark-200">
                    <nav class="-mb-px flex space-x-8">
                        <a href="@Url.Action("Index", new { q = Model.Query })" 
                           class="py-2 px-1 border-b-2 border-primary-500 text-primary-600 dark:text-primary-400 font-medium text-sm">
                            All (@Model.TotalResults)
                        </a>
                        @foreach (var type in Model.ResultsByType.OrderByDescending(x => x.Value))
                        {
                            <a href="@Url.Action("Index", new { q = Model.Query, type = type.Key.ToLower() })" 
                               class="py-2 px-1 border-b-2 border-transparent text-neutral-500 dark:text-dark-400 hover:text-neutral-700 dark:hover:text-dark-200 hover:border-neutral-300 dark:hover:border-dark-300 font-medium text-sm">
                                @type.Key (@type.Value)
                            </a>
                        }
                    </nav>
                </div>
            </div>
        }

        <!-- Search Results -->
        <div class="space-y-4 mb-8">
            @foreach (var result in Model.Results)
            {
                <div class="card-custom hover:shadow-md transition-shadow">
                    <div class="card-body-custom">
                        <div class="flex items-start space-x-4">
                            <!-- Icon -->
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 rounded-lg bg-neutral-100 dark:bg-dark-700 flex items-center justify-center">
                                    <i class="@result.Icon text-neutral-600 dark:text-dark-400"></i>
                                </div>
                            </div>
                            
                            <!-- Content -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center space-x-2 mb-2">
                                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">
                                        <a href="@result.Url" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                            @result.Title
                                        </a>
                                    </h3>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                                        @result.Type
                                    </span>
                                </div>
                                
                                @if (!string.IsNullOrEmpty(result.Description))
                                {
                                    <p class="text-neutral-600 dark:text-neutral-400 mb-3 line-clamp-2">
                                        @result.Description
                                    </p>
                                }
                                
                                <!-- Metadata -->
                                @if (result.Metadata.Any())
                                {
                                    <div class="flex flex-wrap gap-4 text-sm text-neutral-500 dark:text-dark-400">
                                        @foreach (var meta in result.Metadata.Where(m => !string.IsNullOrEmpty(m.Value)))
                                        {
                                            <span>
                                                <strong>@meta.Key:</strong> @meta.Value
                                            </span>
                                        }
                                    </div>
                                }
                            </div>
                            
                            <!-- Action -->
                            <div class="flex-shrink-0">
                                <a href="@result.Url" class="btn-outline-custom btn-sm">
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Pagination -->
        @if (Model.TotalPages > 1)
        {
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    @if (Model.HasPreviousPage)
                    {
                        <a href="@Url.Action("Index", new { q = Model.Query, page = Model.CurrentPage - 1 })" class="btn-outline-custom">
                            <i class="fas fa-chevron-left mr-2"></i>
                            Previous
                        </a>
                    }

                    @if (Model.HasNextPage)
                    {
                        <a href="@Url.Action("Index", new { q = Model.Query, page = Model.CurrentPage + 1 })" class="btn-outline-custom">
                            Next
                            <i class="fas fa-chevron-right ml-2"></i>
                        </a>
                    }
                </div>
                
                <div class="text-sm text-neutral-500 dark:text-dark-400">
                    Page @Model.CurrentPage of @Model.TotalPages
                </div>
            </div>
        }
    }
    else
    {
        <!-- No Results -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-search text-neutral-400 dark:text-dark-500 text-3xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-2">No results found</h3>
            <p class="text-neutral-600 dark:text-neutral-400 mb-6">
                We couldn't find anything matching "<strong>@Model.Query</strong>". Try different keywords or check your spelling.
            </p>
            
            <div class="space-y-2 text-sm text-neutral-500 dark:text-dark-400">
                <p><strong>Search tips:</strong></p>
                <ul class="list-disc list-inside space-y-1">
                    <li>Try different keywords</li>
                    <li>Check your spelling</li>
                    <li>Use more general terms</li>
                    <li>Try searching for partial words</li>
                </ul>
            </div>
        </div>
    }
}
else
{
    <!-- Empty State -->
    <div class="text-center py-12">
        <div class="w-24 h-24 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-search text-primary-600 dark:text-primary-400 text-3xl"></i>
        </div>
        <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-2">Search Everything</h3>
        <p class="text-neutral-600 dark:text-neutral-400 mb-6">
            Find projects, tasks, people, meetings, and more across your workspace.
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <div class="text-center p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                <i class="fas fa-folder text-primary-600 dark:text-primary-400 text-xl mb-2"></i>
                <p class="text-sm font-medium text-neutral-900 dark:text-white">Projects</p>
            </div>
            <div class="text-center p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                <i class="fas fa-tasks text-success-600 dark:text-success-400 text-xl mb-2"></i>
                <p class="text-sm font-medium text-neutral-900 dark:text-white">Tasks</p>
            </div>
            <div class="text-center p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                <i class="fas fa-user text-info-600 dark:text-info-400 text-xl mb-2"></i>
                <p class="text-sm font-medium text-neutral-900 dark:text-white">People</p>
            </div>
            <div class="text-center p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                <i class="fas fa-video text-warning-600 dark:text-warning-400 text-xl mb-2"></i>
                <p class="text-sm font-medium text-neutral-900 dark:text-white">Meetings</p>
            </div>
        </div>
    </div>
}
