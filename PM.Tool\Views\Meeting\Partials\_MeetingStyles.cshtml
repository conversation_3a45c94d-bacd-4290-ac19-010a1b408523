<style>
    /* Meeting Management Styles - Following UI Design System */

    /* Page Header Enhancements */
    .page-header {
        background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,250,252,0.9));
        backdrop-filter: blur(10px);
    }

    .dark .page-header {
        background: linear-gradient(135deg, rgba(31,41,55,0.8), rgba(17,24,39,0.9));
    }

    /* Filter Bar Enhancements */
    .filter-bar {
        background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,250,252,0.98));
        backdrop-filter: blur(8px);
    }

    .dark .filter-bar {
        background: linear-gradient(135deg, rgba(31,41,55,0.95), rgba(17,24,39,0.98));
    }

    /* Stats Badge Enhancements */
    .stats-badge {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .stats-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .stats-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s ease;
    }

    .stats-badge:hover::before {
        left: 100%;
    }

    /* Quick Filter Button Enhancements */
    .quick-filter-btn {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .quick-filter-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .quick-filter-btn.active {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        border-color: var(--primary-500);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        transform: translateY(-1px);
    }

    .dark .quick-filter-btn.active {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        border-color: var(--primary-400);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    /* View Toggle Button Enhancements */
    .view-toggle-btn {
        transition: all 0.2s ease;
        position: relative;
    }

    .view-toggle-btn:hover:not(.active) {
        background-color: rgba(59, 130, 246, 0.1);
        color: var(--primary-600);
    }

    .dark .view-toggle-btn:hover:not(.active) {
        background-color: rgba(59, 130, 246, 0.2);
        color: var(--primary-400);
    }

    .view-toggle-btn.active {
        background: white;
        color: var(--primary-600);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--primary-200);
    }

    .dark .view-toggle-btn.active {
        background: var(--neutral-700);
        color: var(--primary-400);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        border: 1px solid var(--primary-600);
    }

    /* Meeting Card Enhancements */
    .meeting-card {
        transition: all 0.2s ease;
    }

    .meeting-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .dark .meeting-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    /* Meeting List Item Enhancements */
    .meeting-list-item {
        transition: all 0.2s ease;
    }

    .meeting-list-item:hover {
        transform: translateX(4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: var(--primary-300);
    }

    .dark .meeting-list-item:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        border-color: var(--primary-600);
    }

    /* Enhanced Form Controls */
    input[type="text"], select {
        transition: all 0.2s ease;
    }

    input[type="text"]:focus, select:focus {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    }

    /* Professional Button Enhancements */

    /* Primary Button Styles */
    .btn-primary-enhanced {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        border: 1px solid #2563eb;
        box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
        transition: all 0.2s ease;
        font-weight: 600;
        letter-spacing: 0.025em;
    }

    .btn-primary-enhanced:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        border-color: #1d4ed8;
        box-shadow: 0 6px 20px 0 rgba(37, 99, 235, 0.4);
        transform: translateY(-2px);
    }

    .btn-primary-enhanced:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px 0 rgba(37, 99, 235, 0.3);
    }

    /* Secondary Button Styles */
    .btn-secondary-enhanced {
        background: #ffffff;
        border: 1.5px solid #e5e7eb;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.2s ease;
        font-weight: 500;
        color: #374151;
    }

    .dark .btn-secondary-enhanced {
        background: #1f2937;
        border-color: #374151;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
        color: #e5e7eb;
    }

    .btn-secondary-enhanced:hover {
        background: #f9fafb;
        border-color: #d1d5db;
        box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
        color: #111827;
    }

    .dark .btn-secondary-enhanced:hover {
        background: #374151;
        border-color: #4b5563;
        box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.3);
        color: #f9fafb;
    }

    /* Success Button Styles */
    .btn-success-enhanced {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border: 1px solid #059669;
        box-shadow: 0 4px 14px 0 rgba(5, 150, 105, 0.25);
        transition: all 0.2s ease;
        font-weight: 600;
        color: white;
    }

    .btn-success-enhanced:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        border-color: #047857;
        box-shadow: 0 6px 20px 0 rgba(5, 150, 105, 0.4);
        transform: translateY(-2px);
    }

    /* Tertiary Button Styles */
    .btn-tertiary-enhanced {
        background: transparent;
        border: 1px solid transparent;
        transition: all 0.2s ease;
        font-weight: 500;
        color: #6b7280;
    }

    .dark .btn-tertiary-enhanced {
        color: #9ca3af;
    }

    .btn-tertiary-enhanced:hover {
        background: rgba(0, 0, 0, 0.04);
        border-color: #e5e7eb;
        transform: translateY(-1px);
        color: #374151;
    }

    .dark .btn-tertiary-enhanced:hover {
        background: rgba(255, 255, 255, 0.05);
        border-color: #374151;
        color: #e5e7eb;
    }

    /* Button Padding Standards */
    .btn-large-enhanced {
        padding: 0.75rem 2rem; /* py-3 px-8 - More generous padding */
    }

    .btn-medium-enhanced {
        padding: 0.625rem 1.5rem; /* py-2.5 px-6 - Increased from px-5 */
    }

    .btn-small-enhanced {
        font-size: 0.875rem;
        padding: 0.5rem 1.25rem; /* py-2 px-5 - Increased from px-4 */
        font-weight: 500;
    }

    .btn-xs-enhanced {
        font-size: 0.75rem;
        padding: 0.375rem 1rem; /* py-1.5 px-4 - Increased from px-3 */
        font-weight: 500;
    }

    /* Icon Button Enhancements */
    .btn-icon-enhanced {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
    }

    .btn-icon-enhanced:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
    }

    /* Status Color Enhancements */
    .status-scheduled {
        background-color: #dbeafe;
        color: #1e40af;
        border-color: #93c5fd;
    }

    .dark .status-scheduled {
        background-color: rgba(59, 130, 246, 0.2);
        color: #93c5fd;
        border-color: #3b82f6;
    }

    .status-inprogress {
        background-color: #dcfce7;
        color: #166534;
        border-color: #86efac;
    }

    .dark .status-inprogress {
        background-color: rgba(34, 197, 94, 0.2);
        color: #86efac;
        border-color: #22c55e;
    }

    .status-completed {
        background-color: #d1fae5;
        color: #065f46;
        border-color: #6ee7b7;
    }

    .dark .status-completed {
        background-color: rgba(16, 185, 129, 0.2);
        color: #6ee7b7;
        border-color: #10b981;
    }

    .status-cancelled {
        background-color: #fee2e2;
        color: #991b1b;
        border-color: #fca5a5;
    }

    .dark .status-cancelled {
        background-color: rgba(239, 68, 68, 0.2);
        color: #fca5a5;
        border-color: #ef4444;
    }

    /* Meeting Type Icons */
    .meeting-type-standup .type-icon {
        color: #6366f1;
    }

    .meeting-type-planning .type-icon {
        color: #8b5cf6;
    }

    .meeting-type-review .type-icon {
        color: #f59e0b;
    }

    .meeting-type-retrospective .type-icon {
        color: #ec4899;
    }

    .meeting-type-general .type-icon {
        color: #6b7280;
    }

    /* Responsive Enhancements */
    @@media (max-width: 768px) {
        .meeting-card {
            margin-bottom: 1rem;
        }

        .meeting-list-item {
            padding: 1rem;
        }

        .stats-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
        }
    }

    /* Animation Keyframes */
    @@keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    @@keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .fade-in {
        animation: fadeIn 0.3s ease-out;
    }

    /* Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    .dark .loading-skeleton {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200% 100%;
    }

    @@keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Utility Classes */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>
