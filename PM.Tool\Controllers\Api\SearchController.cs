using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Data;
using PM.Tool.Core.Entities;
using PM.Tool.Models.ViewModels;
using System.Security.Claims;
using PM.Tool.Core.Enums;

namespace PM.Tool.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SearchController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SearchController> _logger;

        public SearchController(
            ApplicationDbContext context,
            ILogger<SearchController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Check database data counts for debugging
        /// </summary>
        [HttpGet("debug/counts")]
        public async Task<IActionResult> GetDataCounts()
        {
            try
            {
                var projectCount = await _context.Projects.CountAsync();
                var taskCount = await _context.Tasks.CountAsync();
                var peopleCount = await _context.People.CountAsync();
                var meetingCount = await _context.Meetings.CountAsync();
                var requirementCount = await _context.Requirements.CountAsync();

                return Ok(new {
                    projects = projectCount,
                    tasks = taskCount,
                    people = peopleCount,
                    meetings = meetingCount,
                    requirements = requirementCount,
                    total = projectCount + taskCount + peopleCount + meetingCount + requirementCount
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting data counts");
                return StatusCode(500, new { error = ex.Message });
            }
        }



        /// <summary>
        /// Global search across projects, tasks, documents, and people
        /// </summary>
        /// <param name="q">Search query</param>
        /// <param name="limit">Maximum number of results per category</param>
        /// <returns>Search results grouped by category</returns>
        [HttpGet]
        public async Task<IActionResult> GlobalSearch([FromQuery] string q, [FromQuery] int limit = 5)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q) || q.Length < 2)
                {
                    return Ok(new {
                        query = q,
                        results = new {
                            projects = new object[0],
                            tasks = new object[0],
                            documents = new object[0],
                            people = new object[0],
                            meetings = new object[0],
                            requirements = new object[0]
                        },
                        totalResults = 0
                    });
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var searchTerm = q.ToLower();

                // Search Projects
                var projects = await _context.Projects
                    .Where(p => p.Name.ToLower().Contains(searchTerm) || 
                               p.Description.ToLower().Contains(searchTerm))
                    .Select(p => new {
                        Id = p.Id,
                        Title = p.Name,
                        Description = p.Description,
                        Type = "Project",
                        Icon = "fas fa-folder",
                        Url = Url.Action("Details", "Projects", new { id = p.Id }),
                        Status = p.Status.ToString(),
                        Progress = p.ProgressPercentage,
                        CreatedAt = p.CreatedAt
                    })
                    .Take(limit)
                    .ToListAsync();



                // Search Tasks
                var tasks = await _context.Tasks
                    .Include(t => t.Project)
                    .Where(t => t.Title.ToLower().Contains(searchTerm) || 
                               t.Description.ToLower().Contains(searchTerm))
                    .Select(t => new {
                        Id = t.Id,
                        Title = t.Title,
                        Description = t.Description,
                        Type = "Task",
                        Icon = "fas fa-tasks",
                        Url = Url.Action("Details", "Tasks", new { id = t.Id }),
                        Status = t.Status.ToString(),
                        ProjectName = t.Project.Name,
                        Priority = t.Priority.ToString(),
                        DueDate = t.DueDate,
                        CreatedAt = t.CreatedAt
                    })
                    .Take(limit)
                    .ToListAsync();



                // Search People
                var people = await _context.People
                    .Where(p => p.FirstName.ToLower().Contains(searchTerm) ||
                               p.LastName.ToLower().Contains(searchTerm) ||
                               p.Email.ToLower().Contains(searchTerm) ||
                               (p.Organization != null && p.Organization.ToLower().Contains(searchTerm)) ||
                               (p.Department != null && p.Department.ToLower().Contains(searchTerm)))
                    .Select(p => new {
                        Id = p.Id,
                        Title = $"{p.FirstName} {p.LastName}",
                        Description = $"{p.Title} - {p.Organization}",
                        Type = "Person",
                        Icon = "fas fa-user",
                        Url = Url.Action("Details", "Person", new { id = p.Id }),
                        Email = p.Email,
                        Organization = p.Organization,
                        Department = p.Department,
                        CreatedAt = p.CreatedAt
                    })
                    .Take(limit)
                    .ToListAsync();

                // Search Documents (if you have a documents table)
                var documentResults = new List<object>(); // Placeholder for documents

                // Search Meetings
                var meetings = await _context.Meetings
                    .Where(m => m.Title.ToLower().Contains(searchTerm) ||
                               m.Description.ToLower().Contains(searchTerm))
                    .Select(m => new {
                        Id = m.Id,
                        Title = m.Title,
                        Description = m.Description,
                        Type = "Meeting",
                        Icon = "fas fa-video",
                        Url = Url.Action("Details", "Meeting", new { id = m.Id }),
                        StartTime = m.ScheduledDate,
                        EndTime = m.EndTime,
                        Status = m.Status.ToString(),
                        CreatedAt = m.CreatedAt
                    })
                    .Take(limit)
                    .ToListAsync();

                // Search Requirements
                var requirements = await _context.Requirements
                    .Include(r => r.Project)
                    .Where(r => r.Title.ToLower().Contains(searchTerm) ||
                               r.Description.ToLower().Contains(searchTerm))
                    .Select(r => new {
                        Id = r.Id,
                        Title = r.Title,
                        Description = r.Description,
                        Type = "Requirement",
                        Icon = "fas fa-list-check",
                        Url = Url.Action("Details", "Requirement", new { id = r.Id }),
                        Status = r.Status.ToString(),
                        Priority = r.Priority.ToString(),
                        ProjectName = r.Project.Name,
                        CreatedAt = r.CreatedAt
                    })
                    .Take(limit)
                    .ToListAsync();

                var totalResults = projects.Count + tasks.Count + people.Count +
                                 documentResults.Count + meetings.Count + requirements.Count;

                return Ok(new {
                    query = q,
                    results = new {
                        projects,
                        tasks,
                        people,
                        documents = documentResults,
                        meetings,
                        requirements
                    },
                    totalResults
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing global search for query: {Query}", q);
                return StatusCode(500, new { error = "Search failed" });
            }
        }

        /// <summary>
        /// Quick search suggestions for autocomplete
        /// </summary>
        /// <param name="q">Search query</param>
        /// <param name="limit">Maximum number of suggestions</param>
        /// <returns>Quick search suggestions</returns>
        [HttpGet("suggestions")]
        public async Task<IActionResult> GetSuggestions([FromQuery] string q, [FromQuery] int limit = 8)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q) || q.Length < 2)
                {
                    return Ok(new { suggestions = new object[0] });
                }

                var searchTerm = q.ToLower();
                var suggestions = new List<object>();

                // Project suggestions
                var projectSuggestions = await _context.Projects
                    .Where(p => p.Name.ToLower().Contains(searchTerm))
                    .Select(p => new {
                        Id = p.Id,
                        Text = p.Name,
                        Type = "Project",
                        Icon = "fas fa-folder",
                        Url = Url.Action("Details", "Projects", new { id = p.Id })
                    })
                    .Take(3)
                    .ToListAsync();

                // Task suggestions
                var taskSuggestions = await _context.Tasks
                    .Where(t => t.Title.ToLower().Contains(searchTerm))
                    .Select(t => new {
                        Id = t.Id,
                        Text = t.Title,
                        Type = "Task",
                        Icon = "fas fa-tasks",
                        Url = Url.Action("Details", "Tasks", new { id = t.Id })
                    })
                    .Take(3)
                    .ToListAsync();

                // People suggestions
                var peopleSuggestions = await _context.People
                    .Where(p => (p.FirstName + " " + p.LastName).ToLower().Contains(searchTerm))
                    .Select(p => new {
                        Id = p.Id,
                        Text = $"{p.FirstName} {p.LastName}",
                        Type = "Person",
                        Icon = "fas fa-user",
                        Url = Url.Action("Details", "Person", new { id = p.Id })
                    })
                    .Take(2)
                    .ToListAsync();

                suggestions.AddRange(projectSuggestions);
                suggestions.AddRange(taskSuggestions);
                suggestions.AddRange(peopleSuggestions);

                return Ok(new { 
                    suggestions = suggestions.Take(limit)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search suggestions for query: {Query}", q);
                return StatusCode(500, new { error = "Failed to get suggestions" });
            }
        }

        /// <summary>
        /// Get recent searches for the current user
        /// </summary>
        /// <param name="limit">Maximum number of recent searches</param>
        /// <returns>Recent search queries</returns>
        [HttpGet("recent")]
        public async Task<IActionResult> GetRecentSearches([FromQuery] int limit = 5)
        {
            try
            {
                // This would typically come from a user search history table
                // For now, return empty array as placeholder
                var recentSearches = new List<object>();

                return Ok(new { recentSearches });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent searches");
                return StatusCode(500, new { error = "Failed to get recent searches" });
            }
        }
    }
}
