{"ConnectionStrings": {"DefaultConnection": "Host=**************;Database=PMToolDB;Username=hec_user;Password=*********;Port=5432"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "Microsoft.Extensions.Localization": "Debug", "PM.Tool.Services.DebugLocalizationService": "Debug", "PM.Tool.Controllers.HomeController": "Debug"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/log-.txt", "rollingInterval": "Day"}}]}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "", "SmtpPassword": "", "FromEmail": "<EMAIL>", "FromName": "PM <PERSON><PERSON>"}, "FileUpload": {"MaxFileSize": 10485760, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt", ".zip"], "UploadPath": "uploads"}, "DataEncryption": {"Key": "CHANGE_THIS_IN_PRODUCTION_TO_A_SECURE_32_BYTE_KEY", "EnableEncryption": true, "RotateKeysDaily": false}, "AllowedHosts": "*"}