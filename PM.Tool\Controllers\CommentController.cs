using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

using PM.Tool.Core.Constants;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Extensions;
using PM.Tool.Core.Helpers;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Models.ViewModels;
using PM.Tool.Application.Services;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class CommentController : SecureBaseController
    {
        private readonly ApplicationDbContext _context;
        private readonly INotificationService _notificationService;
        private readonly IMentionService _mentionService;

        public CommentController(
            ApplicationDbContext context,
            INotificationService notificationService,
            IMentionService mentionService,
            IAuditService auditService) : base(auditService)
        {
            _context = context;
            _notificationService = notificationService;
            _mentionService = mentionService;
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CommentViewModel model)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var task = await _context.Tasks
                .Include(t => t.AssignedTo)
                .Include(t => t.Project)
                .FirstOrDefaultAsync(t => t.Id == model.TaskId);

            if (task == null)
                return NotFound();

            var comment = new TaskComment
            {
                Content = model.Content,
                TaskId = model.TaskId,
                UserId = User.GetUserId(),
                CreatedAt = DateTime.UtcNow
            };

            _context.TaskComments.Add(comment);
            await _context.SaveChangesAsync();

            await LogAuditAsync(AuditAction.Create, "TaskComment", comment.Id);

            // Process mentions in the comment
            await _mentionService.ProcessMentionsAsync(
                comment.Content,
                User.GetUserId(),
                task.Id,
                task.ProjectId,
                $"task comment on '{task.Title}'");

            // Notify task assignee
            if (task.AssignedToUserId != User.GetUserId())
            {
                await _notificationService.NotifyCommentAddedAsync(task.Id, User.GetUserId());
            }

            // Send real-time notification
            var realTimeService = HttpContext.RequestServices.GetService<IRealTimeNotificationService>();
            if (realTimeService != null)
            {
                await realTimeService.NotifyCommentAddedAsync(
                    task.Id,
                    comment.Content,
                    User.GetUserId());
            }

            // Load the user to get the full name
            var user = await _context.Users.FindAsync(User.GetUserId());

            // Format content with mentions
            var formattedContent = await _mentionService.FormatMentionsAsync(comment.Content);

            return Json(new
            {
                id = comment.Id,
                content = MarkdownHelper.FormatMarkdown(formattedContent),
                authorName = user?.FullName ?? User.Identity.Name,
                createdAt = comment.CreatedAt
            });
        }

        [HttpPut]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CommentViewModel model)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var comment = await _context.TaskComments.FindAsync(id);

            if (comment == null)
                return NotFound();

            if (comment.UserId != User.GetUserId() && !User.IsInRole(Roles.Admin))
                return Forbid();

            comment.Content = model.Content;
            comment.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            await LogAuditAsync(AuditAction.Update, "TaskComment", comment.Id);

            return Json(new
            {
                content = MarkdownHelper.FormatMarkdown(comment.Content),
                updatedAt = comment.UpdatedAt
            });
        }

        [HttpDelete]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var comment = await _context.TaskComments.FindAsync(id);

            if (comment == null)
                return NotFound();

            if (comment.UserId != User.GetUserId() && !User.IsInRole(Roles.Admin))
                return Forbid();

            _context.TaskComments.Remove(comment);
            await _context.SaveChangesAsync();
            await LogAuditAsync(AuditAction.Delete, "TaskComment", comment.Id);

            return Ok();
        }
    }
}