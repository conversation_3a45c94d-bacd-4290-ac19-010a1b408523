@{
    var text = ViewData["Text"]?.ToString() ?? "Button";
    var variant = ViewData["Variant"]?.ToString() ?? "primary";
    var size = ViewData["Size"]?.ToString() ?? "md";
    var icon = ViewData["Icon"]?.ToString();
    var iconPosition = ViewData["IconPosition"]?.ToString() ?? "left";
    var href = ViewData["Href"]?.ToString();
    var onclick = ViewData["OnClick"]?.ToString();
    var type = ViewData["Type"]?.ToString() ?? "button";
    var disabled = ViewData["Disabled"] as bool? ?? false;
    var fullWidth = ViewData["FullWidth"] as bool? ?? false;
    var loading = ViewData["Loading"] as bool? ?? false;
    var additionalClasses = ViewData["AdditionalClasses"]?.ToString() ?? "";
    var id = ViewData["Id"]?.ToString();
    var ariaLabel = ViewData["AriaLabel"]?.ToString();
    var title = ViewData["Title"]?.ToString();

    // Build compact CSS classes
    var cssClasses = new List<string> { "btn-compact" };
    
    switch (variant.ToLower())
    {
        case "primary":
            cssClasses.Add("btn-compact-primary");
            break;
        case "secondary":
        case "outline":
            cssClasses.Add("btn-compact-secondary");
            break;
        case "danger":
            cssClasses.Add("bg-red-600 hover:bg-red-700 text-white border-red-600");
            break;
        case "success":
            cssClasses.Add("bg-green-600 hover:bg-green-700 text-white border-green-600");
            break;
        case "warning":
            cssClasses.Add("bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600");
            break;
        default:
            cssClasses.Add("btn-compact-primary");
            break;
    }

    // Size variants for compact design
    switch (size.ToLower())
    {
        case "sm":
            cssClasses.Add("text-xs h-7 px-3 gap-1");
            break;
        case "lg":
            cssClasses.Add("text-base h-10 px-6 gap-3");
            break;
        default:
            // Default compact size already defined in btn-compact
            break;
    }

    if (fullWidth)
    {
        cssClasses.Add("w-full");
    }

    if (!string.IsNullOrEmpty(additionalClasses))
    {
        cssClasses.Add(additionalClasses);
    }

    if (disabled)
    {
        cssClasses.Add("opacity-50 cursor-not-allowed");
    }

    var finalClasses = string.Join(" ", cssClasses);
}

@if (!string.IsNullOrEmpty(href))
{
    <a href="@href"
       class="@finalClasses"
       @(string.IsNullOrEmpty(id) ? "" : $"id=\"{id}\"")
       @(string.IsNullOrEmpty(ariaLabel) ? "" : $"aria-label=\"{ariaLabel}\"")
       @(string.IsNullOrEmpty(onclick) ? "" : $"onclick=\"{onclick}\"")
       @(string.IsNullOrEmpty(title) ? "" : $"title=\"{title}\"")
       @(disabled ? "aria-disabled=\"true\"" : "")>

        @if (loading)
        {
            <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-current"></div>
        }
        else if (!string.IsNullOrEmpty(icon) && iconPosition == "left")
        {
            <i class="@icon"></i>
        }

        @if (!string.IsNullOrEmpty(text))
        {
            <span>@text</span>
        }

        @if (!string.IsNullOrEmpty(icon) && iconPosition == "right")
        {
            <i class="@icon"></i>
        }
    </a>
}
else
{
    <button type="@type"
            class="@finalClasses"
            @(string.IsNullOrEmpty(id) ? "" : $"id=\"{id}\"")
            @(string.IsNullOrEmpty(ariaLabel) ? "" : $"aria-label=\"{ariaLabel}\"")
            @(string.IsNullOrEmpty(onclick) ? "" : $"onclick=\"{onclick}\"")
            @(string.IsNullOrEmpty(title) ? "" : $"title=\"{title}\"")
            @(disabled ? "disabled=\"disabled\"" : "")>

        @if (loading)
        {
            <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-current"></div>
        }
        else if (!string.IsNullOrEmpty(icon) && iconPosition == "left")
        {
            <i class="@icon"></i>
        }

        @if (!string.IsNullOrEmpty(text))
        {
            <span>@text</span>
        }

        @if (!string.IsNullOrEmpty(icon) && iconPosition == "right")
        {
            <i class="@icon"></i>
        }
    </button>
}
