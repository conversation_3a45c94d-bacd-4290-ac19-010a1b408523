@{
    /*
    Standardized Page Header Component
    
    Usage:
    @{
        ViewData["Title"] = "Page Title";
        ViewData["Description"] = "Page description";
        ViewData["Icon"] = "fas fa-icon";
        ViewData["IconColor"] = "text-primary-600 dark:text-primary-400"; // Optional
        ViewData["Actions"] = new[] { 
            new { Text = "Action", Variant = "primary", Icon = "fas fa-plus", Href = "/action" }
        };
        ViewData["Stats"] = new[] {
            new { Label = "Total", Value = "100", Icon = "fas fa-chart", Color = "blue" }
        };
        ViewData["ShowBreadcrumb"] = true; // Optional
        ViewData["BreadcrumbItems"] = new[] {
            new { Text = "Dashboard", Href = "/", Icon = "fas fa-home" },
            new { Text = "Current Page", Href = "", Icon = "fas fa-page" }
        };
    }
    <partial name="Components/_PageHeader" view-data="ViewData" />
    */

    var title = ViewData["Title"]?.ToString() ?? "Page Title";
    var description = ViewData["Description"]?.ToString();
    var icon = ViewData["Icon"]?.ToString();
    var iconColor = ViewData["IconColor"]?.ToString() ?? "text-primary-600 dark:text-primary-400";
    var showBreadcrumb = ViewData["ShowBreadcrumb"] as bool? ?? false;
    var breadcrumbItems = ViewData["BreadcrumbItems"] as object[];
    var actions = ViewData["Actions"] as object[];
    var stats = ViewData["Stats"] as object[];
    var headerClass = ViewData["HeaderClass"]?.ToString() ?? "";
    var compact = ViewData["Compact"] as bool? ?? false;
}

<!-- Breadcrumb (if enabled) -->
@if (showBreadcrumb && breadcrumbItems != null)
{
    ViewData["Items"] = breadcrumbItems;
    <partial name="Components/_Breadcrumb" view-data="ViewData" />
}

<!-- Page Header -->
<div class="mb-8 @headerClass">
    <div class="@(compact ? "py-4" : "py-6") @(stats != null && stats.Any() ? "pb-4" : "")">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <!-- Title Section -->
            <div class="flex-1 min-w-0">
                <h1 class="@(compact ? "text-xl" : "text-2xl") font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                    @if (!string.IsNullOrEmpty(icon))
                    {
                        <i class="@icon mr-3 @iconColor"></i>
                    }
                    @title
                </h1>
                @if (!string.IsNullOrEmpty(description))
                {
                    <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                        @description
                    </p>
                }
            </div>

            <!-- Actions Section -->
            @if (actions != null && actions.Any())
            {
                <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                    @foreach (dynamic action in actions)
                    {
                        ViewData["Text"] = action.Text?.ToString();
                        ViewData["Variant"] = action.Variant?.ToString() ?? "primary";
                        ViewData["Icon"] = action.Icon?.ToString();

                        // Safely check for Href property
                        try
                        {
                            ViewData["Href"] = action.Href?.ToString();
                        }
                        catch
                        {
                            ViewData["Href"] = null;
                        }

                        // Safely check for OnClick property
                        try
                        {
                            ViewData["OnClick"] = action.OnClick?.ToString();
                        }
                        catch
                        {
                            ViewData["OnClick"] = null;
                        }

                        // Safely check for Size property
                        try
                        {
                            ViewData["Size"] = action.Size?.ToString() ?? "md";
                        }
                        catch
                        {
                            ViewData["Size"] = "md";
                        }

                        // Safely check for Disabled property
                        try
                        {
                            ViewData["Disabled"] = action.Disabled as bool? ?? false;
                        }
                        catch
                        {
                            ViewData["Disabled"] = false;
                        }

                        <partial name="Components/_Button" view-data="ViewData" />
                    }
                </div>
            }
        </div>

        <!-- Stats Section -->
        @if (stats != null && stats.Any())
        {
            <div class="mt-6 flex flex-wrap gap-3">
                @foreach (dynamic stat in stats)
                {
                    ViewData["Label"] = stat.Label?.ToString();
                    ViewData["Value"] = stat.Value?.ToString();
                    ViewData["Icon"] = stat.Icon?.ToString();

                    // Safely check for Color property
                    try
                    {
                        ViewData["Color"] = stat.Color?.ToString() ?? "blue";
                    }
                    catch
                    {
                        ViewData["Color"] = "blue";
                    }

                    // Safely check for Size property
                    try
                    {
                        ViewData["Size"] = stat.Size?.ToString() ?? "md";
                    }
                    catch
                    {
                        ViewData["Size"] = "md";
                    }

                    // Safely check for Href property
                    try
                    {
                        ViewData["Href"] = stat.Href?.ToString();
                    }
                    catch
                    {
                        ViewData["Href"] = null;
                    }

                    <partial name="Components/_StatsBadge" view-data="ViewData" />
                }
            </div>
        }
    </div>
</div>
