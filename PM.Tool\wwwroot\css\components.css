/* Enterprise-Grade Component Library */
/* Professional, accessible design system components for PM Tool */
/* Unified, Compact, Professional UX Design System */

/* =============================================================================
   DESIGN TOKENS - Unified Spacing & Typography System
   ============================================================================= */

:root {
    /* Compact Spacing Scale */
    --space-xs: 0.25rem;    /* 4px - Micro spacing */
    --space-sm: 0.5rem;     /* 8px - Small spacing */
    --space-md: 0.75rem;    /* 12px - Medium spacing */
    --space-lg: 1rem;       /* 16px - Large spacing */
    --space-xl: 1.5rem;     /* 24px - XL spacing */
    --space-2xl: 2rem;      /* 32px - XXL spacing */

    /* Professional Typography */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */

    /* Compact Component Heights */
    --height-sm: 2rem;      /* 32px - Compact buttons/inputs */
    --height-md: 2.5rem;    /* 40px - Standard buttons/inputs */
    --height-lg: 3rem;      /* 48px - Large buttons */

    /* Professional Borders & Shadows */
    --border-radius: 0.5rem;
    --border-radius-sm: 0.375rem;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* =============================================================================
   COMPACT STATUS PILLS - Professional & Space-Efficient
   ============================================================================= */

.status-pill {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-md);
    font-size: var(--text-xs);
    font-weight: 600;
    line-height: 1;
    border-radius: var(--border-radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    gap: var(--space-xs);
    border: 1px solid transparent;
    transition: all 0.15s ease-in-out;
    height: var(--height-sm);
    min-width: fit-content;
}

/* =============================================================================
   COMPACT CARD SYSTEM - Professional & Space-Efficient
   ============================================================================= */

.card-compact {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: all 0.15s ease-in-out;
}

.card-compact:hover {
    box-shadow: var(--shadow-md);
    border-color: #d1d5db;
}

.card-compact-header {
    padding: var(--space-lg) var(--space-lg) var(--space-md) var(--space-lg);
    border-bottom: 1px solid #f3f4f6;
}

.card-compact-body {
    padding: var(--space-md) var(--space-lg);
}

.card-compact-footer {
    padding: var(--space-md) var(--space-lg) var(--space-lg) var(--space-lg);
    border-top: 1px solid #f3f4f6;
    background: #fafafa;
}

/* Dark mode card variants */
.dark .card-compact {
    background: #1f2937;
    border-color: #374151;
}

.dark .card-compact:hover {
    border-color: #4b5563;
}

.dark .card-compact-header {
    border-bottom-color: #374151;
}

.dark .card-compact-footer {
    border-top-color: #374151;
    background: #111827;
}

/* =============================================================================
   PROFESSIONAL STATUS PILLS - Compact & Consistent
   ============================================================================= */

/* Light Mode Status Pills */
.status-pill.status-todo {
    background-color: #f8fafc;
    color: #475569;
    border-color: #e2e8f0;
}

.status-pill.status-in-progress {
    background-color: #dbeafe;
    color: #1e40af;
    border-color: #93c5fd;
}

.status-pill.status-completed,
.status-pill.status-done {
    background-color: #dcfce7;
    color: #15803d;
    border-color: #86efac;
}

.status-pill.status-blocked {
    background-color: #fee2e2;
    color: #b91c1c;
    border-color: #fca5a5;
}

.status-pill.status-on-hold {
    background-color: #fef3c7;
    color: #b45309;
    border-color: #fcd34d;
}

/* Dark Mode Status Pills */
[data-theme="dark"] .status-pill.status-todo {
    background-color: #374151;
    color: #d1d5db;
    border-color: #4b5563;
}

[data-theme="dark"] .status-pill.status-in-progress {
    background-color: #1e3a8a;
    color: #93c5fd;
    border-color: #2563eb;
}

[data-theme="dark"] .status-pill.status-completed,
[data-theme="dark"] .status-pill.status-done {
    background-color: #14532d;
    color: #86efac;
    border-color: #22c55e;
}

[data-theme="dark"] .status-pill.status-blocked {
    background-color: #7f1d1d;
    color: #fca5a5;
    border-color: #ef4444;
}

[data-theme="dark"] .status-pill.status-on-hold {
    background-color: #78350f;
    color: #fcd34d;
    border-color: #f59e0b;
}

/* =============================================================================
   COMPACT BUTTON SYSTEM - Professional & Consistent
   ============================================================================= */

.btn-compact {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--text-sm);
    font-weight: 500;
    line-height: 1;
    border-radius: var(--border-radius-sm);
    border: 1px solid transparent;
    transition: all 0.15s ease-in-out;
    height: var(--height-sm);
    min-width: fit-content;
    cursor: pointer;
    text-decoration: none;
}

.btn-compact:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-compact-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border-color: #2563eb;
    box-shadow: var(--shadow-sm);
}

.btn-compact-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    box-shadow: var(--shadow-md);
}

.btn-compact-secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;
    box-shadow: var(--shadow-sm);
}

.btn-compact-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Dark mode button variants */
.dark .btn-compact-secondary {
    background: #374151;
    color: #f3f4f6;
    border-color: #4b5563;
}

.dark .btn-compact-secondary:hover {
    background: #4b5563;
    border-color: #6b7280;
}

/* =============================================================================
   COMPACT FORM SYSTEM - Professional & Space-Efficient
   ============================================================================= */

.form-compact {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.form-group-compact {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.form-group-compact label {
    font-size: var(--text-sm);
    font-weight: 500;
    color: #374151;
    margin-bottom: var(--space-xs);
}

.dark .form-group-compact label {
    color: #f3f4f6;
}

.input-compact {
    height: var(--height-sm);
    padding: 0 var(--space-md);
    font-size: var(--text-sm);
    border: 1px solid #d1d5db;
    border-radius: var(--border-radius-sm);
    background: white;
    transition: all 0.15s ease-in-out;
}

.input-compact:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .input-compact {
    background: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
}

.dark .input-compact:focus {
    border-color: #60a5fa;
}

/* =============================================================================
   COMPACT TABLE SYSTEM - Professional Data Display
   ============================================================================= */

.table-compact {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--text-sm);
}

.table-compact th {
    padding: var(--space-md) var(--space-lg);
    text-align: left;
    font-weight: 600;
    color: #374151;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.table-compact td {
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
}

.table-compact tbody tr:hover {
    background: #f9fafb;
}

.dark .table-compact th {
    background: #374151;
    color: #f3f4f6;
    border-bottom-color: #4b5563;
}

.dark .table-compact td {
    border-bottom-color: #374151;
}

.dark .table-compact tbody tr:hover {
    background: #374151;
}

/* =============================================================================
   COMPACT LAYOUT SYSTEM - Professional Space Management
   ============================================================================= */

.layout-compact {
    padding: var(--space-xl);
    max-width: 100%;
}

.page-header-compact {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid #e5e7eb;
}

.dark .page-header-compact {
    border-bottom-color: #374151;
}

.page-title-compact {
    font-size: var(--text-xl);
    font-weight: 600;
    color: #111827;
    margin: 0;
    line-height: 1.2;
}

.dark .page-title-compact {
    color: #f9fafb;
}

.page-subtitle-compact {
    font-size: var(--text-sm);
    color: #6b7280;
    margin-top: var(--space-xs);
}

.dark .page-subtitle-compact {
    color: #9ca3af;
}

.page-actions-compact {
    display: flex;
    gap: var(--space-md);
    flex-shrink: 0;
}

.content-grid-compact {
    display: grid;
    gap: var(--space-xl);
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .content-grid-compact {
        grid-template-columns: 1fr 300px;
    }
}

.stats-grid-compact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

/* =============================================================================
   PROFESSIONAL NAVIGATION - Compact & Efficient
   ============================================================================= */

.nav-compact {
    display: flex;
    gap: var(--space-md);
    padding: var(--space-md) 0;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: var(--space-xl);
}

.dark .nav-compact {
    border-bottom-color: #374151;
}

.nav-item-compact {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--text-sm);
    font-weight: 500;
    color: #6b7280;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: all 0.15s ease-in-out;
}

.nav-item-compact:hover {
    color: #374151;
    background: #f3f4f6;
}

.nav-item-compact.active {
    color: #3b82f6;
    background: #eff6ff;
}

.dark .nav-item-compact {
    color: #9ca3af;
}

.dark .nav-item-compact:hover {
    color: #f3f4f6;
    background: #374151;
}

.dark .nav-item-compact.active {
    color: #60a5fa;
    background: #1e3a8a;
}

/* =============================================================================
   ADDITIONAL COMPACT BUTTON VARIANTS
   ============================================================================= */

.btn-compact-danger {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    border-color: #b91c1c;
    box-shadow: var(--shadow-sm);
}

.btn-compact-danger:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    box-shadow: var(--shadow-md);
}

.btn-compact-success {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    color: white;
    border-color: #15803d;
    box-shadow: var(--shadow-sm);
}

.btn-compact-success:hover {
    background: linear-gradient(135deg, #15803d 0%, #166534 100%);
    box-shadow: var(--shadow-md);
}

.btn-compact-warning {
    background: linear-gradient(135deg, #ca8a04 0%, #a16207 100%);
    color: white;
    border-color: #a16207;
    box-shadow: var(--shadow-sm);
}

.btn-compact-warning:hover {
    background: linear-gradient(135deg, #a16207 0%, #854d0e 100%);
    box-shadow: var(--shadow-md);
}

/* =============================================================================
   COMPACT UTILITY CLASSES
   ============================================================================= */

.text-compact {
    line-height: 1.4;
}

.spacing-compact > * + * {
    margin-top: var(--space-md);
}

.grid-compact {
    display: grid;
    gap: var(--space-lg);
}

.flex-compact {
    display: flex;
    gap: var(--space-md);
    align-items: center;
}

.stack-compact {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

/* Responsive grid patterns */
.grid-compact-2 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-compact-3 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.grid-compact-4 {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
}

/* =============================================================================
   COMPACT SIDEBAR NAVIGATION
   ============================================================================= */

.sidebar-nav-compact {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.sidebar-section-compact {
    margin-top: var(--space-xl);
}

.sidebar-section-compact:first-child {
    margin-top: 0;
}

.sidebar-section-title {
    padding: 0 var(--space-sm);
    font-size: 0.6875rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-sm);
}

.dark .sidebar-section-title {
    color: #9ca3af;
}

/* Override nav-item-compact for sidebar specific styling */
.nav-item-compact {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-sm);
    font-weight: 500;
    color: #6b7280;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: all 0.15s ease-in-out;
    margin: 0 var(--space-xs);
}

.nav-item-compact:hover {
    color: #374151;
    background: #f3f4f6;
}

.nav-item-compact.active {
    color: #3b82f6;
    background: #eff6ff;
    font-weight: 600;
}

.dark .nav-item-compact {
    color: #9ca3af;
}

.dark .nav-item-compact:hover {
    color: #f3f4f6;
    background: #374151;
}

.dark .nav-item-compact.active {
    color: #60a5fa;
    background: #1e3a8a;
}

/* Icon sizing for sidebar */
.nav-item-compact i {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
    text-align: center;
}

/* Enhanced Card Components with Professional Dark Mode */
.card-enterprise {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card-enterprise:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: var(--color-accent);
}

.card-enterprise:focus-within {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
}

/* Dark mode card enhancements */
[data-theme="dark"] .card-enterprise {
    background-color: var(--color-surface);
    border-color: var(--color-border);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .card-enterprise:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.6), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
    border-color: var(--color-accent);
}

/* Card Header */
.card-header-enterprise {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border-light);
}

.card-title-enterprise {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0;
    line-height: 1.4;
}

.card-subtitle-enterprise {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    margin: 0.25rem 0 0 0;
}

/* Card Body */
.card-body-enterprise {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* Card Footer */
.card-footer-enterprise {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--color-border-light);
    margin-top: auto;
}

/* Priority Indicators with Enhanced Styling */
.priority-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid transparent;
}

.priority-indicator.priority-low {
    background-color: #dcfce7;
    color: #15803d;
    border-color: #86efac;
}

.priority-indicator.priority-medium {
    background-color: #fef3c7;
    color: #b45309;
    border-color: #fcd34d;
}

.priority-indicator.priority-high {
    background-color: #fee2e2;
    color: #b91c1c;
    border-color: #fca5a5;
}

.priority-indicator.priority-critical {
    background-color: #fee2e2;
    color: #b91c1c;
    border-color: #fca5a5;
    animation: pulse-critical 2s infinite;
}

/* Dark mode priority indicators */
[data-theme="dark"] .priority-indicator.priority-low {
    background-color: #14532d;
    color: #86efac;
    border-color: #22c55e;
}

[data-theme="dark"] .priority-indicator.priority-medium {
    background-color: #78350f;
    color: #fcd34d;
    border-color: #f59e0b;
}

[data-theme="dark"] .priority-indicator.priority-high {
    background-color: #7f1d1d;
    color: #fca5a5;
    border-color: #ef4444;
}

[data-theme="dark"] .priority-indicator.priority-critical {
    background-color: #7f1d1d;
    color: #fca5a5;
    border-color: #ef4444;
}

@keyframes pulse-critical {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.02);
    }
}

/* Enterprise Form Components */
.form-group-enterprise {
    margin-bottom: 1.5rem;
}

.form-label-enterprise {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-text-primary);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.form-input-enterprise {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--color-text-primary);
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.form-input-enterprise:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background-color: var(--color-surface-elevated);
}

.form-input-enterprise:disabled {
    background-color: var(--color-bg-secondary);
    color: var(--color-text-muted);
    cursor: not-allowed;
    opacity: 0.6;
}

.form-input-enterprise::placeholder {
    color: var(--color-text-muted);
}

/* Dark mode form inputs */
[data-theme="dark"] .form-input-enterprise {
    background-color: var(--color-surface);
    border-color: var(--color-border);
    color: var(--color-text-primary);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .form-input-enterprise:focus {
    border-color: var(--color-accent);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
    background-color: var(--color-surface-elevated);
}

[data-theme="dark"] .form-input-enterprise:disabled {
    background-color: var(--color-bg-secondary);
    color: var(--color-text-muted);
}

/* Form Validation States */
.form-input-enterprise.is-valid {
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-input-enterprise.is-invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

[data-theme="dark"] .form-input-enterprise.is-valid {
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
}

[data-theme="dark"] .form-input-enterprise.is-invalid {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* Form Help Text */
.form-help-text {
    font-size: 0.75rem;
    color: var(--color-text-muted);
    margin-top: 0.25rem;
    line-height: 1.4;
}

.form-error-text {
    font-size: 0.75rem;
    color: #ef4444;
    margin-top: 0.25rem;
    line-height: 1.4;
}

.form-success-text {
    font-size: 0.75rem;
    color: #22c55e;
    margin-top: 0.25rem;
    line-height: 1.4;
}

/* Enterprise Alert Components */
.alert-enterprise {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem 1.25rem;
    border-radius: 0.5rem;
    border: 1px solid transparent;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.alert-enterprise::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: currentColor;
}

/* Success Alert */
.alert-success-enterprise {
    background-color: #f0fdf4;
    color: #15803d;
    border-color: #bbf7d0;
}

.alert-success-enterprise::before {
    background-color: #22c55e;
}

/* Warning Alert */
.alert-warning-enterprise {
    background-color: #fffbeb;
    color: #b45309;
    border-color: #fde68a;
}

.alert-warning-enterprise::before {
    background-color: #f59e0b;
}

/* Danger Alert */
.alert-danger-enterprise {
    background-color: #fef2f2;
    color: #b91c1c;
    border-color: #fecaca;
}

.alert-danger-enterprise::before {
    background-color: #ef4444;
}

/* Info Alert */
.alert-info-enterprise {
    background-color: #f0f9ff;
    color: #075985;
    border-color: #bae6fd;
}

.alert-info-enterprise::before {
    background-color: #0ea5e9;
}

/* Dark Mode Alerts */
[data-theme="dark"] .alert-success-enterprise {
    background-color: rgba(20, 83, 45, 0.2);
    color: #86efac;
    border-color: #14532d;
}

[data-theme="dark"] .alert-warning-enterprise {
    background-color: rgba(120, 53, 15, 0.2);
    color: #fcd34d;
    border-color: #78350f;
}

[data-theme="dark"] .alert-danger-enterprise {
    background-color: rgba(127, 29, 29, 0.2);
    color: #fca5a5;
    border-color: #7f1d1d;
}

[data-theme="dark"] .alert-info-enterprise {
    background-color: rgba(7, 89, 133, 0.2);
    color: #7dd3fc;
    border-color: #075985;
}

/* Alert Icon */
.alert-icon {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
}

/* Alert Content */
.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: currentColor;
}

.alert-message {
    margin: 0;
    color: currentColor;
    opacity: 0.9;
}

/* Dismissible Alert */
.alert-dismissible {
    padding-right: 3rem;
}

.alert-close {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    background: none;
    border: none;
    color: currentColor;
    opacity: 0.6;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: opacity 0.2s ease-in-out;
}

.alert-close:hover {
    opacity: 1;
}

.alert-close:focus {
    outline: 2px solid currentColor;
    outline-offset: 2px;
}

/* Accessibility Enhancements */

/* Focus Management */
.focus-visible {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
    border-radius: 0.25rem;
}

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Skip Links */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--color-accent);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 0.25rem;
    z-index: 1000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .card-enterprise,
    .form-input-enterprise,
    .alert-enterprise {
        border-width: 2px;
    }

    .status-pill {
        border-width: 2px;
        font-weight: 700;
    }

    .priority-indicator {
        border-width: 2px;
        font-weight: 700;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .priority-indicator.priority-critical {
        animation: none;
    }
}

/* Dark Mode Preference Detection */
@media (prefers-color-scheme: dark) {
    :root:not([data-theme]) {
        --color-bg: #111827;
        --color-bg-secondary: #1f2937;
        --color-surface: #1e1e1e;
        --color-surface-elevated: #171717;
        --color-text-primary: #f9fafb;
        --color-text-secondary: #d1d5db;
        --color-text-muted: #9ca3af;
        --color-border: #374151;
        --color-border-light: #2d3748;
        --color-border-strong: #4b5563;
    }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .card-enterprise {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .card-header-enterprise {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .card-footer-enterprise {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .alert-enterprise {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }

    .form-input-enterprise {
        padding: 0.625rem 0.875rem;
        font-size: 1rem; /* Prevent zoom on iOS */
    }

    .status-pill {
        font-size: 0.7rem;
        padding: 0.2rem 0.6rem;
    }
}

@media (max-width: 480px) {
    .card-enterprise {
        padding: 0.75rem;
        border-radius: 0.5rem;
    }

    .alert-enterprise {
        padding: 0.625rem 0.875rem;
        gap: 0.5rem;
    }

    .priority-indicator {
        font-size: 0.75rem;
        padding: 0.2rem 0.4rem;
    }
}

/* Enhanced Loading States */
.loading-skeleton {
    background: linear-gradient(90deg,
        var(--color-bg-secondary) 25%,
        var(--color-border-light) 50%,
        var(--color-bg-secondary) 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
    border-radius: 0.375rem;
}

[data-theme="dark"] .loading-skeleton {
    background: linear-gradient(90deg,
        #374151 25%,
        #4b5563 50%,
        #374151 75%);
}

@keyframes loading-shimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.loading-skeleton.skeleton-text {
    height: 1em;
    margin-bottom: 0.5rem;
}

.loading-skeleton.skeleton-title {
    height: 1.5em;
    width: 60%;
    margin-bottom: 1rem;
}

.loading-skeleton.skeleton-button {
    height: 2.5em;
    width: 120px;
}

.loading-skeleton.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

/* Enhanced Empty States */
.empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    color: var(--color-text-secondary);
}

.empty-state-icon {
    font-size: 3rem;
    color: var(--color-text-muted);
    margin-bottom: 1.5rem;
    opacity: 0.6;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 0.75rem;
}

.empty-state-description {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Professional User Avatar */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--color-accent);
    color: white;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    border: 2px solid var(--color-surface);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease-in-out;
}

.user-avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.user-avatar.avatar-sm {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
    border-width: 1px;
}

.user-avatar.avatar-lg {
    width: 48px;
    height: 48px;
    font-size: 1.125rem;
    border-width: 3px;
}

/* Button Enhancements */
.btn-enterprise {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1;
    border-radius: 0.5rem;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-enterprise:focus {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
}

.btn-enterprise:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary-enterprise {
    background-color: var(--color-accent);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary-enterprise:hover:not(:disabled) {
    background-color: var(--color-accent-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-secondary-enterprise {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    border-color: var(--color-border);
    box-shadow: var(--shadow-sm);
}

.btn-secondary-enterprise:hover:not(:disabled) {
    background-color: var(--color-bg-secondary);
    border-color: var(--color-border-strong);
    transform: translateY(-1px);
}

/* Print Styles */
@media print {
    .card-enterprise {
        box-shadow: none;
        border: 1px solid #000;
        break-inside: avoid;
    }

    .alert-enterprise {
        border: 1px solid #000;
        box-shadow: none;
    }

    .btn-enterprise {
        border: 1px solid #000;
        box-shadow: none;
    }

    .loading-skeleton {
        display: none;
    }
}
