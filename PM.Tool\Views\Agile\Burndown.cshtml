@{
    ViewData["Title"] = "Burndown Charts";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
    var sprints = ViewBag.Sprints as IEnumerable<PM.Tool.Core.Entities.Agile.Sprint>;
    var selectedSprint = ViewBag.SelectedSprint as PM.Tool.Core.Entities.Agile.Sprint;
    var projectId = ViewBag.ProjectId as int? ?? 0;
}

<!-- Page Header Compact -->
<div class="page-header-compact">
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <i class="fas fa-chart-line text-purple-600 dark:text-purple-400"></i>
            </div>
            <div>
                <h1 class="text-xl font-bold text-neutral-900 dark:text-white">Burndown Charts</h1>
                <p class="text-sm text-neutral-500 dark:text-neutral-400">@project?.Name</p>
            </div>
        </div>
        <div class="flex items-center gap-2">
            <a href="@Url.Action("SprintPlanning", "Agile", new { projectId })" class="btn-compact btn-compact-secondary">
                <i class="fas fa-tasks"></i> Sprint Planning
            </a>
            <a href="@Url.Action("Kanban", "Agile", new { projectId })" class="btn-compact btn-compact-secondary">
                <i class="fas fa-columns"></i> Kanban Board
            </a>
            <button onclick="exportChart()" class="btn-compact btn-compact-secondary">
                <i class="fas fa-download"></i> Export
            </button>
        </div>
    </div>
</div>

<!-- Sprint Selection & Metrics -->
<div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4 mb-6">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-4">
            <div class="flex items-center gap-2">
                <i class="fas fa-rocket text-neutral-500 dark:text-neutral-400"></i>
                <span class="text-sm font-medium text-neutral-700 dark:text-neutral-300">Sprint:</span>
            </div>
            <select id="sprintSelector" class="form-select-compact" onchange="changeSprint()">
                <option value="">Select Sprint...</option>
                @if (sprints != null)
                {
                    @foreach (var sprint in sprints.OrderByDescending(s => s.StartDate))
                    {
                        <option value="@sprint.Id" selected="@(sprint.Id == selectedSprint?.Id)">
                            @sprint.Name (@sprint.Status)
                        </option>
                    }
                }
            </select>
        </div>
        
        @if (selectedSprint != null)
        {
            <div class="flex items-center gap-6 text-sm">
                <div class="text-center">
                    <div class="text-neutral-500 dark:text-neutral-400">Duration</div>
                    <div class="font-semibold text-neutral-900 dark:text-white">
                        @((selectedSprint.EndDate - selectedSprint.StartDate).Days) days
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-neutral-500 dark:text-neutral-400">Status</div>
                    <div class="font-semibold">
                        <span class="px-2 py-1 text-xs rounded-full
                            @switch (selectedSprint.Status)
                            {
                                case PM.Tool.Core.Entities.Agile.SprintStatus.Planning:
                                    @("bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400")
                                    break;
                                case PM.Tool.Core.Entities.Agile.SprintStatus.Active:
                                    @("bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400")
                                    break;
                                case PM.Tool.Core.Entities.Agile.SprintStatus.Completed:
                                    @("bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400")
                                    break;
                                case PM.Tool.Core.Entities.Agile.SprintStatus.Cancelled:
                                    @("bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400")
                                    break;
                                default:
                                    @("bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400")
                                    break;
                            }">
                            @selectedSprint.Status
                        </span>
                    </div>
                </div>
                <div class="text-center">
                    <div class="text-neutral-500 dark:text-neutral-400">Progress</div>
                    <div class="font-semibold text-neutral-900 dark:text-white" id="sprintProgress">
                        Loading...
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<!-- Charts Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Burndown Chart -->
    <div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg">
        <div class="p-4 border-b border-neutral-200 dark:border-neutral-700">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <i class="fas fa-chart-line text-purple-600 dark:text-purple-400"></i>
                    <h3 class="font-semibold text-neutral-900 dark:text-white">Sprint Burndown</h3>
                </div>
                <div class="flex items-center gap-2">
                    <button onclick="toggleIdealLine()" class="btn-compact btn-compact-secondary text-xs">
                        <i class="fas fa-eye"></i> Ideal
                    </button>
                    <button onclick="refreshBurndown()" class="btn-compact btn-compact-secondary text-xs">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="p-4">
            <div class="relative" style="height: 300px;">
                <canvas id="burndownChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Velocity Chart -->
    <div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg">
        <div class="p-4 border-b border-neutral-200 dark:border-neutral-700">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <i class="fas fa-tachometer-alt text-blue-600 dark:text-blue-400"></i>
                    <h3 class="font-semibold text-neutral-900 dark:text-white">Team Velocity</h3>
                </div>
                <button onclick="refreshVelocity()" class="btn-compact btn-compact-secondary text-xs">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
        <div class="p-4">
            <div class="relative" style="height: 300px;">
                <canvas id="velocityChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Cumulative Flow Diagram -->
<div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg">
    <div class="p-4 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
                <i class="fas fa-stream text-green-600 dark:text-green-400"></i>
                <h3 class="font-semibold text-neutral-900 dark:text-white">Cumulative Flow Diagram</h3>
            </div>
            <div class="flex items-center gap-2">
                <select id="cfdTimeRange" class="form-select-compact text-xs" onchange="updateCFD()">
                    <option value="30">Last 30 days</option>
                    <option value="60">Last 60 days</option>
                    <option value="90">Last 90 days</option>
                </select>
                <button onclick="refreshCFD()" class="btn-compact btn-compact-secondary text-xs">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="p-4">
        <div class="relative" style="height: 400px;">
            <canvas id="cfdChart" class="w-full h-full"></canvas>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let burndownChart, velocityChart, cfdChart;
        let currentSprintId = @(selectedSprint?.Id ?? 0);

        $(document).ready(function() {
            if (currentSprintId > 0) {
                loadAllCharts();
            }
        });

        function changeSprint() {
            const sprintId = document.getElementById('sprintSelector').value;
            if (sprintId) {
                window.location.href = '@Url.Action("Burndown", "Agile", new { projectId })' + '?sprintId=' + sprintId;
            }
        }

        function loadAllCharts() {
            loadBurndownChart();
            loadVelocityChart();
            loadCFDChart();
            updateSprintProgress();
        }

        function loadBurndownChart() {
            if (!currentSprintId) return;
            
            fetch(`@Url.Action("GetBurndownData", "Agile")?sprintId=${currentSprintId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Failed to load burndown data:', data.error);
                        return;
                    }
                    renderBurndownChart(data);
                })
                .catch(error => {
                    console.error('Failed to load burndown data:', error);
                });
        }

        function renderBurndownChart(data) {
            const ctx = document.getElementById('burndownChart').getContext('2d');
            
            if (burndownChart) {
                burndownChart.destroy();
            }

            burndownChart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: false
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Story Points'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Sprint Days'
                            }
                        }
                    }
                }
            });
        }

        function loadVelocityChart() {
            fetch(`@Url.Action("GetVelocityData", "Agile")?projectId=@projectId`)
                .then(response => response.json())
                .then(data => {
                    renderVelocityChart(data);
                })
                .catch(error => {
                    console.error('Failed to load velocity data:', error);
                });
        }

        function renderVelocityChart(data) {
            const ctx = document.getElementById('velocityChart').getContext('2d');
            
            if (velocityChart) {
                velocityChart.destroy();
            }

            velocityChart = new Chart(ctx, {
                type: 'bar',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Story Points'
                            }
                        }
                    }
                }
            });
        }

        function loadCFDChart() {
            const timeRange = document.getElementById('cfdTimeRange').value;
            
            fetch(`@Url.Action("GetCumulativeFlowData", "Agile")?projectId=@projectId&days=${timeRange}`)
                .then(response => response.json())
                .then(data => {
                    renderCFDChart(data);
                })
                .catch(error => {
                    console.error('Failed to load CFD data:', error);
                });
        }

        function renderCFDChart(data) {
            const ctx = document.getElementById('cfdChart').getContext('2d');
            
            if (cfdChart) {
                cfdChart.destroy();
            }

            cfdChart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            stacked: true,
                            title: {
                                display: true,
                                text: 'Story Count'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        }
                    },
                    elements: {
                        line: {
                            fill: true
                        }
                    }
                }
            });
        }

        function updateSprintProgress() {
            if (!currentSprintId) return;
            
            fetch(`@Url.Action("GetSprintMetrics", "Agile")?sprintId=${currentSprintId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('sprintProgress').textContent = 
                        `${data.completedStoryPoints || 0} / ${data.totalStoryPoints || 0} SP`;
                })
                .catch(error => {
                    console.error('Failed to load sprint progress:', error);
                    document.getElementById('sprintProgress').textContent = 'Error';
                });
        }

        // Chart control functions
        function toggleIdealLine() {
            if (burndownChart) {
                const idealDataset = burndownChart.data.datasets.find(d => d.label === 'Ideal Burndown');
                if (idealDataset) {
                    idealDataset.hidden = !idealDataset.hidden;
                    burndownChart.update();
                }
            }
        }

        function refreshBurndown() {
            loadBurndownChart();
        }

        function refreshVelocity() {
            loadVelocityChart();
        }

        function refreshCFD() {
            loadCFDChart();
        }

        function updateCFD() {
            loadCFDChart();
        }

        function exportChart() {
            // Export functionality - could export as PNG or PDF
            if (burndownChart) {
                const url = burndownChart.toBase64Image();
                const link = document.createElement('a');
                link.download = `burndown-chart-sprint-${currentSprintId}.png`;
                link.href = url;
                link.click();
            }
        }
    </script>
}

@section Styles {
    <style>
        .form-select-compact {
            font-size: 0.875rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            padding: 0.375rem 0.75rem;
            background-color: white;
            color: #111827;
        }

        .dark .form-select-compact {
            border-color: #4b5563;
            background-color: #1f2937;
            color: white;
        }

        .form-select-compact:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
        }
    </style>
}
